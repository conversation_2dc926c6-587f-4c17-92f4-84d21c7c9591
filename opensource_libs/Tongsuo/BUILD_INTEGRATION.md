# Tongsuo Build Integration

This document describes how to integrate Tongsuo crypto library into different build systems.

## Generated Files

The following files are auto-generated from the Tongsuo Makefile:

- `sources.mk` - Makefile source lists for Trusty build system
- `sources.bp` - Android Blueprint source lists  
- `sources.cmake` - CMake source lists
- `crypto-sources.mk` - Trusty-specific crypto sources include file

## Build Systems

### 1. Trusty Build System

For Trusty projects, use the main `rules.mk` file:

```makefile
# In your module's rules.mk
MODULE_LIBRARY_DEPS += \
    opensource_libs/Tongsuo \
```

The library provides:
- Symbol prefix isolation (`TONGSUO_` prefix) to avoid conflicts with BoringSSL
- TEE-optimized configuration with disabled features not needed in TEE environment
- SM2/SM3/SM4 algorithm support enabled

### 2. Host Tools Build

For host tools that need Tongsuo crypto:

```makefile
# Include the host build rules
include build/tools/tongsuo/rules.mk
```

### 3. Android Build (Blueprint)

Use the Android.bp file:

```bp
cc_binary {
    name: "my_app",
    srcs: ["my_app.c"],
    static_libs: ["libtongsuo"],
}
```

### 4. CMake Build

```cmake
find_package(Tongsuo REQUIRED)
target_link_libraries(my_target Tongsuo::tongsuo_crypto)
```

## Configuration

### Compile Flags

The library is configured with the following key flags:

- `TONGSUO_IMPLEMENTATION` - Tongsuo library implementation
- `SYMBOL_PREFIX=TONGSUO_` - Symbol prefix for isolation
- `OPENSSL_NO_STDIO` - Disable stdio operations
- `OPENSSL_NO_SOCK` - Disable socket operations  
- `OPENSSL_NO_THREADS` - Disable threading
- `D__TEE__` - TEE environment marker
- `OPENSSL_ENABLE_SM2/SM3/SM4` - Enable SM algorithms

### Disabled Features

The following OpenSSL features are disabled for TEE environment:

- Apps, async, autoload-config, deprecated
- DGRAM, DSO, DTLS, engine, FIPS
- Shared libraries, SSL/TLS, tests, UI console

## Regenerating Build Files

To regenerate the build files after Makefile changes:

```bash
cd opensource_libs/Tongsuo
python3 generate_sources_mk.py
```

This will update:
- sources.mk
- sources.bp  
- sources.cmake

## Integration Notes

1. **Symbol Conflicts**: The `TONGSUO_` prefix prevents conflicts with BoringSSL symbols
2. **Header Isolation**: Headers are exported from `include/openssl/` directory
3. **TEE Optimization**: Configuration optimized for TEE environment constraints
4. **SM Algorithms**: Chinese SM2/SM3/SM4 algorithms are enabled by default

## File Structure

```
opensource_libs/Tongsuo/
├── sources.mk              # Trusty makefile sources
├── sources.bp              # Android blueprint sources  
├── sources.cmake           # CMake sources
├── crypto-sources.mk       # Trusty crypto include file
├── rules.mk                # Main Trusty build rules
├── Android.bp              # Android build configuration
├── CMakeLists.txt          # CMake build configuration
├── generate_sources_mk.py  # Source file generator script
└── build_tongsuo_arm.sh    # ARM build script
```
