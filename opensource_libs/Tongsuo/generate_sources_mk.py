#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script to generate sources.mk from Tongsuo Makefile
Extracts source files and headers from the compiled Makefile
"""

import re
import os
import sys

def extract_sources_from_makefile(makefile_path):
    """Extract source files from Makefile"""
    sources = []
    asm_sources = []
    
    with open(makefile_path, 'r') as f:
        content = f.read()
    
    # Find the libcrypto.a target and extract all .o files
    # Pattern to match libcrypto-lib-*.o files
    pattern = r'crypto/[^/]+/libcrypto-lib-([^.]+)\.o'
    matches = re.findall(pattern, content)
    
    # Also find direct crypto files
    direct_pattern = r'crypto/libcrypto-lib-([^.]+)\.o'
    direct_matches = re.findall(direct_pattern, content)
    
    # Find provider files
    provider_pattern = r'providers/libcrypto-lib-([^.]+)\.o'
    provider_matches = re.findall(provider_pattern, content)
    
    # Extract source file mappings
    source_mappings = {}
    
    # Find lines that map .o files to .c files
    mapping_pattern = r'crypto/([^/]+)/libcrypto-lib-([^.]+)\.o:\s*crypto/([^/]+)/([^.\s]+)\.(c|S)'
    mapping_matches = re.findall(mapping_pattern, content)
    
    for match in mapping_matches:
        dir1, obj_name, dir2, src_name, ext = match
        if dir1 == dir2:  # Ensure directory consistency
            if ext == 'c':
                sources.append(f"crypto/{dir1}/{src_name}.c")
            elif ext == 'S':
                asm_sources.append(f"crypto/{dir1}/{src_name}.S")
    
    # Find direct crypto file mappings
    direct_mapping_pattern = r'crypto/libcrypto-lib-([^.]+)\.o:\s*crypto/([^.\s]+)\.(c|S)'
    direct_mapping_matches = re.findall(direct_mapping_pattern, content)
    
    for match in direct_mapping_matches:
        obj_name, src_name, ext = match
        if ext == 'c':
            sources.append(f"crypto/{src_name}.c")
        elif ext == 'S':
            asm_sources.append(f"crypto/{src_name}.S")
    
    # Find provider file mappings
    provider_mapping_pattern = r'providers/libcrypto-lib-([^.]+)\.o:\s*providers/([^.\s]+)\.c'
    provider_mapping_matches = re.findall(provider_mapping_pattern, content)
    
    for match in provider_mapping_matches:
        obj_name, src_name = match
        sources.append(f"providers/{src_name}.c")
    
    return sorted(list(set(sources))), sorted(list(set(asm_sources)))

def extract_headers():
    """Extract header files from include directory"""
    headers = []
    include_dir = "include/openssl"
    
    if os.path.exists(include_dir):
        for file in os.listdir(include_dir):
            if file.endswith('.h') and not file.endswith('.h.in'):
                headers.append(f"include/openssl/{file}")
    
    return sorted(headers)

def generate_sources_mk(sources, asm_sources, headers, output_path):
    """Generate sources.mk file"""
    
    with open(output_path, 'w') as f:
        f.write("""# Copyright (c) 2024, Tongsuo Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# This file contains source lists for Tongsuo crypto library.
# Generated from Tongsuo Makefile. Do not edit manually.

""")
        
        # Write C sources
        f.write("tongsuo_crypto_sources := \\\n")
        for i, source in enumerate(sources):
            if i == len(sources) - 1:
                f.write(f"  {source}\n\n")
            else:
                f.write(f"  {source}\\\n")
        
        # Write ASM sources
        if asm_sources:
            f.write("tongsuo_crypto_sources_asm := \\\n")
            for i, source in enumerate(asm_sources):
                if i == len(asm_sources) - 1:
                    f.write(f"  {source}\n\n")
                else:
                    f.write(f"  {source}\\\n")
        
        # Write headers
        f.write("tongsuo_crypto_headers := \\\n")
        for i, header in enumerate(headers):
            if i == len(headers) - 1:
                f.write(f"  {header}\n\n")
            else:
                f.write(f"  {header}\\\n")

def generate_sources_bp(sources, asm_sources, headers, output_path):
    """Generate sources.bp file for Android build"""

    with open(output_path, 'w') as f:
        f.write("""// Auto-generated from Tongsuo Makefile. Do not edit manually.

filegroup {
    name: "tongsuo_crypto_sources",
    srcs: [
""")

        for source in sources:
            f.write(f'        "{source}",\n')

        f.write("""    ],
}

filegroup {
    name: "tongsuo_crypto_sources_asm",
    srcs: [
""")

        for source in asm_sources:
            f.write(f'        "{source}",\n')

        f.write("""    ],
}

filegroup {
    name: "tongsuo_crypto_headers",
    srcs: [
""")

        for header in headers:
            f.write(f'        "{header}",\n')

        f.write("""    ],
}
""")

def generate_sources_cmake(sources, asm_sources, headers, output_path):
    """Generate sources.cmake file for CMake build"""

    with open(output_path, 'w') as f:
        f.write("""# Auto-generated from Tongsuo Makefile. Do not edit manually.

set(TONGSUO_CRYPTO_SOURCES
""")

        for source in sources:
            f.write(f'  {source}\n')

        f.write(""")

set(TONGSUO_CRYPTO_SOURCES_ASM
""")

        for source in asm_sources:
            f.write(f'  {source}\n')

        f.write(""")

set(TONGSUO_CRYPTO_HEADERS
""")

        for header in headers:
            f.write(f'  {header}\n')

        f.write(")\n")

def main():
    script_dir = os.path.dirname(os.path.abspath(__file__))
    makefile_path = os.path.join(script_dir, "Makefile")
    sources_mk_path = os.path.join(script_dir, "sources.mk")
    sources_bp_path = os.path.join(script_dir, "sources.bp")
    sources_cmake_path = os.path.join(script_dir, "sources.cmake")

    if not os.path.exists(makefile_path):
        print(f"Error: Makefile not found at {makefile_path}")
        sys.exit(1)

    print("Extracting sources from Makefile...")
    sources, asm_sources = extract_sources_from_makefile(makefile_path)

    print("Extracting headers...")
    headers = extract_headers()

    print(f"Found {len(sources)} C sources")
    print(f"Found {len(asm_sources)} ASM sources")
    print(f"Found {len(headers)} headers")

    print("Generating sources.mk...")
    generate_sources_mk(sources, asm_sources, headers, sources_mk_path)
    print(f"Generated {sources_mk_path}")

    print("Generating sources.bp...")
    generate_sources_bp(sources, asm_sources, headers, sources_bp_path)
    print(f"Generated {sources_bp_path}")

    print("Generating sources.cmake...")
    generate_sources_cmake(sources, asm_sources, headers, sources_cmake_path)
    print(f"Generated {sources_cmake_path}")

if __name__ == "__main__":
    main()
