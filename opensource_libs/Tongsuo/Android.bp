// Tongsuo crypto library for Android

package {
    default_visibility: ["//visibility:private"],
    default_applicable_licenses: ["external_tongsuo_license"],
}

license {
    name: "external_tongsuo_license",
    visibility: [":__subpackages__"],
    license_kinds: [
        "SPDX-license-identifier-Apache-2.0",
    ],
    license_text: [
        "LICENSE.txt",
    ],
}

// Pull in the autogenerated sources modules
build = ["sources.bp"]

// Used by libtongsuo crypto library
cc_defaults {
    name: "tongsuo_flags",

    cflags: [
        "-DTONGSUO_IMPLEMENTATION",
        "-DOPENSSL_NO_STDIO",
        "-DOPENSSL_NO_SOCK", 
        "-DOPENSSL_NO_THREADS",
        "-D__TEE__",
        "-Wno-unused-parameter",
        "-Wno-sign-compare",
        "-Wno-unused-function",
        "-DOPENSSL_NO_APPS",
        "-DOPENSSL_NO_ASYNC",
        "-DOPENSSL_NO_AUTOLOAD_CONFIG",
        "-<PERSON><PERSON><PERSON><PERSON>L_NO_DEPRECATED",
        "-<PERSON><PERSON><PERSON><PERSON><PERSON>_NO_DGRAM",
        "-DOPEN<PERSON>L_NO_DSO",
        "-DOPENSSL_NO_DTLS",
        "-DOPENSSL_NO_ENGINE",
        "-DOPENSSL_NO_FIPS",
        "-DOPENSSL_NO_SHARED",
        "-DOPENSSL_NO_SSL",
        "-DOPENSSL_NO_TESTS",
        "-DOPENSSL_NO_TLS",
        "-DOPENSSL_NO_UI_CONSOLE",
        "-DOPENSSL_ENABLE_SM2",
        "-DOPENSSL_ENABLE_SM3",
        "-DOPENSSL_ENABLE_SM4",
    ],

    local_include_dirs: ["include"],
    export_include_dirs: ["include"],

    // Symbol prefix to avoid conflicts with BoringSSL
    cflags: ["-DSYMBOL_PREFIX=TONGSUO_"],
}

cc_library {
    name: "libtongsuo",
    defaults: ["tongsuo_flags"],
    
    srcs: [
        ":tongsuo_crypto_sources",
        ":tongsuo_crypto_sources_asm",
    ],

    target: {
        android: {
            cflags: ["-DTONGSUO_ANDROID_SYSTEM"],
        },
    },

    visibility: ["//visibility:public"],
}

// Source file groups
filegroup {
    name: "tongsuo_crypto_sources",
    srcs: [
        // This will be populated by sources.bp
    ],
}

filegroup {
    name: "tongsuo_crypto_sources_asm", 
    srcs: [
        // This will be populated by sources.bp
    ],
}

filegroup {
    name: "tongsuo_crypto_headers",
    srcs: [
        // This will be populated by sources.bp
    ],
}
