cmake_minimum_required(VERSION 3.5)

project(Tongsuo
  VERSION 3.0.3
  LANGUAGES C ASM)

if(CMAKE_COMPILER_IS_GNUCC OR CMAKE_C_COMPILER_ID MATCHES "Clang")
  set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-unused-function")
endif()

# Include source lists
include(sources.cmake)

# Set up include directories
include_directories(include)
include_directories(crypto)

# Define compile definitions
add_definitions(
  -DTONGSUO_IMPLEMENTATION
  -DOPENSSL_NO_STDIO
  -DOPENSSL_NO_SOCK
  -DOPENSSL_NO_THREADS
  -D__TEE__
  -DOPENSSL_NO_APPS
  -DOPENSSL_NO_ASYNC
  -DOPENSSL_NO_AUTOLOAD_CONFIG
  -DOPENSSL_NO_DEPRECATED
  -DOPENSSL_NO_DGRAM
  -DOPENSSL_NO_DSO
  -DOPENSSL_NO_DTLS
  -DOPENSSL_NO_ENGINE
  -DOPENSSL_NO_FIPS
  -DOPENSSL_NO_SHARED
  -DOPENSSL_NO_SSL
  -DOPENSSL_NO_TESTS
  -DOPENSSL_NO_TLS
  -DOPENSSL_NO_UI_CONSOLE
  -DOPENSSL_ENABLE_SM2
  -DOPENSSL_ENABLE_SM3
  -DOPENSSL_ENABLE_SM4
)

# Symbol prefix to avoid conflicts with BoringSSL
add_definitions(-DSYMBOL_PREFIX=TONGSUO_)

# Create crypto library
add_library(tongsuo_crypto STATIC
  ${TONGSUO_CRYPTO_SOURCES}
  ${TONGSUO_CRYPTO_SOURCES_ASM}
)

target_include_directories(tongsuo_crypto PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>
)

# Set library properties
set_target_properties(tongsuo_crypto PROPERTIES
  OUTPUT_NAME tongsuo
  VERSION ${PROJECT_VERSION}
  SOVERSION ${PROJECT_VERSION_MAJOR}
)

# Install targets
install(TARGETS tongsuo_crypto
  EXPORT TongsuoTargets
  LIBRARY DESTINATION lib
  ARCHIVE DESTINATION lib
  RUNTIME DESTINATION bin
)

# Install headers
install(DIRECTORY include/openssl
  DESTINATION include
  FILES_MATCHING PATTERN "*.h"
)

# Export configuration
install(EXPORT TongsuoTargets
  FILE TongsuoTargets.cmake
  NAMESPACE Tongsuo::
  DESTINATION lib/cmake/Tongsuo
)

# Create config file
include(CMakePackageConfigHelpers)
write_basic_package_version_file(
  TongsuoConfigVersion.cmake
  VERSION ${PROJECT_VERSION}
  COMPATIBILITY SameMajorVersion
)

configure_package_config_file(
  ${CMAKE_CURRENT_SOURCE_DIR}/TongsuoConfig.cmake.in
  ${CMAKE_CURRENT_BINARY_DIR}/TongsuoConfig.cmake
  INSTALL_DESTINATION lib/cmake/Tongsuo
)

install(FILES
  ${CMAKE_CURRENT_BINARY_DIR}/TongsuoConfig.cmake
  ${CMAKE_CURRENT_BINARY_DIR}/TongsuoConfigVersion.cmake
  DESTINATION lib/cmake/Tongsuo
)
