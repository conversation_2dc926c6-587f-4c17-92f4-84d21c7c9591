# Copyright (c) 2024, Tongsuo Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# This file contains source lists for Tongsuo crypto library.
# Generated from Tongsuo Makefile. Do not edit manually.

tongsuo_crypto_sources := \
  crypto/aes/aes_cbc.c\
  crypto/aes/aes_cfb.c\
  crypto/aes/aes_core.c\
  crypto/aes/aes_ecb.c\
  crypto/aes/aes_misc.c\
  crypto/aes/aes_ofb.c\
  crypto/aes/aes_wrap.c\
  crypto/armcap.c\
  crypto/asn1/a_bitstr.c\
  crypto/asn1/a_d2i_fp.c\
  crypto/asn1/a_digest.c\
  crypto/asn1/a_dup.c\
  crypto/asn1/a_gentm.c\
  crypto/asn1/a_i2d_fp.c\
  crypto/asn1/a_int.c\
  crypto/asn1/a_mbstr.c\
  crypto/asn1/a_object.c\
  crypto/asn1/a_octet.c\
  crypto/asn1/a_print.c\
  crypto/asn1/a_sign.c\
  crypto/asn1/a_strex.c\
  crypto/asn1/a_strnid.c\
  crypto/asn1/a_time.c\
  crypto/asn1/a_type.c\
  crypto/asn1/a_utctm.c\
  crypto/asn1/a_utf8.c\
  crypto/asn1/a_verify.c\
  crypto/asn1/ameth_lib.c\
  crypto/asn1/asn1_err.c\
  crypto/asn1/asn1_gen.c\
  crypto/asn1/asn1_item_list.c\
  crypto/asn1/asn1_lib.c\
  crypto/asn1/asn1_parse.c\
  crypto/asn1/asn_mime.c\
  crypto/asn1/asn_moid.c\
  crypto/asn1/asn_mstbl.c\
  crypto/asn1/asn_pack.c\
  crypto/asn1/bio_asn1.c\
  crypto/asn1/bio_ndef.c\
  crypto/asn1/d2i_param.c\
  crypto/asn1/d2i_pr.c\
  crypto/asn1/d2i_pu.c\
  crypto/asn1/evp_asn1.c\
  crypto/asn1/f_int.c\
  crypto/asn1/f_string.c\
  crypto/asn1/i2d_evp.c\
  crypto/asn1/n_pkey.c\
  crypto/asn1/nsseq.c\
  crypto/asn1/p5_pbe.c\
  crypto/asn1/p5_pbev2.c\
  crypto/asn1/p5_scrypt.c\
  crypto/asn1/p8_pkey.c\
  crypto/asn1/t_bitst.c\
  crypto/asn1/t_pkey.c\
  crypto/asn1/t_spki.c\
  crypto/asn1/tasn_dec.c\
  crypto/asn1/tasn_enc.c\
  crypto/asn1/tasn_fre.c\
  crypto/asn1/tasn_new.c\
  crypto/asn1/tasn_prn.c\
  crypto/asn1/tasn_scn.c\
  crypto/asn1/tasn_typ.c\
  crypto/asn1/tasn_utl.c\
  crypto/asn1/x_algor.c\
  crypto/asn1/x_bignum.c\
  crypto/asn1/x_info.c\
  crypto/asn1/x_int64.c\
  crypto/asn1/x_pkey.c\
  crypto/asn1/x_sig.c\
  crypto/asn1/x_spki.c\
  crypto/asn1/x_val.c\
  crypto/asn1_dsa.c\
  crypto/async/async.c\
  crypto/async/async_err.c\
  crypto/async/async_wait.c\
  crypto/bio/bf_buff.c\
  crypto/bio/bf_lbuf.c\
  crypto/bio/bf_nbio.c\
  crypto/bio/bf_null.c\
  crypto/bio/bf_prefix.c\
  crypto/bio/bf_readbuff.c\
  crypto/bio/bio_addr.c\
  crypto/bio/bio_cb.c\
  crypto/bio/bio_dump.c\
  crypto/bio/bio_err.c\
  crypto/bio/bio_lib.c\
  crypto/bio/bio_meth.c\
  crypto/bio/bio_print.c\
  crypto/bio/bio_sock.c\
  crypto/bio/bio_sock2.c\
  crypto/bio/bss_acpt.c\
  crypto/bio/bss_bio.c\
  crypto/bio/bss_conn.c\
  crypto/bio/bss_core.c\
  crypto/bio/bss_dgram.c\
  crypto/bio/bss_fd.c\
  crypto/bio/bss_file.c\
  crypto/bio/bss_log.c\
  crypto/bio/bss_mem.c\
  crypto/bio/bss_null.c\
  crypto/bio/bss_sock.c\
  crypto/bio/ossl_core_bio.c\
  crypto/bn/bn_add.c\
  crypto/bn/bn_asm.c\
  crypto/bn/bn_blind.c\
  crypto/bn/bn_const.c\
  crypto/bn/bn_conv.c\
  crypto/bn/bn_ctx.c\
  crypto/bn/bn_dh.c\
  crypto/bn/bn_div.c\
  crypto/bn/bn_err.c\
  crypto/bn/bn_exp.c\
  crypto/bn/bn_exp2.c\
  crypto/bn/bn_gcd.c\
  crypto/bn/bn_gf2m.c\
  crypto/bn/bn_intern.c\
  crypto/bn/bn_kron.c\
  crypto/bn/bn_lib.c\
  crypto/bn/bn_mod.c\
  crypto/bn/bn_mont.c\
  crypto/bn/bn_mpi.c\
  crypto/bn/bn_mul.c\
  crypto/bn/bn_nist.c\
  crypto/bn/bn_prime.c\
  crypto/bn/bn_print.c\
  crypto/bn/bn_rand.c\
  crypto/bn/bn_recp.c\
  crypto/bn/bn_rsa_fips186_4.c\
  crypto/bn/bn_shift.c\
  crypto/bn/bn_sm2.c\
  crypto/bn/bn_sqr.c\
  crypto/bn/bn_sqrt.c\
  crypto/bn/bn_srp.c\
  crypto/bn/bn_word.c\
  crypto/bsearch.c\
  crypto/buffer/buf_err.c\
  crypto/buffer/buffer.c\
  crypto/cmac/cmac.c\
  crypto/cmp/cmp_asn.c\
  crypto/cmp/cmp_client.c\
  crypto/cmp/cmp_ctx.c\
  crypto/cmp/cmp_err.c\
  crypto/cmp/cmp_hdr.c\
  crypto/cmp/cmp_http.c\
  crypto/cmp/cmp_msg.c\
  crypto/cmp/cmp_protect.c\
  crypto/cmp/cmp_server.c\
  crypto/cmp/cmp_status.c\
  crypto/cmp/cmp_util.c\
  crypto/cmp/cmp_vfy.c\
  crypto/cms/cms_asn1.c\
  crypto/cms/cms_att.c\
  crypto/cms/cms_cd.c\
  crypto/cms/cms_dd.c\
  crypto/cms/cms_dh.c\
  crypto/cms/cms_ec.c\
  crypto/cms/cms_enc.c\
  crypto/cms/cms_env.c\
  crypto/cms/cms_err.c\
  crypto/cms/cms_ess.c\
  crypto/cms/cms_io.c\
  crypto/cms/cms_kari.c\
  crypto/cms/cms_lib.c\
  crypto/cms/cms_pwri.c\
  crypto/cms/cms_rsa.c\
  crypto/cms/cms_sd.c\
  crypto/cms/cms_smime.c\
  crypto/comp/c_zlib.c\
  crypto/comp/comp_err.c\
  crypto/comp/comp_lib.c\
  crypto/conf/conf_api.c\
  crypto/conf/conf_def.c\
  crypto/conf/conf_err.c\
  crypto/conf/conf_lib.c\
  crypto/conf/conf_mall.c\
  crypto/conf/conf_mod.c\
  crypto/conf/conf_sap.c\
  crypto/conf/conf_ssl.c\
  crypto/context.c\
  crypto/core_algorithm.c\
  crypto/core_fetch.c\
  crypto/core_namemap.c\
  crypto/cpt_err.c\
  crypto/cpuid.c\
  crypto/crmf/crmf_asn.c\
  crypto/crmf/crmf_err.c\
  crypto/crmf/crmf_lib.c\
  crypto/crmf/crmf_pbm.c\
  crypto/cryptlib.c\
  crypto/ct/ct_b64.c\
  crypto/ct/ct_err.c\
  crypto/ct/ct_log.c\
  crypto/ct/ct_oct.c\
  crypto/ct/ct_policy.c\
  crypto/ct/ct_prn.c\
  crypto/ct/ct_sct.c\
  crypto/ct/ct_sct_ctx.c\
  crypto/ct/ct_vfy.c\
  crypto/ct/ct_x509v3.c\
  crypto/ctype.c\
  crypto/cversion.c\
  crypto/der_writer.c\
  crypto/des/cbc_cksm.c\
  crypto/des/cbc_enc.c\
  crypto/des/cfb64ede.c\
  crypto/des/cfb64enc.c\
  crypto/des/cfb_enc.c\
  crypto/des/des_enc.c\
  crypto/des/ecb3_enc.c\
  crypto/des/ecb_enc.c\
  crypto/des/fcrypt.c\
  crypto/des/fcrypt_b.c\
  crypto/des/ofb64ede.c\
  crypto/des/ofb64enc.c\
  crypto/des/ofb_enc.c\
  crypto/des/pcbc_enc.c\
  crypto/des/qud_cksm.c\
  crypto/des/rand_key.c\
  crypto/des/set_key.c\
  crypto/des/str2key.c\
  crypto/des/xcbc_enc.c\
  crypto/dh/dh_ameth.c\
  crypto/dh/dh_asn1.c\
  crypto/dh/dh_backend.c\
  crypto/dh/dh_check.c\
  crypto/dh/dh_err.c\
  crypto/dh/dh_gen.c\
  crypto/dh/dh_group_params.c\
  crypto/dh/dh_kdf.c\
  crypto/dh/dh_key.c\
  crypto/dh/dh_lib.c\
  crypto/dh/dh_meth.c\
  crypto/dh/dh_pmeth.c\
  crypto/dh/dh_prn.c\
  crypto/dh/dh_rfc5114.c\
  crypto/dsa/dsa_ameth.c\
  crypto/dsa/dsa_asn1.c\
  crypto/dsa/dsa_backend.c\
  crypto/dsa/dsa_check.c\
  crypto/dsa/dsa_err.c\
  crypto/dsa/dsa_gen.c\
  crypto/dsa/dsa_key.c\
  crypto/dsa/dsa_lib.c\
  crypto/dsa/dsa_meth.c\
  crypto/dsa/dsa_ossl.c\
  crypto/dsa/dsa_pmeth.c\
  crypto/dsa/dsa_prn.c\
  crypto/dsa/dsa_sign.c\
  crypto/dsa/dsa_vrf.c\
  crypto/dso/dso_dl.c\
  crypto/dso/dso_dlfcn.c\
  crypto/dso/dso_err.c\
  crypto/dso/dso_lib.c\
  crypto/dso/dso_openssl.c\
  crypto/dso/dso_win32.c\
  crypto/ebcdic.c\
  crypto/ec/curve25519.c\
  crypto/ec/ec2_oct.c\
  crypto/ec/ec2_smpl.c\
  crypto/ec/ec_ameth.c\
  crypto/ec/ec_asn1.c\
  crypto/ec/ec_backend.c\
  crypto/ec/ec_check.c\
  crypto/ec/ec_curve.c\
  crypto/ec/ec_cvt.c\
  crypto/ec/ec_deprecated.c\
  crypto/ec/ec_err.c\
  crypto/ec/ec_key.c\
  crypto/ec/ec_kmeth.c\
  crypto/ec/ec_lib.c\
  crypto/ec/ec_mult.c\
  crypto/ec/ec_oct.c\
  crypto/ec/ec_pmeth.c\
  crypto/ec/ec_print.c\
  crypto/ec/ecdh_kdf.c\
  crypto/ec/ecdh_ossl.c\
  crypto/ec/ecdsa_ossl.c\
  crypto/ec/ecdsa_sign.c\
  crypto/ec/ecdsa_vrf.c\
  crypto/ec/eck_prn.c\
  crypto/ec/ecp_meth.c\
  crypto/ec/ecp_mont.c\
  crypto/ec/ecp_nist.c\
  crypto/ec/ecp_nistz256.c\
  crypto/ec/ecp_oct.c\
  crypto/ec/ecp_smpl.c\
  crypto/ec/ecx_backend.c\
  crypto/ec/ecx_key.c\
  crypto/ec/ecx_meth.c\
  crypto/eia3/eia3.c\
  crypto/encode_decode/decoder_err.c\
  crypto/encode_decode/decoder_lib.c\
  crypto/encode_decode/decoder_meth.c\
  crypto/encode_decode/decoder_pkey.c\
  crypto/encode_decode/encoder_err.c\
  crypto/encode_decode/encoder_lib.c\
  crypto/encode_decode/encoder_meth.c\
  crypto/encode_decode/encoder_pkey.c\
  crypto/err/err.c\
  crypto/err/err_all.c\
  crypto/err/err_all_legacy.c\
  crypto/err/err_blocks.c\
  crypto/err/err_prn.c\
  crypto/ess/ess_asn1.c\
  crypto/ess/ess_err.c\
  crypto/ess/ess_lib.c\
  crypto/evp/asymcipher.c\
  crypto/evp/bio_b64.c\
  crypto/evp/bio_enc.c\
  crypto/evp/bio_md.c\
  crypto/evp/bio_ok.c\
  crypto/evp/c_allc.c\
  crypto/evp/c_alld.c\
  crypto/evp/cmeth_lib.c\
  crypto/evp/ctrl_params_translate.c\
  crypto/evp/dh_ctrl.c\
  crypto/evp/dh_support.c\
  crypto/evp/digest.c\
  crypto/evp/dsa_ctrl.c\
  crypto/evp/e_aes.c\
  crypto/evp/e_aes_cbc_hmac_sha1.c\
  crypto/evp/e_aes_cbc_hmac_sha256.c\
  crypto/evp/e_chacha20_poly1305.c\
  crypto/evp/e_des.c\
  crypto/evp/e_des3.c\
  crypto/evp/e_eea3.c\
  crypto/evp/e_null.c\
  crypto/evp/e_rc4.c\
  crypto/evp/e_rc4_hmac_md5.c\
  crypto/evp/e_rc5.c\
  crypto/evp/e_sm4.c\
  crypto/evp/e_wbsm4_baiwu.c\
  crypto/evp/e_wbsm4_wsise.c\
  crypto/evp/e_wbsm4_xiaolai.c\
  crypto/evp/e_xcbc_d.c\
  crypto/evp/ec_ctrl.c\
  crypto/evp/ec_support.c\
  crypto/evp/encode.c\
  crypto/evp/evp_cnf.c\
  crypto/evp/evp_enc.c\
  crypto/evp/evp_err.c\
  crypto/evp/evp_fetch.c\
  crypto/evp/evp_key.c\
  crypto/evp/evp_lib.c\
  crypto/evp/evp_pbe.c\
  crypto/evp/evp_pkey.c\
  crypto/evp/evp_rand.c\
  crypto/evp/evp_utils.c\
  crypto/evp/exchange.c\
  crypto/evp/kdf_lib.c\
  crypto/evp/kdf_meth.c\
  crypto/evp/kem.c\
  crypto/evp/keymgmt_lib.c\
  crypto/evp/keymgmt_meth.c\
  crypto/evp/legacy_md5.c\
  crypto/evp/legacy_md5_sha1.c\
  crypto/evp/legacy_sha.c\
  crypto/evp/m_null.c\
  crypto/evp/m_sigver.c\
  crypto/evp/mac_lib.c\
  crypto/evp/mac_meth.c\
  crypto/evp/names.c\
  crypto/evp/p5_crpt.c\
  crypto/evp/p5_crpt2.c\
  crypto/evp/p_legacy.c\
  crypto/evp/p_lib.c\
  crypto/evp/p_open.c\
  crypto/evp/p_seal.c\
  crypto/evp/p_sign.c\
  crypto/evp/p_verify.c\
  crypto/evp/pbe_scrypt.c\
  crypto/evp/pmeth_check.c\
  crypto/evp/pmeth_gn.c\
  crypto/evp/pmeth_lib.c\
  crypto/evp/signature.c\
  crypto/ex_data.c\
  crypto/ffc/ffc_backend.c\
  crypto/ffc/ffc_dh.c\
  crypto/ffc/ffc_key_generate.c\
  crypto/ffc/ffc_key_validate.c\
  crypto/ffc/ffc_params.c\
  crypto/ffc/ffc_params_generate.c\
  crypto/ffc/ffc_params_validate.c\
  crypto/getenv.c\
  crypto/hmac/hmac.c\
  crypto/http/http_client.c\
  crypto/http/http_err.c\
  crypto/http/http_lib.c\
  crypto/info.c\
  crypto/init.c\
  crypto/initthread.c\
  crypto/kdf/kdf_err.c\
  crypto/lhash/lh_stats.c\
  crypto/lhash/lhash.c\
  crypto/md5/md5_dgst.c\
  crypto/md5/md5_one.c\
  crypto/md5/md5_sha1.c\
  crypto/mem.c\
  crypto/mem_sec.c\
  crypto/modes/cbc128.c\
  crypto/modes/ccm128.c\
  crypto/modes/cfb128.c\
  crypto/modes/ctr128.c\
  crypto/modes/cts128.c\
  crypto/modes/gcm128.c\
  crypto/modes/ocb128.c\
  crypto/modes/ofb128.c\
  crypto/modes/siv128.c\
  crypto/modes/wrap128.c\
  crypto/modes/xts128.c\
  crypto/o_dir.c\
  crypto/o_fopen.c\
  crypto/o_init.c\
  crypto/o_str.c\
  crypto/o_syslog.c\
  crypto/o_time.c\
  crypto/objects/o_names.c\
  crypto/objects/obj_dat.c\
  crypto/objects/obj_err.c\
  crypto/objects/obj_lib.c\
  crypto/objects/obj_xref.c\
  crypto/ocsp/ocsp_asn.c\
  crypto/ocsp/ocsp_cl.c\
  crypto/ocsp/ocsp_err.c\
  crypto/ocsp/ocsp_ext.c\
  crypto/ocsp/ocsp_http.c\
  crypto/ocsp/ocsp_lib.c\
  crypto/ocsp/ocsp_prn.c\
  crypto/ocsp/ocsp_srv.c\
  crypto/ocsp/ocsp_vfy.c\
  crypto/ocsp/v3_ocsp.c\
  crypto/packet.c\
  crypto/param_build.c\
  crypto/param_build_set.c\
  crypto/params.c\
  crypto/params_dup.c\
  crypto/params_from_text.c\
  crypto/passphrase.c\
  crypto/pem/pem_all.c\
  crypto/pem/pem_err.c\
  crypto/pem/pem_info.c\
  crypto/pem/pem_lib.c\
  crypto/pem/pem_oth.c\
  crypto/pem/pem_pk8.c\
  crypto/pem/pem_pkey.c\
  crypto/pem/pem_sign.c\
  crypto/pem/pem_x509.c\
  crypto/pem/pem_xaux.c\
  crypto/pem/pvkfmt.c\
  crypto/pkcs12/p12_add.c\
  crypto/pkcs12/p12_asn.c\
  crypto/pkcs12/p12_attr.c\
  crypto/pkcs12/p12_crpt.c\
  crypto/pkcs12/p12_crt.c\
  crypto/pkcs12/p12_decr.c\
  crypto/pkcs12/p12_init.c\
  crypto/pkcs12/p12_key.c\
  crypto/pkcs12/p12_kiss.c\
  crypto/pkcs12/p12_mutl.c\
  crypto/pkcs12/p12_npas.c\
  crypto/pkcs12/p12_p8d.c\
  crypto/pkcs12/p12_p8e.c\
  crypto/pkcs12/p12_sbag.c\
  crypto/pkcs12/p12_utl.c\
  crypto/pkcs12/pk12err.c\
  crypto/pkcs7/bio_pk7.c\
  crypto/pkcs7/pk7_asn1.c\
  crypto/pkcs7/pk7_attr.c\
  crypto/pkcs7/pk7_doit.c\
  crypto/pkcs7/pk7_lib.c\
  crypto/pkcs7/pk7_mime.c\
  crypto/pkcs7/pk7_smime.c\
  crypto/pkcs7/pkcs7err.c\
  crypto/poly1305/poly1305.c\
  crypto/property/defn_cache.c\
  crypto/property/property.c\
  crypto/property/property_err.c\
  crypto/property/property_parse.c\
  crypto/property/property_query.c\
  crypto/property/property_string.c\
  crypto/provider.c\
  crypto/provider_child.c\
  crypto/provider_conf.c\
  crypto/provider_core.c\
  crypto/provider_predefined.c\
  crypto/punycode.c\
  crypto/rand/prov_seed.c\
  crypto/rand/rand_deprecated.c\
  crypto/rand/rand_err.c\
  crypto/rand/rand_lib.c\
  crypto/rand/rand_pool.c\
  crypto/rand/randfile.c\
  crypto/rc4/rc4_enc.c\
  crypto/rc4/rc4_skey.c\
  crypto/rsa/rsa_ameth.c\
  crypto/rsa/rsa_asn1.c\
  crypto/rsa/rsa_backend.c\
  crypto/rsa/rsa_chk.c\
  crypto/rsa/rsa_crpt.c\
  crypto/rsa/rsa_err.c\
  crypto/rsa/rsa_gen.c\
  crypto/rsa/rsa_lib.c\
  crypto/rsa/rsa_meth.c\
  crypto/rsa/rsa_mp.c\
  crypto/rsa/rsa_mp_names.c\
  crypto/rsa/rsa_none.c\
  crypto/rsa/rsa_oaep.c\
  crypto/rsa/rsa_ossl.c\
  crypto/rsa/rsa_pk1.c\
  crypto/rsa/rsa_pmeth.c\
  crypto/rsa/rsa_prn.c\
  crypto/rsa/rsa_pss.c\
  crypto/rsa/rsa_saos.c\
  crypto/rsa/rsa_schemes.c\
  crypto/rsa/rsa_sign.c\
  crypto/rsa/rsa_sp800_56b_check.c\
  crypto/rsa/rsa_sp800_56b_gen.c\
  crypto/rsa/rsa_x931.c\
  crypto/sdf/sdf_lib.c\
  crypto/sdf/sdf_meth.c\
  crypto/self_test_core.c\
  crypto/sha/sha1_one.c\
  crypto/sha/sha1dgst.c\
  crypto/sha/sha256.c\
  crypto/sha/sha3.c\
  crypto/sha/sha512.c\
  crypto/siphash/siphash.c\
  crypto/sm2/sm2_crypt.c\
  crypto/sm2/sm2_err.c\
  crypto/sm2/sm2_key.c\
  crypto/sm2/sm2_kmeth.c\
  crypto/sm2/sm2_sign.c\
  crypto/sm3/legacy_sm3.c\
  crypto/sm3/sm3.c\
  crypto/sm4/sm4.c\
  crypto/sparse_array.c\
  crypto/stack/stack.c\
  crypto/store/store_err.c\
  crypto/store/store_lib.c\
  crypto/store/store_meth.c\
  crypto/store/store_result.c\
  crypto/store/store_strings.c\
  crypto/threads_lib.c\
  crypto/threads_none.c\
  crypto/threads_pthread.c\
  crypto/threads_win.c\
  crypto/trace.c\
  crypto/ts/ts_asn1.c\
  crypto/ts/ts_conf.c\
  crypto/ts/ts_err.c\
  crypto/ts/ts_lib.c\
  crypto/ts/ts_req_print.c\
  crypto/ts/ts_req_utils.c\
  crypto/ts/ts_rsp_print.c\
  crypto/ts/ts_rsp_sign.c\
  crypto/ts/ts_rsp_utils.c\
  crypto/ts/ts_rsp_verify.c\
  crypto/ts/ts_verify_ctx.c\
  crypto/tsapi/tsapi_lib.c\
  crypto/txt_db/txt_db.c\
  crypto/ui/ui_err.c\
  crypto/ui/ui_lib.c\
  crypto/ui/ui_null.c\
  crypto/ui/ui_openssl.c\
  crypto/ui/ui_util.c\
  crypto/uid.c\
  crypto/x509/by_dir.c\
  crypto/x509/by_file.c\
  crypto/x509/by_store.c\
  crypto/x509/pcy_cache.c\
  crypto/x509/pcy_data.c\
  crypto/x509/pcy_lib.c\
  crypto/x509/pcy_map.c\
  crypto/x509/pcy_node.c\
  crypto/x509/pcy_tree.c\
  crypto/x509/t_crl.c\
  crypto/x509/t_req.c\
  crypto/x509/t_x509.c\
  crypto/x509/v3_addr.c\
  crypto/x509/v3_admis.c\
  crypto/x509/v3_akeya.c\
  crypto/x509/v3_akid.c\
  crypto/x509/v3_asid.c\
  crypto/x509/v3_bcons.c\
  crypto/x509/v3_bitst.c\
  crypto/x509/v3_conf.c\
  crypto/x509/v3_cpols.c\
  crypto/x509/v3_crld.c\
  crypto/x509/v3_enum.c\
  crypto/x509/v3_extku.c\
  crypto/x509/v3_genn.c\
  crypto/x509/v3_ia5.c\
  crypto/x509/v3_info.c\
  crypto/x509/v3_int.c\
  crypto/x509/v3_lib.c\
  crypto/x509/v3_ncons.c\
  crypto/x509/v3_pci.c\
  crypto/x509/v3_pcia.c\
  crypto/x509/v3_pcons.c\
  crypto/x509/v3_pku.c\
  crypto/x509/v3_pmaps.c\
  crypto/x509/v3_prn.c\
  crypto/x509/v3_purp.c\
  crypto/x509/v3_san.c\
  crypto/x509/v3_skid.c\
  crypto/x509/v3_sxnet.c\
  crypto/x509/v3_tlsf.c\
  crypto/x509/v3_utl.c\
  crypto/x509/v3err.c\
  crypto/x509/x509_att.c\
  crypto/x509/x509_cmp.c\
  crypto/x509/x509_d2.c\
  crypto/x509/x509_def.c\
  crypto/x509/x509_err.c\
  crypto/x509/x509_ext.c\
  crypto/x509/x509_lu.c\
  crypto/x509/x509_meth.c\
  crypto/x509/x509_obj.c\
  crypto/x509/x509_r2x.c\
  crypto/x509/x509_req.c\
  crypto/x509/x509_set.c\
  crypto/x509/x509_trust.c\
  crypto/x509/x509_txt.c\
  crypto/x509/x509_v3.c\
  crypto/x509/x509_vfy.c\
  crypto/x509/x509_vpm.c\
  crypto/x509/x509cset.c\
  crypto/x509/x509name.c\
  crypto/x509/x509rset.c\
  crypto/x509/x509spki.c\
  crypto/x509/x_all.c\
  crypto/x509/x_attrib.c\
  crypto/x509/x_crl.c\
  crypto/x509/x_exten.c\
  crypto/x509/x_name.c\
  crypto/x509/x_pubkey.c\
  crypto/x509/x_req.c\
  crypto/x509/x_x509.c\
  crypto/x509/x_x509a.c\
  crypto/zuc/zuc.c\
  providers/baseprov.c\
  providers/defltprov.c\
  providers/legacyprov.c\
  providers/nullprov.c\
  providers/prov_running.c

tongsuo_crypto_sources_asm := \
  crypto/aes/aesv8-armx.S\
  crypto/aes/vpaes-armv8.S\
  crypto/arm64cpuid.S\
  crypto/bn/armv8-mont.S\
  crypto/chacha/chacha-armv8.S\
  crypto/ec/ecp_nistz256-armv8.S\
  crypto/modes/aes-gcm-armv8_64.S\
  crypto/modes/ghashv8-armx.S\
  crypto/poly1305/poly1305-armv8.S\
  crypto/sha/keccak1600-armv8.S\
  crypto/sha/sha1-armv8.S\
  crypto/sha/sha256-armv8.S\
  crypto/sha/sha512-armv8.S\
  crypto/sm3/sm3-armv8.S\
  crypto/sm4/sm4-armv8.S

tongsuo_crypto_headers := \
  include/openssl/aes.h\
  include/openssl/asn1.h\
  include/openssl/asn1_mac.h\
  include/openssl/asn1err.h\
  include/openssl/asn1t.h\
  include/openssl/async.h\
  include/openssl/asyncerr.h\
  include/openssl/bio.h\
  include/openssl/bioerr.h\
  include/openssl/bn.h\
  include/openssl/bnerr.h\
  include/openssl/buffer.h\
  include/openssl/buffererr.h\
  include/openssl/bulletproofs.h\
  include/openssl/cmac.h\
  include/openssl/cmp.h\
  include/openssl/cmp_util.h\
  include/openssl/cmperr.h\
  include/openssl/cms.h\
  include/openssl/cmserr.h\
  include/openssl/comp.h\
  include/openssl/comperr.h\
  include/openssl/conf.h\
  include/openssl/conf_api.h\
  include/openssl/conferr.h\
  include/openssl/configuration.h\
  include/openssl/conftypes.h\
  include/openssl/core.h\
  include/openssl/core_dispatch.h\
  include/openssl/core_names.h\
  include/openssl/core_object.h\
  include/openssl/crmf.h\
  include/openssl/crmferr.h\
  include/openssl/crypto.h\
  include/openssl/cryptoerr.h\
  include/openssl/cryptoerr_legacy.h\
  include/openssl/ct.h\
  include/openssl/cterr.h\
  include/openssl/decoder.h\
  include/openssl/decodererr.h\
  include/openssl/des.h\
  include/openssl/dh.h\
  include/openssl/dherr.h\
  include/openssl/dsa.h\
  include/openssl/dsaerr.h\
  include/openssl/dtls1.h\
  include/openssl/e_os2.h\
  include/openssl/ebcdic.h\
  include/openssl/ec.h\
  include/openssl/ecdh.h\
  include/openssl/ecdsa.h\
  include/openssl/ecerr.h\
  include/openssl/encoder.h\
  include/openssl/encodererr.h\
  include/openssl/engine.h\
  include/openssl/engineerr.h\
  include/openssl/err.h\
  include/openssl/ess.h\
  include/openssl/esserr.h\
  include/openssl/evp.h\
  include/openssl/evperr.h\
  include/openssl/fips_names.h\
  include/openssl/fipskey.h\
  include/openssl/hmac.h\
  include/openssl/http.h\
  include/openssl/httperr.h\
  include/openssl/kdf.h\
  include/openssl/kdferr.h\
  include/openssl/lhash.h\
  include/openssl/macros.h\
  include/openssl/md5.h\
  include/openssl/modes.h\
  include/openssl/nizk.h\
  include/openssl/ntls.h\
  include/openssl/obj_mac.h\
  include/openssl/objects.h\
  include/openssl/objectserr.h\
  include/openssl/ocsp.h\
  include/openssl/ocsperr.h\
  include/openssl/opensslconf.h\
  include/openssl/opensslv.h\
  include/openssl/ossl_typ.h\
  include/openssl/paillier.h\
  include/openssl/param_build.h\
  include/openssl/params.h\
  include/openssl/pem.h\
  include/openssl/pem2.h\
  include/openssl/pemerr.h\
  include/openssl/pkcs12.h\
  include/openssl/pkcs12err.h\
  include/openssl/pkcs7.h\
  include/openssl/pkcs7err.h\
  include/openssl/prov_ssl.h\
  include/openssl/proverr.h\
  include/openssl/provider.h\
  include/openssl/rand.h\
  include/openssl/randerr.h\
  include/openssl/rc4.h\
  include/openssl/rc5.h\
  include/openssl/rsa.h\
  include/openssl/rsaerr.h\
  include/openssl/safestack.h\
  include/openssl/sdf.h\
  include/openssl/self_test.h\
  include/openssl/sgd.h\
  include/openssl/sha.h\
  include/openssl/sm2_threshold.h\
  include/openssl/sm3.h\
  include/openssl/srp.h\
  include/openssl/srtp.h\
  include/openssl/ssl.h\
  include/openssl/ssl2.h\
  include/openssl/ssl3.h\
  include/openssl/sslerr.h\
  include/openssl/sslerr_legacy.h\
  include/openssl/stack.h\
  include/openssl/store.h\
  include/openssl/storeerr.h\
  include/openssl/symbol_prefix.h\
  include/openssl/symhacks.h\
  include/openssl/tls1.h\
  include/openssl/trace.h\
  include/openssl/ts.h\
  include/openssl/tsapi.h\
  include/openssl/tserr.h\
  include/openssl/txt_db.h\
  include/openssl/types.h\
  include/openssl/ui.h\
  include/openssl/uierr.h\
  include/openssl/x509.h\
  include/openssl/x509_vfy.h\
  include/openssl/x509err.h\
  include/openssl/x509v3.h\
  include/openssl/x509v3err.h\
  include/openssl/zkp_gadget.h\
  include/openssl/zkp_transcript.h\
  include/openssl/zkpbperr.h\
  include/openssl/zkperr.h\
  include/openssl/zkpnizkerr.h

