# Copyright (c) 2024, Tongsuo Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# This file contains source lists for Tongsuo crypto library.
# Generated from Tongsuo Makefile. Do not edit manually.

tongsuo_crypto_sources := \
  crypto/aes/aes_cbc.c\
  crypto/aes/aes_cfb.c\
  crypto/aes/aes_core.c\
  crypto/aes/aes_ecb.c\
  crypto/aes/aes_misc.c\
  crypto/aes/aes_ofb.c\
  crypto/aes/aes_wrap.c\
  crypto/asn1/a_bitstr.c\
  crypto/asn1/a_d2i_fp.c\
  crypto/asn1/a_digest.c\
  crypto/asn1/a_dup.c\
  crypto/asn1/a_gentm.c\
  crypto/asn1/a_i2d_fp.c\
  crypto/asn1/a_int.c\
  crypto/asn1/a_mbstr.c\
  crypto/asn1/a_object.c\
  crypto/asn1/a_octet.c\
  crypto/asn1/a_print.c\
  crypto/asn1/a_sign.c\
  crypto/asn1/a_strex.c\
  crypto/asn1/a_strnid.c\
  crypto/asn1/a_time.c\
  crypto/asn1/a_type.c\
  crypto/asn1/a_utctm.c\
  crypto/asn1/a_utf8.c\
  crypto/asn1/a_verify.c\
  crypto/asn1/ameth_lib.c\
  crypto/asn1/asn1_err.c\
  crypto/asn1/asn1_gen.c\
  crypto/asn1/asn1_item_list.c\
  crypto/asn1/asn1_lib.c\
  crypto/asn1/asn1_parse.c\
  crypto/asn1/asn_mime.c\
  crypto/asn1/asn_moid.c\
  crypto/asn1/asn_mstbl.c\
  crypto/asn1/asn_pack.c\
  crypto/asn1/bio_asn1.c\
  crypto/asn1/bio_ndef.c\
  crypto/asn1/d2i_param.c\
  crypto/asn1/d2i_pr.c\
  crypto/asn1/d2i_pu.c\
  crypto/asn1/evp_asn1.c\
  crypto/asn1/f_int.c\
  crypto/asn1/f_string.c\
  crypto/asn1/i2d_evp.c\
  crypto/asn1/n_pkey.c\
  crypto/asn1/nsseq.c\
  crypto/asn1/p5_pbe.c\
  crypto/asn1/p5_pbev2.c\
  crypto/asn1/p5_scrypt.c\
  crypto/asn1/p8_pkey.c\
  crypto/asn1/t_bitst.c\
  crypto/asn1/t_pkey.c\
  crypto/asn1/t_spki.c\
  crypto/asn1/tasn_dec.c\
  crypto/asn1/tasn_enc.c\
  crypto/asn1/tasn_fre.c\
  crypto/asn1/tasn_new.c\
  crypto/asn1/tasn_prn.c\
  crypto/asn1/tasn_scn.c\
  crypto/asn1/tasn_typ.c\
  crypto/asn1/tasn_utl.c\
  crypto/asn1/x_algor.c\
  crypto/asn1/x_bignum.c\
  crypto/asn1/x_info.c\
  crypto/asn1/x_int64.c\
  crypto/asn1/x_pkey.c\
  crypto/asn1/x_sig.c\
  crypto/asn1/x_spki.c\
  crypto/asn1/x_val.c\
  crypto/async/arch/async_null.c\
  crypto/async/arch/async_posix.c\
  crypto/async/arch/async_win.c\
  crypto/async/async.c\
  crypto/async/async_err.c\
  crypto/async/async_wait.c\
  crypto/bio/bf_buff.c\
  crypto/bio/bf_lbuf.c\
  crypto/bio/bf_nbio.c\
  crypto/bio/bf_null.c\
  crypto/bio/bf_prefix.c\
  crypto/bio/bf_readbuff.c\
  crypto/bio/bio_addr.c\
  crypto/bio/bio_cb.c\
  crypto/bio/bio_dump.c\
  crypto/bio/bio_err.c\
  crypto/bio/bio_lib.c\
  crypto/bio/bio_meth.c\
  crypto/bio/bio_print.c\
  crypto/bio/bio_sock.c\
  crypto/bio/bio_sock2.c\
  crypto/bio/bss_acpt.c\
  crypto/bio/bss_bio.c\
  crypto/bio/bss_conn.c\
  crypto/bio/bss_core.c\
  crypto/bio/bss_dgram.c\
  crypto/bio/bss_fd.c\
  crypto/bio/bss_file.c\
  crypto/bio/bss_log.c\
  crypto/bio/bss_mem.c\
  crypto/bio/bss_null.c\
  crypto/bio/bss_sock.c\
  crypto/bio/ossl_core_bio.c\
  crypto/bn/bn_add.c\
  crypto/bn/bn_asm.c\
  crypto/bn/bn_blind.c\
  crypto/bn/bn_const.c\
  crypto/bn/bn_conv.c\
  crypto/bn/bn_ctx.c\
  crypto/bn/bn_dh.c\
  crypto/bn/bn_div.c\
  crypto/bn/bn_err.c\
  crypto/bn/bn_exp.c\
  crypto/bn/bn_exp2.c\
  crypto/bn/bn_gcd.c\
  crypto/bn/bn_gf2m.c\
  crypto/bn/bn_intern.c\
  crypto/bn/bn_kron.c\
  crypto/bn/bn_lib.c\
  crypto/bn/bn_mod.c\
  crypto/bn/bn_mont.c\
  crypto/bn/bn_mpi.c\
  crypto/bn/bn_mul.c\
  crypto/bn/bn_nist.c\
  crypto/bn/bn_prime.c\
  crypto/bn/bn_print.c\
  crypto/bn/bn_rand.c\
  crypto/bn/bn_recp.c\
  crypto/bn/bn_rsa_fips186_4.c\
  crypto/bn/bn_shift.c\
  crypto/bn/bn_sm2.c\
  crypto/bn/bn_sqr.c\
  crypto/bn/bn_sqrt.c\
  crypto/bn/bn_srp.c\
  crypto/bn/bn_word.c\
  crypto/buffer/buf_err.c\
  crypto/buffer/buffer.c\
  crypto/cmac/cmac.c\
  crypto/cmp/cmp_asn.c\
  crypto/cmp/cmp_client.c\
  crypto/cmp/cmp_ctx.c\
  crypto/cmp/cmp_err.c\
  crypto/cmp/cmp_hdr.c\
  crypto/cmp/cmp_http.c\
  crypto/cmp/cmp_msg.c\
  crypto/cmp/cmp_protect.c\
  crypto/cmp/cmp_server.c\
  crypto/cmp/cmp_status.c\
  crypto/cmp/cmp_util.c\
  crypto/cmp/cmp_vfy.c\
  crypto/cms/cms_asn1.c\
  crypto/cms/cms_att.c\
  crypto/cms/cms_cd.c\
  crypto/cms/cms_dd.c\
  crypto/cms/cms_dh.c\
  crypto/cms/cms_ec.c\
  crypto/cms/cms_enc.c\
  crypto/cms/cms_env.c\
  crypto/cms/cms_err.c\
  crypto/cms/cms_ess.c\
  crypto/cms/cms_io.c\
  crypto/cms/cms_kari.c\
  crypto/cms/cms_lib.c\
  crypto/cms/cms_pwri.c\
  crypto/cms/cms_rsa.c\
  crypto/cms/cms_sd.c\
  crypto/cms/cms_smime.c\
  crypto/comp/c_zlib.c\
  crypto/comp/comp_err.c\
  crypto/comp/comp_lib.c\
  crypto/conf/conf_api.c\
  crypto/conf/conf_def.c\
  crypto/conf/conf_err.c\
  crypto/conf/conf_lib.c\
  crypto/conf/conf_mall.c\
  crypto/conf/conf_mod.c\
  crypto/conf/conf_sap.c\
  crypto/conf/conf_ssl.c\
  crypto/crmf/crmf_asn.c\
  crypto/crmf/crmf_err.c\
  crypto/crmf/crmf_lib.c\
  crypto/crmf/crmf_pbm.c\
  crypto/ct/ct_b64.c\
  crypto/ct/ct_err.c\
  crypto/ct/ct_log.c\
  crypto/ct/ct_oct.c\
  crypto/ct/ct_policy.c\
  crypto/ct/ct_prn.c\
  crypto/ct/ct_sct.c\
  crypto/ct/ct_sct_ctx.c\
  crypto/ct/ct_vfy.c\
  crypto/ct/ct_x509v3.c\
  crypto/des/cbc_cksm.c\
  crypto/des/cbc_enc.c\
  crypto/des/cfb64ede.c\
  crypto/des/cfb64enc.c\
  crypto/des/cfb_enc.c\
  crypto/des/des_enc.c\
  crypto/des/ecb3_enc.c\
  crypto/des/ecb_enc.c\
  crypto/des/fcrypt.c\
  crypto/des/fcrypt_b.c\
  crypto/des/ofb64ede.c\
  crypto/des/ofb64enc.c\
  crypto/des/ofb_enc.c\
  crypto/des/pcbc_enc.c\
  crypto/des/qud_cksm.c\
  crypto/des/rand_key.c\
  crypto/des/set_key.c\
  crypto/des/str2key.c\
  crypto/des/xcbc_enc.c\
  crypto/dh/dh_ameth.c\
  crypto/dh/dh_asn1.c\
  crypto/dh/dh_backend.c\
  crypto/dh/dh_check.c\
  crypto/dh/dh_err.c\
  crypto/dh/dh_gen.c\
  crypto/dh/dh_group_params.c\
  crypto/dh/dh_kdf.c\
  crypto/dh/dh_key.c\
  crypto/dh/dh_lib.c\
  crypto/dh/dh_meth.c\
  crypto/dh/dh_pmeth.c\
  crypto/dh/dh_prn.c\
  crypto/dh/dh_rfc5114.c\
  crypto/dsa/dsa_ameth.c\
  crypto/dsa/dsa_asn1.c\
  crypto/dsa/dsa_backend.c\
  crypto/dsa/dsa_check.c\
  crypto/dsa/dsa_err.c\
  crypto/dsa/dsa_gen.c\
  crypto/dsa/dsa_key.c\
  crypto/dsa/dsa_lib.c\
  crypto/dsa/dsa_meth.c\
  crypto/dsa/dsa_ossl.c\
  crypto/dsa/dsa_pmeth.c\
  crypto/dsa/dsa_prn.c\
  crypto/dsa/dsa_sign.c\
  crypto/dsa/dsa_vrf.c\
  crypto/dso/dso_dl.c\
  crypto/dso/dso_dlfcn.c\
  crypto/dso/dso_err.c\
  crypto/dso/dso_lib.c\
  crypto/dso/dso_openssl.c\
  crypto/dso/dso_win32.c\
  crypto/ec/curve448/arch_32/f_impl32.c\
  crypto/ec/curve448/arch_64/f_impl64.c\
  crypto/ec/curve448/curve448.c\
  crypto/ec/curve448/curve448_tables.c\
  crypto/ec/curve448/eddsa.c\
  crypto/ec/curve448/f_generic.c\
  crypto/ec/curve448/scalar.c\
  crypto/ec/curve25519.c\
  crypto/ec/ec2_oct.c\
  crypto/ec/ec2_smpl.c\
  crypto/ec/ec_ameth.c\
  crypto/ec/ec_asn1.c\
  crypto/ec/ec_backend.c\
  crypto/ec/ec_check.c\
  crypto/ec/ec_curve.c\
  crypto/ec/ec_cvt.c\
  crypto/ec/ec_deprecated.c\
  crypto/ec/ec_err.c\
  crypto/ec/ec_key.c\
  crypto/ec/ec_kmeth.c\
  crypto/ec/ec_lib.c\
  crypto/ec/ec_mult.c\
  crypto/ec/ec_oct.c\
  crypto/ec/ec_pmeth.c\
  crypto/ec/ec_print.c\
  crypto/ec/ecdh_kdf.c\
  crypto/ec/ecdh_ossl.c\
  crypto/ec/ecdsa_ossl.c\
  crypto/ec/ecdsa_sign.c\
  crypto/ec/ecdsa_vrf.c\
  crypto/ec/eck_prn.c\
  crypto/ec/ecp_meth.c\
  crypto/ec/ecp_mont.c\
  crypto/ec/ecp_nist.c\
  crypto/ec/ecp_nistz256.c\
  crypto/ec/ecp_oct.c\
  crypto/ec/ecp_smpl.c\
  crypto/ec/ecx_backend.c\
  crypto/ec/ecx_key.c\
  crypto/ec/ecx_meth.c\
  crypto/eia3/eia3.c\
  crypto/encode_decode/decoder_err.c\
  crypto/encode_decode/decoder_lib.c\
  crypto/encode_decode/decoder_meth.c\
  crypto/encode_decode/decoder_pkey.c\
  crypto/encode_decode/encoder_err.c\
  crypto/encode_decode/encoder_lib.c\
  crypto/encode_decode/encoder_meth.c\
  crypto/encode_decode/encoder_pkey.c\
  crypto/err/err.c\
  crypto/err/err_all.c\
  crypto/err/err_all_legacy.c\
  crypto/err/err_blocks.c\
  crypto/err/err_prn.c\
  crypto/ess/ess_asn1.c\
  crypto/ess/ess_err.c\
  crypto/ess/ess_lib.c\
  crypto/evp/asymcipher.c\
  crypto/evp/bio_b64.c\
  crypto/evp/bio_enc.c\
  crypto/evp/bio_md.c\
  crypto/evp/bio_ok.c\
  crypto/evp/c_allc.c\
  crypto/evp/c_alld.c\
  crypto/evp/cmeth_lib.c\
  crypto/evp/ctrl_params_translate.c\
  crypto/evp/dh_ctrl.c\
  crypto/evp/dh_support.c\
  crypto/evp/digest.c\
  crypto/evp/dsa_ctrl.c\
  crypto/evp/e_aes.c\
  crypto/evp/e_aes_cbc_hmac_sha1.c\
  crypto/evp/e_aes_cbc_hmac_sha256.c\
  crypto/evp/e_chacha20_poly1305.c\
  crypto/evp/e_des.c\
  crypto/evp/e_des3.c\
  crypto/evp/e_eea3.c\
  crypto/evp/e_null.c\
  crypto/evp/e_rc4.c\
  crypto/evp/e_rc4_hmac_md5.c\
  crypto/evp/e_rc5.c\
  crypto/evp/e_sm4.c\
  crypto/evp/e_wbsm4_baiwu.c\
  crypto/evp/e_wbsm4_wsise.c\
  crypto/evp/e_wbsm4_xiaolai.c\
  crypto/evp/e_xcbc_d.c\
  crypto/evp/ec_ctrl.c\
  crypto/evp/ec_support.c\
  crypto/evp/encode.c\
  crypto/evp/evp_cnf.c\
  crypto/evp/evp_enc.c\
  crypto/evp/evp_err.c\
  crypto/evp/evp_fetch.c\
  crypto/evp/evp_key.c\
  crypto/evp/evp_lib.c\
  crypto/evp/evp_pbe.c\
  crypto/evp/evp_pkey.c\
  crypto/evp/evp_rand.c\
  crypto/evp/evp_utils.c\
  crypto/evp/exchange.c\
  crypto/evp/kdf_lib.c\
  crypto/evp/kdf_meth.c\
  crypto/evp/kem.c\
  crypto/evp/keymgmt_lib.c\
  crypto/evp/keymgmt_meth.c\
  crypto/evp/legacy_md5.c\
  crypto/evp/legacy_md5_sha1.c\
  crypto/evp/legacy_sha.c\
  crypto/evp/m_null.c\
  crypto/evp/m_sigver.c\
  crypto/evp/mac_lib.c\
  crypto/evp/mac_meth.c\
  crypto/evp/names.c\
  crypto/evp/p5_crpt.c\
  crypto/evp/p5_crpt2.c\
  crypto/evp/p_legacy.c\
  crypto/evp/p_lib.c\
  crypto/evp/p_open.c\
  crypto/evp/p_seal.c\
  crypto/evp/p_sign.c\
  crypto/evp/p_verify.c\
  crypto/evp/pbe_scrypt.c\
  crypto/evp/pmeth_check.c\
  crypto/evp/pmeth_gn.c\
  crypto/evp/pmeth_lib.c\
  crypto/evp/signature.c\
  crypto/ffc/ffc_backend.c\
  crypto/ffc/ffc_dh.c\
  crypto/ffc/ffc_key_generate.c\
  crypto/ffc/ffc_key_validate.c\
  crypto/ffc/ffc_params.c\
  crypto/ffc/ffc_params_generate.c\
  crypto/ffc/ffc_params_validate.c\
  crypto/hmac/hmac.c\
  crypto/http/http_client.c\
  crypto/http/http_err.c\
  crypto/http/http_lib.c\
  crypto/kdf/kdf_err.c\
  crypto/lhash/lh_stats.c\
  crypto/lhash/lhash.c\
  crypto/asn1_dsa.c\
  crypto/bsearch.c\
  crypto/context.c\
  crypto/core_algorithm.c\
  crypto/core_fetch.c\
  crypto/core_namemap.c\
  crypto/cpt_err.c\
  crypto/cpuid.c\
  crypto/cryptlib.c\
  crypto/ctype.c\
  crypto/cversion.c\
  crypto/der_writer.c\
  crypto/ebcdic.c\
  crypto/ex_data.c\
  crypto/getenv.c\
  crypto/info.c\
  crypto/init.c\
  crypto/initthread.c\
  crypto/mem.c\
  crypto/mem_sec.c\
  crypto/o_dir.c\
  crypto/o_fopen.c\
  crypto/o_init.c\
  crypto/o_str.c\
  crypto/o_syslog.c\
  crypto/o_time.c\
  crypto/packet.c\
  crypto/param_build.c\
  crypto/param_build_set.c\
  crypto/params.c\
  crypto/params_dup.c\
  crypto/params_from_text.c\
  crypto/passphrase.c\
  crypto/provider.c\
  crypto/provider_child.c\
  crypto/provider_conf.c\
  crypto/provider_core.c\
  crypto/provider_predefined.c\
  crypto/punycode.c\
  crypto/self_test_core.c\
  crypto/sparse_array.c\
  crypto/threads_lib.c\
  crypto/threads_none.c\
  crypto/threads_pthread.c\
  crypto/threads_win.c\
  crypto/trace.c\
  crypto/uid.c\
