#!/usr/bin/env python3
"""
Extract source files from Tongsuo Makefile and generate sources.mk
"""

import re
import os

def extract_sources_from_makefile():
    """Extract all source files from the libcrypto.a target in Makefile"""
    
    # Read the Makefile
    with open('Makefile', 'r') as f:
        content = f.read()
    
    # Find the libcrypto.a target definition
    # Look for the pattern: libcrypto.a: ... \
    libcrypto_match = re.search(r'libcrypto\.a:\s*([^$]+?)(?=\n\s*\$\(RM\))', content, re.DOTALL)
    
    if not libcrypto_match:
        print("Could not find libcrypto.a target in Makefile")
        return []
    
    libcrypto_content = libcrypto_match.group(1)
    
    # Extract all .o files and convert them to .c files
    object_files = re.findall(r'([a-zA-Z0-9_/.-]+)\.o', libcrypto_content)
    
    source_files = []
    asm_files = []
    
    for obj_file in object_files:
        # Remove the lib prefix pattern (e.g., libcrypto-lib-, libdefault-lib-, etc.)
        source_path = re.sub(r'/lib[a-zA-Z0-9_-]*-lib-', '/', obj_file)
        
        # Check if it's an assembly file
        if any(asm_ext in source_path for asm_ext in ['-armv8', '-armx', 'armv8-mont', 'ecp_nistz256-armv8']):
            # These are assembly files, add .S extension
            asm_files.append(source_path + '.S')
        else:
            # Regular C source file
            source_files.append(source_path + '.c')
    
    # Remove duplicates and sort
    source_files = sorted(list(set(source_files)))
    asm_files = sorted(list(set(asm_files)))
    
    return source_files, asm_files

def generate_sources_mk(source_files, asm_files):
    """Generate the sources.mk file"""
    
    content = """# Generated from Makefile by extract_sources_from_makefile.py
# This file contains all source files for Tongsuo crypto library

tongsuo_crypto_sources := \\
"""
    
    # Add C source files
    for i, src in enumerate(source_files):
        if i == len(source_files) - 1:
            content += f"  {src}\n"
        else:
            content += f"  {src}\\\n"
    
    content += "\ntongsuo_crypto_sources_asm := \\\n"
    
    # Add assembly source files
    for i, src in enumerate(asm_files):
        if i == len(asm_files) - 1:
            content += f"  {src}\n"
        else:
            content += f"  {src}\\\n"
    
    # Write to sources.mk
    with open('sources.mk', 'w') as f:
        f.write(content)
    
    print(f"Generated sources.mk with {len(source_files)} C files and {len(asm_files)} assembly files")
    print(f"Total: {len(source_files) + len(asm_files)} source files")

def main():
    if not os.path.exists('Makefile'):
        print("Error: Makefile not found in current directory")
        return
    
    print("Extracting source files from Makefile...")
    source_files, asm_files = extract_sources_from_makefile()
    
    if not source_files:
        print("No source files found!")
        return
    
    print(f"Found {len(source_files)} C source files")
    print(f"Found {len(asm_files)} assembly source files")
    
    # Show some examples
    print("\nExample C source files:")
    for src in source_files[:10]:
        print(f"  {src}")
    
    if asm_files:
        print("\nExample assembly source files:")
        for src in asm_files[:5]:
            print(f"  {src}")
    
    print("\nGenerating sources.mk...")
    generate_sources_mk(source_files, asm_files)
    
    print("Done!")

if __name__ == "__main__":
    main()
