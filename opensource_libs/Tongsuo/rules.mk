# Copyright (C) 2024 The Tongsuo Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

LOCAL_DIR := $(GET_LOCAL_DIR)

MODULE := $(LOCAL_DIR)

TONGSUO_DIR := opensource_libs/Tongsuo

include $(TONGSUO_DIR)/sources.mk

# Use only a minimal set of sources for testing
MODULE_SRCS := \
	$(TONGSUO_DIR)/crypto/mem.c \
	$(TONGSUO_DIR)/crypto/cryptlib.c \
	$(TONGSUO_DIR)/crypto/cversion.c \
	$(TONGSUO_DIR)/crypto/ex_data.c \
	$(TONGSUO_DIR)/crypto/cpt_err.c \
	$(TONGSUO_DIR)/crypto/ebcdic.c \
	$(TONGSUO_DIR)/crypto/uid.c \
	$(TONGSUO_DIR)/crypto/o_time.c \
	$(TONGSUO_DIR)/crypto/o_str.c \
	$(TONGSUO_DIR)/crypto/o_dir.c \
	$(TONGSUO_DIR)/crypto/o_fopen.c \
	$(TONGSUO_DIR)/crypto/o_init.c \
	$(TONGSUO_DIR)/crypto/aes/aes_core.c \
	$(TONGSUO_DIR)/crypto/aes/aes_cbc.c \
	$(TONGSUO_DIR)/crypto/aes/aes_cfb.c \
	$(TONGSUO_DIR)/crypto/aes/aes_ecb.c \
	$(TONGSUO_DIR)/crypto/aes/aes_misc.c \
	$(TONGSUO_DIR)/crypto/aes/aes_ofb.c \
	$(TONGSUO_DIR)/crypto/aes/aes_wrap.c \

MODULE_EXPORT_INCLUDES += \
	$(TONGSUO_DIR)/include \

MODULE_INCLUDES += \
	$(TONGSUO_DIR)/include \
	$(TONGSUO_DIR)/crypto \

MODULE_CFLAGS += \
	-DTONGSUO_IMPLEMENTATION \
	-DOPENSSL_NO_STDIO \
	-DOPENSSL_NO_SOCK \
	-DOPENSSL_NO_THREADS \
	-D__TEE__ \
	-DOPENSSL_NO_ASM \
	-DOPENSSL_NO_ENGINE \
	-DOPENSSL_NO_HW \
	-DOPENSSL_NO_DEPRECATED \
	-DOPENSSL_SMALL_FOOTPRINT \
	-Wno-unused-parameter \
	-Wno-sign-compare \
	-Wno-unused-function \
	-Wno-unused-variable \
	-Wno-implicit-function-declaration \

MODULE_ASFLAGS += \
	-Wno-unused-parameter \

# Symbol prefix isolation to avoid conflicts with BoringSSL
# MODULE_CFLAGS += -DSYMBOL_PREFIX=TONGSUO_

# Disable features not needed in TEE environment
MODULE_CFLAGS += \
	-DOPENSSL_NO_APPS \
	-DOPENSSL_NO_ASYNC \
	-DOPENSSL_NO_AUTOLOAD_CONFIG \
	-DOPENSSL_NO_DEPRECATED \
	-DOPENSSL_NO_DGRAM \
	-DOPENSSL_NO_DSO \
	-DOPENSSL_NO_DTLS \
	-DOPENSSL_NO_ENGINE \
	-DOPENSSL_NO_FIPS \
	-DOPENSSL_NO_SHARED \
	-DOPENSSL_NO_SSL \
	-DOPENSSL_NO_TESTS \
	-DOPENSSL_NO_TLS \
	-DOPENSSL_NO_UI_CONSOLE \

# Enable SM algorithms
MODULE_CFLAGS += \
	-DOPENSSL_ENABLE_SM2 \
	-DOPENSSL_ENABLE_SM3 \
	-DOPENSSL_ENABLE_SM4 \

include make/rctee_lib.mk
