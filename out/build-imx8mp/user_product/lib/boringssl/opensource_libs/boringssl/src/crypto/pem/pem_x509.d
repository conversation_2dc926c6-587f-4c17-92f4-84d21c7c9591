out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/pem/pem_x509.o: \
  kernel/rctee/lib/ubsan/exemptlist \
  /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt \
  opensource_libs/boringssl/src/crypto/pem/pem_x509.c \
  opensource_libs/libcxx/include/stdio.h \
  opensource_libs/libcxx/include/__config \
  opensource_libs/musl/include/stdio.h \
  opensource_libs/musl/include/features.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  opensource_libs/Tongsuo/include/openssl/bio.h \
  opensource_libs/Tongsuo/include/openssl/macros.h \
  opensource_libs/Tongsuo/include/openssl/opensslconf.h \
  opensource_libs/Tongsuo/include/openssl/configuration.h \
  opensource_libs/Tongsuo/include/openssl/symbol_prefix.h \
  opensource_libs/Tongsuo/include/openssl/opensslv.h \
  opensource_libs/Tongsuo/include/openssl/e_os2.h \
  opensource_libs/libcxx/include/inttypes.h \
  opensource_libs/musl/include/inttypes.h \
  opensource_libs/libcxx/include/stdint.h \
  opensource_libs/musl/include/stdint.h \
  opensource_libs/musl/arch/aarch64/bits/stdint.h \
  opensource_libs/musl/include/stdarg.h \
  opensource_libs/Tongsuo/include/openssl/crypto.h \
  opensource_libs/libcxx/include/stdlib.h \
  opensource_libs/musl/include/stdlib.h \
  opensource_libs/musl/include/alloca.h \
  opensource_libs/musl/include/time.h \
  opensource_libs/Tongsuo/include/openssl/safestack.h \
  opensource_libs/Tongsuo/include/openssl/stack.h \
  opensource_libs/Tongsuo/include/openssl/types.h \
  opensource_libs/libcxx/include/limits.h \
  opensource_libs/musl/include/limits.h \
  opensource_libs/musl/arch/aarch64/bits/limits.h \
  opensource_libs/Tongsuo/include/openssl/cryptoerr.h \
  opensource_libs/Tongsuo/include/openssl/symhacks.h \
  opensource_libs/Tongsuo/include/openssl/cryptoerr_legacy.h \
  opensource_libs/Tongsuo/include/openssl/core.h \
  opensource_libs/libcxx/include/stddef.h \
  opensource_libs/musl/include/stddef.h \
  opensource_libs/musl/include/syslog.h \
  opensource_libs/Tongsuo/include/openssl/bioerr.h \
  opensource_libs/Tongsuo/include/openssl/evp.h \
  opensource_libs/Tongsuo/include/openssl/core_dispatch.h \
  opensource_libs/Tongsuo/include/openssl/evperr.h \
  opensource_libs/Tongsuo/include/openssl/params.h \
  opensource_libs/Tongsuo/include/openssl/bn.h \
  opensource_libs/Tongsuo/include/openssl/bnerr.h \
  opensource_libs/Tongsuo/include/openssl/objects.h \
  opensource_libs/Tongsuo/include/openssl/obj_mac.h \
  opensource_libs/Tongsuo/include/openssl/asn1.h \
  opensource_libs/Tongsuo/include/openssl/asn1err.h \
  opensource_libs/Tongsuo/include/openssl/objectserr.h \
  opensource_libs/Tongsuo/include/openssl/pem.h \
  opensource_libs/Tongsuo/include/openssl/x509.h \
  opensource_libs/Tongsuo/include/openssl/buffer.h \
  opensource_libs/Tongsuo/include/openssl/buffererr.h \
  opensource_libs/musl/include/sys/types.h \
  opensource_libs/musl/include/endian.h \
  opensource_libs/musl/include/sys/select.h \
  opensource_libs/Tongsuo/include/openssl/ec.h \
  opensource_libs/libcxx/include/string.h \
  opensource_libs/musl/include/string.h \
  opensource_libs/musl/include/strings.h \
  opensource_libs/Tongsuo/include/openssl/ecerr.h \
  opensource_libs/Tongsuo/include/openssl/sha.h \
  opensource_libs/Tongsuo/include/openssl/x509err.h \
  opensource_libs/Tongsuo/include/openssl/x509_vfy.h \
  opensource_libs/Tongsuo/include/openssl/lhash.h \
  opensource_libs/Tongsuo/include/openssl/pkcs7.h \
  opensource_libs/Tongsuo/include/openssl/pkcs7err.h \
  opensource_libs/Tongsuo/include/openssl/pemerr.h

kernel/rctee/lib/ubsan/exemptlist:

/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt:

opensource_libs/libcxx/include/stdio.h:

opensource_libs/libcxx/include/__config:

opensource_libs/musl/include/stdio.h:

opensource_libs/musl/include/features.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

opensource_libs/Tongsuo/include/openssl/bio.h:

opensource_libs/Tongsuo/include/openssl/macros.h:

opensource_libs/Tongsuo/include/openssl/opensslconf.h:

opensource_libs/Tongsuo/include/openssl/configuration.h:

opensource_libs/Tongsuo/include/openssl/symbol_prefix.h:

opensource_libs/Tongsuo/include/openssl/opensslv.h:

opensource_libs/Tongsuo/include/openssl/e_os2.h:

opensource_libs/libcxx/include/inttypes.h:

opensource_libs/musl/include/inttypes.h:

opensource_libs/libcxx/include/stdint.h:

opensource_libs/musl/include/stdint.h:

opensource_libs/musl/arch/aarch64/bits/stdint.h:

opensource_libs/musl/include/stdarg.h:

opensource_libs/Tongsuo/include/openssl/crypto.h:

opensource_libs/libcxx/include/stdlib.h:

opensource_libs/musl/include/stdlib.h:

opensource_libs/musl/include/alloca.h:

opensource_libs/musl/include/time.h:

opensource_libs/Tongsuo/include/openssl/safestack.h:

opensource_libs/Tongsuo/include/openssl/stack.h:

opensource_libs/Tongsuo/include/openssl/types.h:

opensource_libs/libcxx/include/limits.h:

opensource_libs/musl/include/limits.h:

opensource_libs/musl/arch/aarch64/bits/limits.h:

opensource_libs/Tongsuo/include/openssl/cryptoerr.h:

opensource_libs/Tongsuo/include/openssl/symhacks.h:

opensource_libs/Tongsuo/include/openssl/cryptoerr_legacy.h:

opensource_libs/Tongsuo/include/openssl/core.h:

opensource_libs/libcxx/include/stddef.h:

opensource_libs/musl/include/stddef.h:

opensource_libs/musl/include/syslog.h:

opensource_libs/Tongsuo/include/openssl/bioerr.h:

opensource_libs/Tongsuo/include/openssl/evp.h:

opensource_libs/Tongsuo/include/openssl/core_dispatch.h:

opensource_libs/Tongsuo/include/openssl/evperr.h:

opensource_libs/Tongsuo/include/openssl/params.h:

opensource_libs/Tongsuo/include/openssl/bn.h:

opensource_libs/Tongsuo/include/openssl/bnerr.h:

opensource_libs/Tongsuo/include/openssl/objects.h:

opensource_libs/Tongsuo/include/openssl/obj_mac.h:

opensource_libs/Tongsuo/include/openssl/asn1.h:

opensource_libs/Tongsuo/include/openssl/asn1err.h:

opensource_libs/Tongsuo/include/openssl/objectserr.h:

opensource_libs/Tongsuo/include/openssl/pem.h:

opensource_libs/Tongsuo/include/openssl/x509.h:

opensource_libs/Tongsuo/include/openssl/buffer.h:

opensource_libs/Tongsuo/include/openssl/buffererr.h:

opensource_libs/musl/include/sys/types.h:

opensource_libs/musl/include/endian.h:

opensource_libs/musl/include/sys/select.h:

opensource_libs/Tongsuo/include/openssl/ec.h:

opensource_libs/libcxx/include/string.h:

opensource_libs/musl/include/string.h:

opensource_libs/musl/include/strings.h:

opensource_libs/Tongsuo/include/openssl/ecerr.h:

opensource_libs/Tongsuo/include/openssl/sha.h:

opensource_libs/Tongsuo/include/openssl/x509err.h:

opensource_libs/Tongsuo/include/openssl/x509_vfy.h:

opensource_libs/Tongsuo/include/openssl/lhash.h:

opensource_libs/Tongsuo/include/openssl/pkcs7.h:

opensource_libs/Tongsuo/include/openssl/pkcs7err.h:

opensource_libs/Tongsuo/include/openssl/pemerr.h:
