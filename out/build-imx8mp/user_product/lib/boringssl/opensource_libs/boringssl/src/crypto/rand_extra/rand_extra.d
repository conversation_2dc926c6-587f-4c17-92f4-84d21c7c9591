out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/rand_extra/rand_extra.o: \
  kernel/rctee/lib/ubsan/exemptlist \
  /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt \
  opensource_libs/boringssl/src/crypto/rand_extra/rand_extra.c \
  opensource_libs/Tongsuo/include/openssl/rand.h \
  opensource_libs/Tongsuo/include/openssl/macros.h \
  opensource_libs/Tongsuo/include/openssl/opensslconf.h \
  opensource_libs/Tongsuo/include/openssl/configuration.h \
  opensource_libs/Tongsuo/include/openssl/symbol_prefix.h \
  opensource_libs/Tongsuo/include/openssl/opensslv.h \
  opensource_libs/libcxx/include/stdlib.h \
  opensource_libs/libcxx/include/__config \
  opensource_libs/musl/include/stdlib.h \
  opensource_libs/musl/include/features.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  opensource_libs/musl/include/alloca.h \
  opensource_libs/Tongsuo/include/openssl/types.h \
  opensource_libs/libcxx/include/limits.h \
  opensource_libs/musl/include/limits.h \
  opensource_libs/musl/arch/aarch64/bits/limits.h \
  opensource_libs/Tongsuo/include/openssl/e_os2.h \
  opensource_libs/libcxx/include/inttypes.h \
  opensource_libs/musl/include/inttypes.h \
  opensource_libs/libcxx/include/stdint.h \
  opensource_libs/musl/include/stdint.h \
  opensource_libs/musl/arch/aarch64/bits/stdint.h \
  opensource_libs/Tongsuo/include/openssl/safestack.h \
  opensource_libs/Tongsuo/include/openssl/stack.h \
  opensource_libs/Tongsuo/include/openssl/randerr.h \
  opensource_libs/Tongsuo/include/openssl/symhacks.h \
  opensource_libs/Tongsuo/include/openssl/cryptoerr_legacy.h \
  opensource_libs/Tongsuo/include/openssl/evp.h \
  opensource_libs/musl/include/stdarg.h \
  opensource_libs/Tongsuo/include/openssl/core.h \
  opensource_libs/libcxx/include/stddef.h \
  opensource_libs/musl/include/stddef.h \
  opensource_libs/Tongsuo/include/openssl/core_dispatch.h \
  opensource_libs/Tongsuo/include/openssl/bio.h \
  opensource_libs/Tongsuo/include/openssl/crypto.h \
  opensource_libs/musl/include/time.h \
  opensource_libs/Tongsuo/include/openssl/cryptoerr.h \
  opensource_libs/musl/include/syslog.h \
  opensource_libs/Tongsuo/include/openssl/bioerr.h \
  opensource_libs/Tongsuo/include/openssl/evperr.h \
  opensource_libs/Tongsuo/include/openssl/params.h \
  opensource_libs/Tongsuo/include/openssl/bn.h \
  opensource_libs/Tongsuo/include/openssl/bnerr.h \
  opensource_libs/Tongsuo/include/openssl/objects.h \
  opensource_libs/Tongsuo/include/openssl/obj_mac.h \
  opensource_libs/Tongsuo/include/openssl/asn1.h \
  opensource_libs/Tongsuo/include/openssl/asn1err.h \
  opensource_libs/Tongsuo/include/openssl/objectserr.h

kernel/rctee/lib/ubsan/exemptlist:

/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt:

opensource_libs/Tongsuo/include/openssl/rand.h:

opensource_libs/Tongsuo/include/openssl/macros.h:

opensource_libs/Tongsuo/include/openssl/opensslconf.h:

opensource_libs/Tongsuo/include/openssl/configuration.h:

opensource_libs/Tongsuo/include/openssl/symbol_prefix.h:

opensource_libs/Tongsuo/include/openssl/opensslv.h:

opensource_libs/libcxx/include/stdlib.h:

opensource_libs/libcxx/include/__config:

opensource_libs/musl/include/stdlib.h:

opensource_libs/musl/include/features.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

opensource_libs/musl/include/alloca.h:

opensource_libs/Tongsuo/include/openssl/types.h:

opensource_libs/libcxx/include/limits.h:

opensource_libs/musl/include/limits.h:

opensource_libs/musl/arch/aarch64/bits/limits.h:

opensource_libs/Tongsuo/include/openssl/e_os2.h:

opensource_libs/libcxx/include/inttypes.h:

opensource_libs/musl/include/inttypes.h:

opensource_libs/libcxx/include/stdint.h:

opensource_libs/musl/include/stdint.h:

opensource_libs/musl/arch/aarch64/bits/stdint.h:

opensource_libs/Tongsuo/include/openssl/safestack.h:

opensource_libs/Tongsuo/include/openssl/stack.h:

opensource_libs/Tongsuo/include/openssl/randerr.h:

opensource_libs/Tongsuo/include/openssl/symhacks.h:

opensource_libs/Tongsuo/include/openssl/cryptoerr_legacy.h:

opensource_libs/Tongsuo/include/openssl/evp.h:

opensource_libs/musl/include/stdarg.h:

opensource_libs/Tongsuo/include/openssl/core.h:

opensource_libs/libcxx/include/stddef.h:

opensource_libs/musl/include/stddef.h:

opensource_libs/Tongsuo/include/openssl/core_dispatch.h:

opensource_libs/Tongsuo/include/openssl/bio.h:

opensource_libs/Tongsuo/include/openssl/crypto.h:

opensource_libs/musl/include/time.h:

opensource_libs/Tongsuo/include/openssl/cryptoerr.h:

opensource_libs/musl/include/syslog.h:

opensource_libs/Tongsuo/include/openssl/bioerr.h:

opensource_libs/Tongsuo/include/openssl/evperr.h:

opensource_libs/Tongsuo/include/openssl/params.h:

opensource_libs/Tongsuo/include/openssl/bn.h:

opensource_libs/Tongsuo/include/openssl/bnerr.h:

opensource_libs/Tongsuo/include/openssl/objects.h:

opensource_libs/Tongsuo/include/openssl/obj_mac.h:

opensource_libs/Tongsuo/include/openssl/asn1.h:

opensource_libs/Tongsuo/include/openssl/asn1err.h:

opensource_libs/Tongsuo/include/openssl/objectserr.h:
