out/build-imx8mp/user_product/lib/app_manifest/kernel/rctee/lib/app_manifest/app_manifest.o: \
  kernel/rctee/lib/ubsan/exemptlist \
  /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt \
  kernel/rctee/lib/app_manifest/app_manifest.c \
  opensource_libs/musl/include/assert.h \
  opensource_libs/musl/include/features.h \
  opensource_libs/libcxx/include/inttypes.h \
  opensource_libs/libcxx/include/__config \
  opensource_libs/musl/include/inttypes.h \
  opensource_libs/libcxx/include/stdint.h \
  opensource_libs/musl/include/stdint.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  opensource_libs/musl/arch/aarch64/bits/stdint.h \
  kernel/rctee/lib/app_manifest/include/lib/app_manifest/app_manifest.h \
  kernel/lk/include/shared/lk/compiler.h \
  opensource_libs/libcxx/include/stddef.h \
  opensource_libs/musl/include/stddef.h \
  opensource_libs/libcxx/include/stdbool.h \
  opensource_libs/musl/include/stdbool.h \
  kernel/rctee/include/uapi/uapi/rctee_uuid.h \
  kernel/lk/include/shared/lk/macros.h \
  opensource_libs/libcxx/include/string.h \
  opensource_libs/musl/include/string.h \
  opensource_libs/musl/include/strings.h \
  user/base/include/user/trusty_log.h \
  opensource_libs/libcxx/include/stdio.h \
  opensource_libs/musl/include/stdio.h kernel/lk/include/uapi/uapi/err.h

kernel/rctee/lib/ubsan/exemptlist:

/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt:

opensource_libs/musl/include/assert.h:

opensource_libs/musl/include/features.h:

opensource_libs/libcxx/include/inttypes.h:

opensource_libs/libcxx/include/__config:

opensource_libs/musl/include/inttypes.h:

opensource_libs/libcxx/include/stdint.h:

opensource_libs/musl/include/stdint.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

opensource_libs/musl/arch/aarch64/bits/stdint.h:

kernel/rctee/lib/app_manifest/include/lib/app_manifest/app_manifest.h:

kernel/lk/include/shared/lk/compiler.h:

opensource_libs/libcxx/include/stddef.h:

opensource_libs/musl/include/stddef.h:

opensource_libs/libcxx/include/stdbool.h:

opensource_libs/musl/include/stdbool.h:

kernel/rctee/include/uapi/uapi/rctee_uuid.h:

kernel/lk/include/shared/lk/macros.h:

opensource_libs/libcxx/include/string.h:

opensource_libs/musl/include/string.h:

opensource_libs/musl/include/strings.h:

user/base/include/user/trusty_log.h:

opensource_libs/libcxx/include/stdio.h:

opensource_libs/musl/include/stdio.h:

kernel/lk/include/uapi/uapi/err.h:
