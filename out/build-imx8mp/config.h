#ifndef __out_build_imx8mp_config_h_H
#define __out_build_imx8mp_config_h_H
#define LK 1
#define __TRUSTY__ 1
#define TIMER_ARM_GENERIC_SELECTED CNTPS
#define WITH_NO_PHYS_RELOCATION 1
#define HEAP_GROW_SIZE 0X400000
#define MMU_IDENT_SIZE_SHIFT 38
#define APP_STORAGE_RPMB_BLOCK_COUNT 2048
#define STORAGE_TOTAL_RPMB_BLOCK_COUNT 8192
#define WITH_LIB_VERSION 1
#define ARM_GIC_USE_SYSTEM_REG 1
#define IMX8MP_LCDIF_INDEX 1
#define WITH_FFA_SUPPORT 1
#define CFG_DUTA 1
#define CFG_CANCELL 1
#define USE_IMX_MONOTONIC_TIME 1
#define MEMBASE 0X56000000
#define MEMSIZE 0X2000000
#define MMU_WITH_TRAMPOLINE 1
#define ARM64_CPU_ARMV8_A 1
#define ARM_ISA_ARMV8 1
#define IS_64BIT 1
#define ARCH_DEFAULT_STACK_SIZE 4096
#define ARCH_DEFAULT_SHADOW_STACK_SIZE 4096
#define WITH_SMP 1
#define SMP_MAX_CPUS 4
#define SMP_CPU_CLUSTER_SHIFT 2
#define SMP_CPU_ID_BITS 24
#define ARM_MERGE_FIQ_IRQ 1
#define ARCH_HAS_FIQ 1
#define KERNEL_ASPACE_BASE 0XFFFFFFFF00000000
#define KERNEL_ASPACE_SIZE 0X100000000
#define USER_ASPACE_BASE 0X0000000000008000
#define USER_ASPACE_SIZE 0X00000000F0000000
#define KERNEL_BASE 0XFFFFFFFF00000000
#define KERNEL_LOAD_OFFSET 0
#define MEMBASE 0X56000000
#define MEMSIZE 0X2000000
#define PLATFORM_HAS_DYNAMIC_TIMER 1
#define LK_LIBC_IMPLEMENTATION_IS_MUSL 1
#define WITH_LIB_RCTEE 1
#define WITH_RCTEE_IPC 1
#define WITH_WAIT_ANY_SUPPORT 1
#define WITH_SYSCALL_TABLE 1
#define WITH_LIB_SM 1
#define LIB_BACKTRACE_ENABLE 1
#define LK_HEAP_IMPLEMENTATION MINIHEAP
#define PROJECT_IMX8MP 1
#define PROJECT "IMX8MP"
#define TARGET_IMX8MP 1
#define TARGET "IMX8MP"
#define PLATFORM_IMX 1
#define PLATFORM "IMX"
#define ARCH_ARM64 1
#define ARCH "ARM64"
#define WITH_APP 1
#define WITH_BUILD_TOOLS_PACKAGE_TOOL 1
#define WITH_DEV 1
#define WITH_DEV_INTERRUPT_ARM_GIC 1
#define WITH_DEV_TIMER_ARM_GENERIC 1
#define WITH_KERNEL 1
#define WITH_KERNEL_RCTEE_APP_BUSYTEST 1
#define WITH_KERNEL_RCTEE_LIB_APP_MANIFEST 1
#define WITH_KERNEL_RCTEE_LIB_ARM_FFA 1
#define WITH_KERNEL_RCTEE_LIB_BACKTRACE 1
#define WITH_KERNEL_RCTEE_LIB_EXTMEM 1
#define WITH_KERNEL_RCTEE_LIB_KTIPC 1
#define WITH_KERNEL_RCTEE_LIB_KTIPC_TEST_MAIN 1
#define WITH_KERNEL_RCTEE_LIB_KTIPC_TEST_SRV 1
#define WITH_KERNEL_RCTEE_LIB_LIBC_EXT 1
#define WITH_KERNEL_RCTEE_LIB_LIBC_TRUSTY 1
#define WITH_KERNEL_RCTEE_LIB_MEMLOG 1
#define WITH_KERNEL_RCTEE_LIB_RAND 1
#define WITH_KERNEL_RCTEE_LIB_RCTEE 1
#define WITH_KERNEL_RCTEE_LIB_SM 1
#define WITH_KERNEL_RCTEE_LIB_SMC 1
#define WITH_KERNEL_RCTEE_LIB_SYSCALL 1
#define WITH_KERNEL_RCTEE_LIB_UBSAN 1
#define WITH_KERNEL_RCTEE_LIB_UNITTEST 1
#define WITH_KERNEL_RCTEE_LIB_VERSION 1
#define WITH_KERNEL_RCTEE_SERVICES_APPLOADER 1
#define WITH_KERNEL_RCTEE_SERVICES_GENERIC_TA_SERVICE 1
#define WITH_KERNEL_RCTEE_SERVICES_HWRNG 1
#define WITH_KERNEL_RCTEE_SERVICES_SMC 1
#define WITH_KERNEL_VM 1
#define WITH_LIB_BINARY_SEARCH_TREE 1
#define WITH_LIB_CBUF 1
#define WITH_LIB_DEBUG 1
#define WITH_LIB_FIXED_POINT 1
#define WITH_LIB_HEAP 1
#define WITH_LIB_HEAP_MINIHEAP 1
#define WITH_LIB_IO 1
#define WITH_PLATFORM 1
#define WITH_TARGET 1
#define WITH_USER_BASE_APP_APPLOADER_TESTS_CBOR_TEST 1
#define LK_DEBUGLEVEL 2
#define LK_LOGLEVEL 2
#define TLOG_LVL_DEFAULT 4
#define IPC_MAX_HANDLES 64
#define RELEASE_BUILD 0
#define ASLR 1
#define USER_SCS_ENABLED 1
#define KERNEL_SCS_ENABLED 1
#define GLOBAL_INCLUDES "_IOUT_BUILD_IMX8MP__IKERNEL_LK_INCLUDE_UAPI__IBUILD_TOOLS_INCLUDE_UAPI__IKERNEL_RCTEE_INCLUDE_UAPI__IUSER_BASE_INCLUDE_UAPI__IOPENSOURCE_LIBS_HEADERS_INCLUDE_UAPI__IKERNEL_RCTEE_PLATFORM_NXP_IMX8_INCLUDE_UAPI__IKERNEL_HARDWARE_NXP_INCLUDE_UAPI__IKERNEL_LK_EXTERNAL_INCLUDE_UAPI__IKERNEL_LK_INCLUDE_SHARED__IBUILD_TOOLS_INCLUDE_SHARED__IKERNEL_RCTEE_INCLUDE_SHARED__IUSER_BASE_INCLUDE_SHARED__IOPENSOURCE_LIBS_HEADERS_INCLUDE_SHARED__IKERNEL_RCTEE_PLATFORM_NXP_IMX8_INCLUDE_SHARED__IKERNEL_HARDWARE_NXP_INCLUDE_SHARED__IKERNEL_LK_EXTERNAL_INCLUDE_SHARED__IKERNEL_LK_INCLUDE__IBUILD_TOOLS_INCLUDE__IKERNEL_RCTEE_INCLUDE__IUSER_BASE_INCLUDE__IOPENSOURCE_LIBS_HEADERS_INCLUDE__IKERNEL_RCTEE_PLATFORM_NXP_IMX8_INCLUDE__IKERNEL_HARDWARE_NXP_INCLUDE__IKERNEL_LK_EXTERNAL_INCLUDE__IKERNEL_LK_INCLUDE_UAPI_UAPI__IBUILD_TOOLS_INCLUDE_UAPI_UAPI__IKERNEL_RCTEE_INCLUDE_UAPI_UAPI__IUSER_BASE_INCLUDE_UAPI_UAPI__IOPENSOURCE_LIBS_HEADERS_INCLUDE_UAPI_UAPI__IKERNEL_RCTEE_PLATFORM_NXP_IMX8_INCLUDE_UAPI_UAPI__IKERNEL_HARDWARE_NXP_INCLUDE_UAPI_UAPI__IKERNEL_LK_EXTERNAL_INCLUDE_UAPI_UAPI__IKERNEL_LK_INCLUDE_SHARED_LK__IBUILD_TOOLS_INCLUDE_SHARED_LK__IKERNEL_RCTEE_INCLUDE_SHARED_LK__IUSER_BASE_INCLUDE_SHARED_LK__IOPENSOURCE_LIBS_HEADERS_INCLUDE_SHARED_LK__IKERNEL_RCTEE_PLATFORM_NXP_IMX8_INCLUDE_SHARED_LK__IKERNEL_HARDWARE_NXP_INCLUDE_SHARED_LK__IKERNEL_LK_EXTERNAL_INCLUDE_SHARED_LK__IKERNEL_HARDWARE_NXP_PLATFORM_IMX_COMMON_INCLUDE__IKERNEL_HARDWARE_NXP_PLATFORM_IMX_COMMON_INCLUDE_PLATFORM__IKERNEL_HARDWARE_NXP_PLATFORM_IMX_SOC_IMX8MP_INCLUDE__IKERNEL_HARDWARE_NXP_PLATFORM_IMX_INCLUDE__IKERNEL_LK_ARCH_ARM64_INCLUDE__IKERNEL_LK_TOP_INCLUDE__IKERNEL_LK_APP_INCLUDE__IKERNEL_LK_DEV_INCLUDE__IKERNEL_LK_DEV_INTERRUPT_ARM_GIC_INCLUDE__IKERNEL_LK_DEV_TIMER_ARM_GENERIC_INCLUDE__IKERNEL_LK_KERNEL_INCLUDE__IKERNEL_RCTEE_APP_BUSYTEST_INCLUDE__IKERNEL_RCTEE_LIB_KTIPC_TEST_MAIN_INCLUDE__IKERNEL_RCTEE_LIB_KTIPC_TEST_SRV_INCLUDE__IKERNEL_RCTEE_LIB_MEMLOG_INCLUDE__IKERNEL_RCTEE_LIB_RCTEE_INCLUDE__IKERNEL_RCTEE_LIB_RCTEE_INCLUDE__IKERNEL_RCTEE_LIB_SM_INCLUDE__IUSER_BASE_INTERFACE_SMC_INCLUDE__IKERNEL_RCTEE_LIB_SM_INCLUDE__IKERNEL_RCTEE_LIB_UBSAN_INCLUDE__IKERNEL_RCTEE_LIB_VERSION_INCLUDE__IKERNEL_RCTEE_LIB_VERSION_INCLUDE__IUSER_BASE_INTERFACE_APPLOADER_INCLUDE__IKERNEL_RCTEE_SERVICES_APPLOADER_INCLUDE__IKERNEL_RCTEE_SERVICES_GENERIC_TA_SERVICE_INCLUDE__IUSER_BASE_INTERFACE_HWRNG_INCLUDE__IKERNEL_RCTEE_SERVICES_HWRNG_INCLUDE__IKERNEL_RCTEE_SERVICES_SMC_INCLUDE__IKERNEL_LK_PLATFORM_INCLUDE__IKERNEL_LK_TARGET_INCLUDE__IKERNEL_RCTEE_LIB_APP_MANIFEST_INCLUDE__IKERNEL_RCTEE_LIB_ARM_FFA_INCLUDE__IUSER_BASE_INTERFACE_ARM_FFA_INCLUDE__IKERNEL_RCTEE_LIB_ARM_FFA_INCLUDE__IKERNEL_RCTEE_LIB_BACKTRACE_INCLUDE__IKERNEL_RCTEE_LIB_EXTMEM_INCLUDE__IKERNEL_RCTEE_LIB_EXTMEM_INCLUDE__IKERNEL_RCTEE_LIB_KTIPC_INCLUDE__IKERNEL_RCTEE_LIB_LIBC_EXT_INCLUDE__IKERNEL_LK_LIB_LIBC_INCLUDE_COMMON__IKERNEL_RCTEE_LIB_LIBC_TRUSTY_INCLUDE__IKERNEL_RCTEE_LIB_RAND_INCLUDE__IKERNEL_RCTEE_LIB_RAND_INCLUDE__IKERNEL_RCTEE_LIB_SMC_INCLUDE__IKERNEL_RCTEE_LIB_SMC_INCLUDE__IKERNEL_RCTEE_LIB_SYSCALL_INCLUDE__IKERNEL_RCTEE_LIB_SYSCALL_INCLUDE__IKERNEL_RCTEE_LIB_UNITTEST_INCLUDE___IKERNEL_RCTEE_LIB_UNITTEST_INCLUDE__IKERNEL_LK_KERNEL_VM_INCLUDE__IKERNEL_LK_LIB_DEBUG_INCLUDE__IKERNEL_LK_LIB_FIXED_POINT_INCLUDE__IKERNEL_LK_LIB_HEAP_INCLUDE__IKERNEL_LK_LIB_HEAP_INCLUDE__IKERNEL_LK_LIB_BINARY_SEARCH_TREE_INCLUDE__IKERNEL_LK_LIB_HEAP_MINIHEAP_INCLUDE__IKERNEL_LK_LIB_HEAP_MINIHEAP_INCLUDE__IKERNEL_LK_LIB_IO_INCLUDE__IKERNEL_LK_LIB_CBUF_INCLUDE"
#define GLOBAL_COMPILEFLAGS "_GLLDB__FDEBUG_MACRO__INCLUDE_OUT_BUILD_IMX8MP_CONFIG_H__WERROR__WALL__WSIGN_COMPARE__WNO_MULTICHAR__WNO_UNUSED_FUNCTION__WNO_UNUSED_LABEL__FNO_SHORT_ENUMS__FNO_COMMON__FNO_OMIT_FRAME_POINTER__WIMPLICIT_FALLTHROUGH__WVLA__FFUNCTION_SECTIONS__FDATA_SECTIONS__U__LINUX_____SYSROOT FAKE_SYSROOT__FINLINE__ISYSTEM_OPENSOURCE_LIBS_MUSL_ARCH_AARCH64__ISYSTEM_OPENSOURCE_LIBS_MUSL_ARCH_GENERIC__ISYSTEM_OPENSOURCE_LIBS_MUSL_INCLUDE___D_ALL_SOURCE__FPIE__FVISIBILITY HIDDEN"
#define GLOBAL_OPTFLAGS "_O2"
#define GLOBAL_CFLAGS "__STD C17__WSTRICT_PROTOTYPES__WWRITE_STRINGS_"
#define GLOBAL_CPPFLAGS "__STD CPP17__FNO_EXCEPTIONS__FNO_RTTI__FNO_THREADSAFE_STATICS__WNO_C99_DESIGNATOR_"
#define GLOBAL_ASMFLAGS "_DASSEMBLY_"
#define GLOBAL_LDFLAGS "___UNDEFINED __AEABI_UNWIND_CPP_PR0___GC_SECTIONS___LKERNEL_LK__LBUILD_TOOLS__LKERNEL_RCTEE__LUSER_BASE__LOPENSOURCE_LIBS_HEADERS__LKERNEL_RCTEE_PLATFORM_NXP_IMX8__LKERNEL_HARDWARE_NXP__LKERNEL_LK_EXTERNAL___WHOLE_ARCHIVE__Z_MAX_PAGE_SIZE 4096__PIE___NO_DYNAMIC_LINKER__Z_TEXT__BSYMBOLIC___PACK_DYN_RELOCS RELR___USE_ANDROID_RELR_TAGS"
#define ARCH_COMPILEFLAGS "__MGENERAL_REGS_ONLY__DWITH_NO_FP 1__FFIXED_X18___TARGET_AARCH64_LINUX_GNU"
#define ARCH_CFLAGS ""
#define ARCH_CPPFLAGS ""
#define ARCH_ASMFLAGS ""
#endif
