ffffffff00001a20 0000000000000000 t .confEL1
ffffffff00001a10 0000000000000000 t .inEL2
ffffffff000019e8 0000000000000000 t .notEL1
ffffffff00107000 0000000000000000 r .ta.body.end
ffffffff0012f000 0000000000000000 r .ta.body.end
ffffffff00140000 0000000000000000 r .ta.body.end
ffffffff0014d000 0000000000000000 r .ta.body.end
ffffffff00044000 0000000000000000 r .ta.body.start
ffffffff00107000 0000000000000000 r .ta.body.start
ffffffff0012f000 0000000000000000 r .ta.body.start
ffffffff00140000 0000000000000000 r .ta.body.start
ffffffff00043600 0000000000000000 r .ta.manifest.end
ffffffff00043644 0000000000000000 r .ta.manifest.end
ffffffff000436bc 0000000000000000 r .ta.manifest.end
ffffffff00043764 0000000000000000 r .ta.manifest.end
ffffffff000435b0 0000000000000000 r .ta.manifest.start
ffffffff00043600 0000000000000000 r .ta.manifest.start
ffffffff00043644 0000000000000000 r .ta.manifest.start
ffffffff000436bc 0000000000000000 r .ta.manifest.start
ffffffff0014d498 0000000000000000 d _DYNAMIC
ffffffff0014d080 0000000000000000 R __apps_end
ffffffff0014d080 0000000000000000 R __apps_start
ffffffff0015c110 0000000000000000 B __bss_end
ffffffff00150000 0000000000000000 B __bss_start
ffffffff0003715c 0000000000000000 R __code_end
ffffffff00000000 0000000000000000 T __code_start
ffffffff0014d498 0000000000000000 R __ctor_end
ffffffff0014d498 0000000000000000 R __ctor_list
ffffffff0014fc58 0000000000000000 D __data_end
ffffffff0014e000 0000000000000000 D __data_start
ffffffff0014e000 0000000000000000 D __data_start_rom
ffffffff0014fc58 0000000000000000 D __devices
ffffffff0014fc58 0000000000000000 D __devices_end
ffffffff0014d080 0000000000000000 R __drivers
ffffffff0014d080 0000000000000000 R __drivers_end
ffffffff0014d498 0000000000000000 R __dtor_end
ffffffff0014d498 0000000000000000 R __dtor_list
ffffffff0003715c 0000000000000000 R __exidx_end
ffffffff0003715c 0000000000000000 R __exidx_start
ffffffff00038060 0000000000000000 R __fault_handler_table_end
ffffffff00038000 0000000000000000 R __fault_handler_table_start
ffffffff0014d080 0000000000000000 D __lk_init
ffffffff0014d3f8 0000000000000000 D __lk_init_end
ffffffff00159000 0000000000000000 B __post_prebss_bss_start
ffffffff0014d080 0000000000000000 R __rctee_app_list_end
ffffffff0014d000 0000000000000000 R __rctee_app_list_start
ffffffff0014d498 0000000000000000 R __relr_end
ffffffff0014d3f8 0000000000000000 R __relr_start
ffffffff0014d5e0 0000000000000000 D __rodata_end
ffffffff00038000 0000000000000000 R __rodata_start
ffffffff00154000 0000000000000000 B __shadow_stack
ffffffff00150000 0000000000000000 B __stack
ffffffff00154000 0000000000000000 B __stack_end
                                  U __system_property_get
ffffffff0015d000 0000000000000000 B _end
ffffffff02000000 0000000000000000 B _end_of_ram
ffffffff00000000 0000000000000000 T _start
ffffffff00001a94 0000000000000000 T arch_clean_cache_range
ffffffff00001ab4 0000000000000000 T arch_clean_invalidate_cache_range
ffffffff00001b50 0000000000000000 T arch_copy_from_user
ffffffff00001b30 0000000000000000 T arch_copy_to_user
ffffffff00001ad4 0000000000000000 T arch_invalidate_cache_range
ffffffff00001a6c 0000000000000000 T arch_spin_lock
ffffffff00001a54 0000000000000000 T arch_spin_trylock
ffffffff00001a8c 0000000000000000 T arch_spin_unlock
ffffffff00001b70 0000000000000000 T arch_strlcpy_from_user
ffffffff00001af4 0000000000000000 T arch_sync_cache_range
ffffffff00001934 0000000000000000 T arm64_context_switch
ffffffff000002e8 0000000000000000 W arm64_curr_cpu_num
ffffffff00001994 0000000000000000 T arm64_el3_to_el1
ffffffff000019d8 0000000000000000 T arm64_elX_to_el1
ffffffff000018a8 0000000000000000 T arm64_enter_uspace
ffffffff00001180 0000000000000000 t arm64_err_exc_current_el_SP0
ffffffff00001380 0000000000000000 t arm64_err_exc_current_el_SPx
ffffffff00001780 0000000000000000 t arm64_err_exc_lower_el_32
ffffffff00001580 0000000000000000 t arm64_err_exc_lower_el_64
ffffffff00001860 0000000000000000 t arm64_exc_shared_restore_short
ffffffff00001000 0000000000000000 T arm64_exception_base
ffffffff00001100 0000000000000000 t arm64_fiq_current_el_SP0
ffffffff00001300 0000000000000000 t arm64_fiq_current_el_SPx
ffffffff00001700 0000000000000000 t arm64_fiq_lower_el_32
ffffffff00001500 0000000000000000 t arm64_fiq_lower_el_64
ffffffff00001080 0000000000000000 t arm64_irq_current_el_SP0
ffffffff00001280 0000000000000000 t arm64_irq_current_el_SPx
ffffffff00001680 0000000000000000 t arm64_irq_lower_el_32
ffffffff00001480 0000000000000000 t arm64_irq_lower_el_64
ffffffff00001000 0000000000000000 t arm64_sync_exc_current_el_SP0
ffffffff00001200 0000000000000000 t arm64_sync_exc_current_el_SPx
ffffffff00001600 0000000000000000 t arm64_sync_exc_lower_el_32
ffffffff00001400 0000000000000000 t arm64_sync_exc_lower_el_64
ffffffff00001800 0000000000000000 t arm64_sync_exc_shared
ffffffff00003260 0000000000000000 t arm64_syscall
ffffffff00000000 0000000000000000 T arm_reset
ffffffff00001bb4 0000000000000000 T copy_from_anywhere
                                  U getauxval
ffffffff0014e000 0000000000000000 d mmu_on_vaddr_ptr
ffffffff0014e00c 0000000000000000 D page_tables_not_ready
ffffffff0000321c 0000000000000000 T platform_early_halt
ffffffff000031f0 0000000000000000 T sm_sched_nonsecure
ffffffff00003240 0000000000000000 T smc8
ffffffff00158000 0000000000000000 B sp_el1_bufs
ffffffff00001bf0 0000000000000000 T tag_for_addr_
ffffffff00158800 0000000000000000 B tt_trampoline
ffffffff0014e008 0000000000000000 D tt_trampoline_not_ready
ffffffff00159108 0000000000000001 b __aarch64_have_lse_atomics
ffffffff00036e1c 0000000000000001 t __typeid__ZTSF14handler_returnP5timeryPvE_global_addr
ffffffff00036e10 0000000000000001 t __typeid__ZTSF14handler_returnPvE_global_addr
ffffffff00036e28 0000000000000001 t __typeid__ZTSF14handler_returnPvyE_global_addr
ffffffff00036e48 0000000000000001 t __typeid__ZTSFbP8unittestE_global_addr
ffffffff00036f0c 0000000000000001 t __typeid__ZTSFiP16rctee_virtio_busE_global_addr
ffffffff00036ed4 0000000000000001 t __typeid__ZTSFiP4vdevE_global_addr
ffffffff00036ed0 0000000000000001 t __typeid__ZTSFiP4vdevPvE_global_addr
ffffffff00036ed8 0000000000000001 t __typeid__ZTSFiP4vdevjE_global_addr
ffffffff00036e98 0000000000000001 t __typeid__ZTSFiP6handlemmjPmE_global_addr
ffffffff00036f04 0000000000000001 t __typeid__ZTSFiP6vqueuePvE_global_addr
ffffffff00036edc 0000000000000001 t __typeid__ZTSFiP7vmm_objPjE_global_addr
ffffffff00036ef0 0000000000000001 t __typeid__ZTSFiP7vmm_objmPmS1_E_global_addr
ffffffff00036e9c 0000000000000001 t __typeid__ZTSFiP9rctee_appE_global_addr
ffffffff0003705c 0000000000000001 t __typeid__ZTSFiPK10ktipc_portP6handlePK4uuidPPvE_global_addr
ffffffff00037074 0000000000000001 t __typeid__ZTSFiPK10ktipc_portP6handlePvE_global_addr
ffffffff00036ec0 0000000000000001 t __typeid__ZTSFiPhmPvE_global_addr
ffffffff00036f88 0000000000000001 t __typeid__ZTSFiPvE_global_addr
ffffffff00036e04 0000000000000001 t __typeid__ZTSFijE_global_addr
ffffffff00036e08 0000000000000001 t __typeid__ZTSFijjmE_global_addr
ffffffff00036e7c 0000000000000001 t __typeid__ZTSFjP6handlejbE_global_addr
ffffffff00036f28 0000000000000001 t __typeid__ZTSFlP10smc32_argsE_global_addr
ffffffff00036ecc 0000000000000001 t __typeid__ZTSFlP4vdevPvE_global_addr
ffffffff00036e2c 0000000000000001 t __typeid__ZTSFlP9io_handlePKcmE_global_addr
ffffffff00036e30 0000000000000001 t __typeid__ZTSFlP9io_handlePcmE_global_addr
ffffffff00036ea4 0000000000000001 t __typeid__ZTSFljmjE_global_addr
ffffffff00036ec8 0000000000000001 t __typeid__ZTSFmP4vdevE_global_addr
ffffffff00036e40 0000000000000001 t __typeid__ZTSFvP12ktipc_serverP18ksrv_event_handlerjE_global_addr
ffffffff00036ebc 0000000000000001 t __typeid__ZTSFvP12phys_mem_objE_global_addr
ffffffff00036e70 0000000000000001 t __typeid__ZTSFvP13handle_waiterE_global_addr
ffffffff00036e6c 0000000000000001 t __typeid__ZTSFvP16__print_callbackE_global_addr
ffffffff00036e68 0000000000000001 t __typeid__ZTSFvP16__print_callbackPKcmE_global_addr
ffffffff00036e88 0000000000000001 t __typeid__ZTSFvP6handleE_global_addr
ffffffff00036f10 0000000000000001 t __typeid__ZTSFvP7vmm_objE_global_addr
ffffffff00036e34 0000000000000001 t __typeid__ZTSFvP9io_handleE_global_addr
ffffffff00037094 0000000000000001 t __typeid__ZTSFvPvE_global_addr
ffffffff00036fc8 0000000000000001 t __typeid__ZTSFvjE_global_addr
ffffffff00036e4c 0000000000000001 t __typeid__ZTSFvvE_global_addr
ffffffff0015bd30 0000000000000001 b _test_context.8
ffffffff0015bd38 0000000000000001 b _test_context.9
ffffffff0015bd8c 0000000000000001 b apps_started
ffffffff00159158 0000000000000001 b arm64_mte_enabled
ffffffff0015bcc8 0000000000000001 b arm_ffa_init_is_success
ffffffff00159990 0000000000000001 b arm_gics.0
ffffffff00159998 0000000000000001 b arm_gics.2
ffffffff0015ac18 0000000000000001 b boot_alloc_start
ffffffff00159128 0000000000000001 b caam_ready
ffffffff00159188 0000000000000001 b doorbell_enabled
ffffffff0015910c 0000000000000001 b no_console
ffffffff0015ac08 0000000000000001 b old_asid_active
ffffffff0015bf10 0000000000000001 b platform_halted
ffffffff0015bcd8 0000000000000001 b supports_ns_bit
ffffffff0015bcd9 0000000000000001 b supports_rx_release
ffffffff00159129 0000000000000001 b tee_ctrl_lcdif
ffffffff001599c8 0000000000000001 b timer_irq
ffffffff0015bcdc 0000000000000002 b ffa_local_id
ffffffff00036e30 0000000000000004 t __debug_stdio_read
ffffffff00036e2c 0000000000000004 t __debug_stdio_write
ffffffff0015be28 0000000000000004 b _dev_cnt
ffffffff0015bcf0 0000000000000004 b _test_context.0
ffffffff0015bcf8 0000000000000004 b _test_context.1
ffffffff0015bd00 0000000000000004 b _test_context.2
ffffffff0015bd88 0000000000000004 b _uctx_slot_id
ffffffff0015bd90 0000000000000004 b als_slot_cnt
ffffffff00006d48 0000000000000004 t app_thread_entry.cfi
ffffffff00010768 0000000000000004 t arch_curr_cpu_num
ffffffff0015bf40 0000000000000004 b boot_args_refcnt
ffffffff0015bd7c 0000000000000004 b close_counter
ffffffff00036e04 0000000000000004 t default_access_policy
ffffffff00036ebc 0000000000000004 t destroy_app_phys_mem
ffffffff0015999c 0000000000000004 b enabled_spi_mask.0
ffffffff001599a0 0000000000000004 b enabled_spi_mask.1
ffffffff001599a4 0000000000000004 b enabled_spi_mask.2
ffffffff001599a8 0000000000000004 b enabled_spi_mask.3
ffffffff0001795c 0000000000000004 t free
ffffffff0003659c 0000000000000004 t generic_ta_service_handle_channel_cleanup.cfi
ffffffff0015be48 0000000000000004 b irq_thread_ready
ffffffff00159130 0000000000000004 b last_linux_fb_addr
ffffffff0015912c 0000000000000004 b last_tee_fb_addr
ffffffff0014e728 0000000000000004 d lock_held_by
ffffffff0001790c 0000000000000004 t memalign
ffffffff00036e6c 0000000000000004 t memlog_commit_callback
ffffffff00036e68 0000000000000004 t memlog_print_callback
ffffffff00036e98 0000000000000004 t memref_mmap
ffffffff0001f3d0 0000000000000004 t nop_handle_channel_cleanup.cfi
ffffffff0015bc98 0000000000000004 b print_saved_state
ffffffff0014e010 0000000000000004 d randseed
ffffffff00036ec8 0000000000000004 t rcipc_descr_size
ffffffff00036ecc 0000000000000004 t rcipc_get_vdev_descr
ffffffff00036f0c 0000000000000004 t rcipc_init
ffffffff00036ed8 0000000000000004 t rcipc_vdev_kick_vq
ffffffff00036ed0 0000000000000004 t rcipc_vdev_probe
ffffffff00036ed4 0000000000000004 t rcipc_vdev_reset
ffffffff0015bda0 0000000000000004 b rctee_next_app_id
ffffffff00036e48 0000000000000004 t run_ktipctest
ffffffff0015a9d0 0000000000000004 b run_queue_bitmap
ffffffff00159134 0000000000000004 b secondaries_to_init
ffffffff0015bc9c 0000000000000004 b secondary_bootstrap_thread_count
ffffffff0015be44 0000000000000004 b sm_api_version
ffffffff0014f000 0000000000000004 d sm_api_version_max
ffffffff0015be40 0000000000000004 b sm_api_version_min
ffffffff00036e00 0000000000000004 t smc_service_handle_channel_cleanup.cfi
ffffffff0014e6d0 0000000000000004 d thread_lock_owner
ffffffff00036e28 0000000000000004 t timer_tick
ffffffff00002ca0 0000000000000008 t __stdio_close
ffffffff00002de8 0000000000000008 t __stdio_seek
ffffffff0015be30 0000000000000008 b _dev_list_lock
ffffffff00036ec0 0000000000000008 t _send_buf
ffffffff0015bd08 0000000000000008 b _test_context.3
ffffffff0015bd10 0000000000000008 b _test_context.4
ffffffff0015bd18 0000000000000008 b _test_context.5
ffffffff0015bd20 0000000000000008 b _test_context.6
ffffffff0015bd28 0000000000000008 b _test_context.7
ffffffff00036ea0 0000000000000008 t _uctx_shutdown
ffffffff00036e9c 0000000000000008 t _uctx_startup
ffffffff0014fa78 0000000000000008 d apploader_service_uuids
ffffffff0014e510 0000000000000008 d arm_boot_cpu_lock
ffffffff00007cb0 0000000000000008 t arm_ipi_generic_handler.cfi
ffffffff0015ac10 0000000000000008 b aux_slock
ffffffff0014eb78 0000000000000008 d bad_uuids
ffffffff0014e668 0000000000000008 d boot_alloc_end
ffffffff0015bf38 0000000000000008 b boot_args
ffffffff00159080 0000000000000008 b buf
ffffffff00036e44 0000000000000008 t chan_event_handler
ffffffff0015be20 0000000000000008 b conn_req_thread
ffffffff00003438 0000000000000008 t default_access_policy.cfi
ffffffff0014e730 0000000000000008 d early_log_end
ffffffff0014e6d8 0000000000000008 d early_log_writeptr
ffffffff0015bce8 0000000000000008 b ffa_buf_size
ffffffff0015bcd0 0000000000000008 b ffa_rx
ffffffff0015bce0 0000000000000008 b ffa_tx
ffffffff0015bd98 0000000000000008 b g_apploader_chandle
ffffffff00159118 0000000000000008 b g_job
ffffffff00159110 0000000000000008 b g_rings
ffffffff00159180 0000000000000008 b gicd_lock
ffffffff0015bfc0 0000000000000008 b ipc_printf_handle
ffffffff0015ac00 0000000000000008 b last_asid
ffffffff0015bf48 0000000000000008 b lk_boot_args.0
ffffffff0015bf50 0000000000000008 b lk_boot_args.1
ffffffff0015bf58 0000000000000008 b lk_boot_args.2
ffffffff0015bf60 0000000000000008 b lk_boot_args.3
ffffffff0015bd80 0000000000000008 b log_lock
ffffffff0001f3c8 0000000000000008 t nop_handle_msg.cfi
ffffffff0014f958 0000000000000008 d nr_syscalls
ffffffff000127a8 0000000000000008 t pmm_vmm_obj_check_flags.cfi
ffffffff00036e40 0000000000000008 t port_event_handler
ffffffff0015ac90 0000000000000008 b print_spin_lock
ffffffff0002e674 0000000000000008 t rcipc_descr_size.cfi
ffffffff00036f08 0000000000000008 t rcipc_rx_vq_notify_cb
ffffffff00036f04 0000000000000008 t rcipc_tx_vq_notify_cb
ffffffff0015c080 0000000000000008 b rng_data_avail_count
ffffffff0015c108 0000000000000008 b rng_data_avail_pos
ffffffff0015bf98 0000000000000008 b sched_shared_datalock
ffffffff0015bf90 0000000000000008 b sched_shared_mem
ffffffff0015be38 0000000000000008 b sm_api_version_lock
ffffffff000341b4 0000000000000008 t sm_mem_obj_compat_destroy.cfi
ffffffff00032ec8 0000000000000008 t smc_get_smp_max_cpus.cfi
ffffffff00033330 0000000000000008 t smc_nop_stdcall.cfi
ffffffff00159120 0000000000000008 b sram_base
ffffffff0015bf88 0000000000000008 b stdcallthread
ffffffff00036e08 0000000000000008 t sys_caam_ioctl
ffffffff00036e0c 0000000000000008 t sys_csu_ioctl
ffffffff001599c0 0000000000000008 b t_callback
ffffffff0015ac20 0000000000000008 b thread_lock
ffffffff0000ff10 0000000000000008 t thread_preempt
ffffffff0015a9d8 0000000000000008 b timer_lock
ffffffff00036ec4 0000000000000008 t tx_data_cb
ffffffff0015bfc8 0000000000000008 b unittest_handle_set
ffffffff0015bfd0 0000000000000008 b unittest_thread
ffffffff0014ead8 0000000000000008 d valid_uuids
ffffffff000149e0 0000000000000008 t vmm_res_obj_check_flags.cfi
ffffffff000156dc 0000000000000008 t vmm_res_obj_get_page.cfi
ffffffff00036e38 000000000000000c t __debug_stdio_lock
ffffffff00036e3c 000000000000000c t __debug_stdio_unlock
ffffffff00036e34 000000000000000c t __debug_stdio_write_commit
ffffffff00036e10 000000000000000c t arm_ipi_generic_handler
ffffffff00036e14 000000000000000c t arm_ipi_reschedule_handler
ffffffff00036e80 000000000000000c t chan_poll
ffffffff001599e0 000000000000000c b cntpct_per_ns
ffffffff00036e70 000000000000000c t handle_event_waiter_notify
ffffffff00036e78 000000000000000c t handle_event_waiter_notify.991
ffffffff00021854 000000000000000c t handle_event_waiter_notify.991.cfi
ffffffff00020568 000000000000000c t handle_event_waiter_notify.cfi
ffffffff00036e7c 000000000000000c t hset_poll
ffffffff00036e74 000000000000000c t hset_waiter_notify
ffffffff00017900 000000000000000c t malloc
ffffffff0015aa60 000000000000000c b mp
ffffffff001599f0 000000000000000c b ms_per_cntpct
ffffffff001599d0 000000000000000c b ns_per_cntpct
ffffffff00003338 000000000000000c t platform_dgetc
ffffffff00036e18 000000000000000c t platform_tick
ffffffff00036e84 000000000000000c t port_poll
ffffffff000029b8 000000000000000c t srand
ffffffff00036e24 000000000000000c t thread_sleep_handler
ffffffff00036e1c 000000000000000c t thread_timer_callback
ffffffff00036e20 000000000000000c t wait_queue_timeout_handler
ffffffff0014ef68 0000000000000010 d _dev_list
ffffffff0014e7d8 0000000000000010 d _test_list
ffffffff0014e9a0 0000000000000010 d _test_param_list
ffffffff0014ee48 0000000000000010 d allowed_mmio_ranges_list
ffffffff0014ee58 0000000000000010 d app_notifier_list
ffffffff00043530 0000000000000010 r apploader_user_uuid
ffffffff0014e530 0000000000000010 d arena_list
ffffffff0014e670 0000000000000010 d aspace_list
ffffffff00036e8c 0000000000000010 t chan_handle_destroy
ffffffff0014e718 0000000000000010 d console_io
ffffffff0015a9c0 0000000000000010 b cpu_priority
ffffffff00159b50 0000000000000010 b dead_threads
ffffffff001599ac 0000000000000010 b enabled_ppi_mask
ffffffff0015c070 0000000000000010 b generic_ta_service_handle_get_property_string.version_str
ffffffff00042794 0000000000000010 r hextable
ffffffff000427a4 0000000000000010 r hextable_caps
ffffffff00036e88 0000000000000010 t hset_destroy
ffffffff0014e430 0000000000000010 d hwcrypto_ta_uuid
ffffffff0014fbb0 0000000000000010 d hwrng_req_list
ffffffff0014e458 0000000000000010 d hwsecure_ta_uuid
ffffffff00006230 0000000000000010 t imx_linux_smcall_init.cfi
ffffffff0014e4c0 0000000000000010 d inout_buffer_paddr
ffffffff0014ecf0 0000000000000010 d ipc_port_list
ffffffff00043570 0000000000000010 r kernel_uuid
ffffffff0014eb98 0000000000000010 d log_list
ffffffff00036e94 0000000000000010 t memref_handle_destroy
ffffffff0002c8dc 0000000000000010 t mutex_acquire
ffffffff0014ef58 0000000000000010 d notify_cbs
ffffffff0000353c 0000000000000010 t platform_init_caam.cfi
ffffffff00006064 0000000000000010 t platform_init_csu.cfi
ffffffff00036e90 0000000000000010 t port_handle_destroy
ffffffff0014e6e0 0000000000000010 d print_callbacks
ffffffff0014ee68 0000000000000010 d rctee_app_list
ffffffff0014e468 0000000000000010 d secure_fb_impl_ta_uuid
ffffffff00003428 0000000000000010 t smc_load_access_policy
ffffffff0015bcb8 0000000000000010 b src_dst_ids
ffffffff00026a20 0000000000000010 t sys_dump_memory_info
ffffffff0002670c 0000000000000010 t sys_set_user_tls
ffffffff00159a40 0000000000000010 b thread_list
ffffffff0014eee0 0000000000000010 d virtio_bus_notifier_list
ffffffff00006c7c 0000000000000010 t vpu_enc_smcall_init.cfi
ffffffff000065e4 0000000000000010 t vpu_smcall_init.cfi
ffffffff0014ece0 0000000000000010 d waiting_for_port_chan_list
ffffffff0015abb0 0000000000000010 b write_port_list
ffffffff000434b4 0000000000000010 r zero_uuid
ffffffff00036ee8 0000000000000014 t ext_mem_obj_check_flags
ffffffff00036efc 0000000000000014 t ext_mem_obj_get_page
ffffffff00001d84 0000000000000014 t fd_io_handle
ffffffff00036edc 0000000000000014 t phys_mem_obj_check_flags
ffffffff00036ef0 0000000000000014 t phys_mem_obj_get_page
ffffffff00003344 0000000000000014 t platform_after_vm_init.cfi
ffffffff00036ee0 0000000000000014 t pmm_vmm_obj_check_flags
ffffffff00036ef4 0000000000000014 t pmm_vmm_obj_get_page
ffffffff00036eec 0000000000000014 t rcipc_ext_mem_check_flags
ffffffff00036f00 0000000000000014 t rcipc_ext_mem_get_page
ffffffff00033338 0000000000000014 t smc_nop_secure_monitor.cfi
ffffffff00025ce0 0000000000000014 t sys_exit_etc
ffffffff00036ee4 0000000000000014 t vmm_res_obj_check_flags
ffffffff00036ef8 0000000000000014 t vmm_res_obj_get_page
ffffffff00002ca8 0000000000000018 t __stdio_read
ffffffff0014d0b0 0000000000000018 D _init_struct_allowed_app_ranges
ffffffff0014d398 0000000000000018 D _init_struct_apploader
ffffffff0014d170 0000000000000018 D _init_struct_arm64_pan_init
ffffffff0014d278 0000000000000018 D _init_struct_arm_ffa_init
ffffffff0014d1d0 0000000000000018 D _init_struct_arm_generic_timer_init_secondary_cpu
ffffffff0014d200 0000000000000018 D _init_struct_arm_generic_timer_resume_cpu
ffffffff0014d1e8 0000000000000018 D _init_struct_arm_generic_timer_suspend_cpu
ffffffff0014d188 0000000000000018 D _init_struct_arm_gic_init_percpu
ffffffff0014d1b8 0000000000000018 D _init_struct_arm_gic_resume_cpu
ffffffff0014d1a0 0000000000000018 D _init_struct_arm_gic_suspend_cpu
ffffffff0014d260 0000000000000018 D _init_struct_busy_test_cpu_init
ffffffff0014d248 0000000000000018 D _init_struct_busy_test_init
ffffffff0014d0e0 0000000000000018 D _init_struct_caam_dev_init
ffffffff0014d0f8 0000000000000018 D _init_struct_csu_dev_init
ffffffff0014d3b0 0000000000000018 D _init_struct_generic_ta
ffffffff0014d3c8 0000000000000018 D _init_struct_hwrng_ktipc_server_init
ffffffff0014d0c8 0000000000000018 D _init_struct_imx_caam
ffffffff0014d110 0000000000000018 D _init_struct_imx_linux_driver
ffffffff0014d2a8 0000000000000018 D _init_struct_ktipc_test_server_init
ffffffff0014d290 0000000000000018 D _init_struct_ktipctest
ffffffff0014d308 0000000000000018 D _init_struct_librctee
ffffffff0014d2f0 0000000000000018 D _init_struct_librctee_apps
ffffffff0014d350 0000000000000018 D _init_struct_libsm
ffffffff0014d368 0000000000000018 D _init_struct_libsm_bootargs
ffffffff0014d2c0 0000000000000018 D _init_struct_memlog
ffffffff0014d098 0000000000000018 D _init_struct_platform_after_vm
ffffffff0014d320 0000000000000018 D _init_struct_rctee_smcall
ffffffff0014d338 0000000000000018 D _init_struct_register_rcipc_init
ffffffff0014d380 0000000000000018 D _init_struct_shared_mem
ffffffff0014d3e0 0000000000000018 D _init_struct_smc
ffffffff0014d128 0000000000000018 D _init_struct_snvs_driver
ffffffff0014d080 0000000000000018 D _init_struct_uart_console
ffffffff0014d2d8 0000000000000018 D _init_struct_uctx
ffffffff0014d230 0000000000000018 D _init_struct_vm
ffffffff0014d218 0000000000000018 D _init_struct_vm_preheap
ffffffff0014d140 0000000000000018 D _init_struct_vpu_driver
ffffffff0014d158 0000000000000018 D _init_struct_vpu_enc_driver
ffffffff0003709c 0000000000000018 t apploader_service_handle_channel_cleanup
ffffffff00037064 0000000000000018 t apploader_service_handle_connect
ffffffff0014fa60 0000000000000018 d apploader_service_port_acl
ffffffff0014eb60 0000000000000018 d blocked_srv_port_acl
ffffffff0014e418 0000000000000018 d caam_ops
ffffffff0003705c 0000000000000018 t connecterr_handle_connect
ffffffff0014eb80 0000000000000018 d connecterr_srv_port_acl
ffffffff0014e2d0 0000000000000018 d console_entity
ffffffff000032b4 0000000000000018 t console_smcall_init.cfi
ffffffff0014e440 0000000000000018 d csu_ops
ffffffff0014edd0 0000000000000018 d fd_op
ffffffff000370a0 0000000000000018 t generic_ta_service_handle_channel_cleanup
ffffffff00037068 0000000000000018 t generic_ta_service_handle_connect
ffffffff00043540 0000000000000018 r generic_ta_service_port_acl
ffffffff000370a4 0000000000000018 t hwrng_handle_channel_cleanup
ffffffff0003706c 0000000000000018 t hwrng_handle_connect
ffffffff00043558 0000000000000018 r hwrng_srv_port_acl
ffffffff0014e478 0000000000000018 d imx_linux_entity
ffffffff00037094 0000000000000018 t nop_handle_channel_cleanup
ffffffff00036f10 0000000000000018 t phys_mem_obj_destroy
ffffffff0014e518 0000000000000018 d phys_mem_obj_ops
ffffffff00036f14 0000000000000018 t pmm_vmm_obj_destroy
ffffffff0014e578 0000000000000018 d pmm_vmm_obj_ops
ffffffff00036f1c 0000000000000018 t rcipc_ext_mem_destroy
ffffffff0014ef40 0000000000000018 d rcipc_ext_mem_ops
ffffffff0014ef78 0000000000000018 d register_rcipc_init.vb_notifier
ffffffff0015bca0 0000000000000018 b secondary_bootstrap_threads
ffffffff00036f20 0000000000000018 t sm_mem_obj_compat_destroy
ffffffff0014f730 0000000000000018 d sm_mem_obj_compat_ops
ffffffff00036f24 0000000000000018 t sm_mem_obj_destroy
ffffffff0014f718 0000000000000018 d sm_mem_obj_ops
ffffffff000370a8 0000000000000018 t smc_service_handle_channel_cleanup
ffffffff00037070 0000000000000018 t smc_service_handle_connect
ffffffff00043580 0000000000000018 r smc_service_port_acl
ffffffff0014e490 0000000000000018 d snvs_entity
ffffffff00003148 0000000000000018 t strlen
ffffffff00036eac 0000000000000018 t sys_readv
ffffffff0014edb8 0000000000000018 d sys_std_fd_op
ffffffff00036ea4 0000000000000018 t sys_std_writev
ffffffff00036eb0 0000000000000018 t sys_wait
ffffffff00036ea8 0000000000000018 t sys_writev
ffffffff00037098 0000000000000018 t test_handle_channel_cleanup
ffffffff00037060 0000000000000018 t test_handle_connect
ffffffff0014eac0 0000000000000018 d test_srv_port_acl
ffffffff00010660 0000000000000018 t thread_set_name
ffffffff00036eb4 0000000000000018 t uctx_handle_readv
ffffffff00036eb8 0000000000000018 t uctx_handle_writev
ffffffff00036f18 0000000000000018 t vmm_res_obj_destroy
ffffffff0014e6b8 0000000000000018 d vmm_res_obj_ops
ffffffff0014e4f8 0000000000000018 d vpu_enc_entity
ffffffff0014e4a8 0000000000000018 d vpu_entity
ffffffff0003680c 000000000000001c t hwrng_handle_channel_cleanup.cfi
ffffffff00036e4c 000000000000001c t initial_thread_func
ffffffff00036e54 000000000000001c t ktipctest_blockedport
ffffffff00036e64 000000000000001c t ktipctest_blockedsend
ffffffff00036e60 000000000000001c t ktipctest_close
ffffffff00036e50 000000000000001c t ktipctest_connecterr
ffffffff00036e58 000000000000001c t ktipctest_echo
ffffffff00036e5c 000000000000001c t ktipctest_echo8
ffffffff0002c8ec 000000000000001c t rctee_app_crash
ffffffff0002c660 000000000000001c t rctee_app_exit
ffffffff0000306c 000000000000001c t strcmp
ffffffff0014e908 000000000000001c d test_pattern
ffffffff0015abc0 0000000000000020 b active_asid_version
ffffffff0015abe0 0000000000000020 b active_aspace
ffffffff00037080 0000000000000020 t apploader_service_handle_msg
ffffffff00159160 0000000000000020 b arm64_kernel_translation_table
ffffffff00159138 0000000000000020 b current_fpstate
ffffffff0014e3c0 0000000000000020 d entropy
ffffffff00037084 0000000000000020 t generic_ta_service_handle_msg
ffffffff00037088 0000000000000020 t hwrng_handle_msg
ffffffff0003708c 0000000000000020 t hwrng_handle_send_unblocked
ffffffff000065c4 0000000000000020 t monotonic_time_s
ffffffff00037074 0000000000000020 t nop_handle_msg
ffffffff0015bf68 0000000000000020 b nsidlethreads
ffffffff0015bf18 0000000000000020 b nsirqthreads
ffffffff000314d8 0000000000000020 t rcipc_rx_vq_notify_cb.cfi
ffffffff000314b8 0000000000000020 t rcipc_tx_vq_notify_cb.cfi
ffffffff00159b60 0000000000000020 b reaper_wait_queue
ffffffff0015bfa0 0000000000000020 b shareinfo
ffffffff0003339c 0000000000000020 t smc_fiq_enter.cfi
ffffffff00037090 0000000000000020 t smc_service_handle_msg
ffffffff00003160 0000000000000020 t strncpy
ffffffff00037078 0000000000000020 t test_handle_msg
ffffffff0003707c 0000000000000020 t test_handle_send_unblocked
ffffffff00003440 0000000000000024 t add_app_ranges.cfi
ffffffff00042b28 0000000000000024 r decriptor_template_decap_dek_blob
ffffffff00042b04 0000000000000024 r decriptor_template_gen_dek_blob
ffffffff000029c4 0000000000000024 t rand
ffffffff00025dd0 0000000000000024 t sys_nanosleep
ffffffff0014ef18 0000000000000028 d _rcipc_dev_ops
ffffffff0014ede8 0000000000000028 d _uctx_notifier
ffffffff0014eef0 0000000000000028 d _virtio_bus
ffffffff0014fa38 0000000000000028 d apploader_service_ops
ffffffff0014fa10 0000000000000028 d apploader_service_port
ffffffff0014ea48 0000000000000028 d blocked_srv_port
ffffffff00019e3c 0000000000000028 t busy_test_busy_func.cfi
ffffffff0014ea98 0000000000000028 d connecterr_srv_ops
ffffffff0014ea70 0000000000000028 d connecterr_srv_port
ffffffff0014e6f0 0000000000000028 d console_io_hooks
ffffffff0014faf0 0000000000000028 d generic_ta_service_ops
ffffffff0014fac8 0000000000000028 d generic_ta_service_port
ffffffff0014fb88 0000000000000028 d hwrng_srv_ops
ffffffff0014fb60 0000000000000028 d hwrng_srv_port
ffffffff0014e4d0 0000000000000028 d inout_buffer_g2_paddr
ffffffff0014fc30 0000000000000028 d smc_service_ops
ffffffff0014fc08 0000000000000028 d smc_service_port
ffffffff0014ea20 0000000000000028 d test_srv_ops
ffffffff0014e9f8 0000000000000028 d test_srv_port
ffffffff000182f0 000000000000002c t __debug_stdio_read.cfi
ffffffff000032cc 000000000000002c t console_stdcall.cfi
ffffffff00031460 000000000000002c t rcipc_ext_mem_check_flags.cfi
ffffffff0003148c 000000000000002c t rcipc_ext_mem_get_page.cfi
ffffffff0014f998 000000000000002d d lk_version
ffffffff000427b8 0000000000000030 r __c_locale
ffffffff00042b4c 0000000000000030 r aes_decriptor_template_ecb_cbc
ffffffff0014ee78 0000000000000030 d app_mgr_event
ffffffff0014e770 0000000000000030 d busy_test_event
ffffffff0014e378 0000000000000030 d confirmation_ui_range
ffffffff00042c18 0000000000000030 r des_decriptor_template_ede_cbc
ffffffff0014ec18 0000000000000030 d hset_ops
ffffffff0014ec48 0000000000000030 d ipc_chan_handle_ops
ffffffff0014ec78 0000000000000030 d ipc_port_handle_ops
ffffffff0014e818 0000000000000030 d ktipctest_blockedport_node
ffffffff0014e8d8 0000000000000030 d ktipctest_blockedsend_node
ffffffff0014e8a8 0000000000000030 d ktipctest_close_node
ffffffff0014e7e8 0000000000000030 d ktipctest_connecterr_node
ffffffff0014e878 0000000000000030 d ktipctest_echo8_node
ffffffff0014e848 0000000000000030 d ktipctest_echo_node
ffffffff0014ed00 0000000000000030 d memref_handle_ops
ffffffff0014e348 0000000000000030 d oemcrypto_range
ffffffff0014f6e8 0000000000000030 d sm_stdcall_function_table
ffffffff00006340 0000000000000030 t snvs_smcall_init.cfi
ffffffff000031c0 0000000000000030 t strnlen
ffffffff00006d4c 0000000000000034 t arch_enter_uspace
ffffffff00001d98 0000000000000034 t file_io_handle
ffffffff0014ee10 0000000000000038 d apps_lock
ffffffff0014f008 0000000000000038 d boot_args_lock
ffffffff0014eba8 0000000000000038 d es_lock
ffffffff0014ed30 0000000000000038 d fd_lock
ffffffff0014e7a0 0000000000000038 d ffa_rxtx_buffer_lock
ffffffff0014ebe0 0000000000000038 d g_hset_lock
ffffffff00036828 0000000000000038 t hwrng_handle_send_unblocked.cfi
ffffffff0014eca8 0000000000000038 d ipc_port_lock
ffffffff0014e3e0 0000000000000038 d lock
ffffffff0014e540 0000000000000038 d lock.406
ffffffff0014e738 0000000000000038 d print_mutex
ffffffff0014e590 0000000000000038 d res_group_lock
ffffffff0014f640 0000000000000038 d smc_table_lock
ffffffff0014f960 0000000000000038 d unittest_lock
ffffffff0014eea8 0000000000000038 d virtio_bus_notifier_lock
ffffffff0014e680 0000000000000038 d vmm_lock
ffffffff00002b08 000000000000003c t _fprintf_output_func
ffffffff00001f28 000000000000003c t _printf_engine
ffffffff0001f38c 000000000000003c t connecterr_handle_connect.cfi
ffffffff0000297c 000000000000003c t longlong_to_hexstring
ffffffff0001184c 000000000000003c t mp_set_curr_cpu_active
ffffffff0000fed4 000000000000003c t spin_unlock_restore
ffffffff00042bd8 0000000000000040 r aes_decriptor_template_gcm
ffffffff00036fa4 0000000000000040 t app_mgr
ffffffff00036f88 0000000000000040 t app_thread_entry
ffffffff00036f90 0000000000000040 t bootstrap2
ffffffff00036f9c 0000000000000040 t busy_test_busy_func
ffffffff00036f98 0000000000000040 t busy_test_server
ffffffff00036fac 0000000000000040 t conn_req_thread_func
ffffffff0015bd3c 0000000000000040 b echo_buf
ffffffff00036fa0 0000000000000040 t ksrv_thread
ffffffff000032f8 0000000000000040 t platform_dputc
ffffffff0002e67c 0000000000000040 t rcipc_get_vdev_descr.cfi
ffffffff00036fb0 0000000000000040 t rcipc_rx_thread_func
ffffffff00036fb4 0000000000000040 t rcipc_tx_thread_func
ffffffff00036fa8 0000000000000040 t rctee_thread_startup
ffffffff00036f8c 0000000000000040 t reaper_thread_routine
ffffffff00159a00 0000000000000040 b saved_state
ffffffff00036f94 0000000000000040 t secondary_cpu_bootstrap2
ffffffff00036fbc 0000000000000040 t sm_irq_loop
ffffffff00036fb8 0000000000000040 t sm_stdcall_loop
ffffffff00036fc0 0000000000000040 t sm_wait_for_smcall
ffffffff00003180 0000000000000040 t strncmp
ffffffff00036fc4 0000000000000040 t unittest_loop
ffffffff00001ee4 0000000000000044 t _vsnprintf_output
ffffffff000079c0 0000000000000044 t arm64_invalid_exception
ffffffff000261a4 0000000000000044 t sys_munmap
ffffffff0014f9c8 0000000000000048 d apploader_ktipc_server
ffffffff00007c68 0000000000000048 t arm64_pan_init.cfi
ffffffff0014fa80 0000000000000048 d generic_ta_ktipc_server
ffffffff0014fb18 0000000000000048 d hwrng_ktipc_server
ffffffff0014e9b0 0000000000000048 d ktipc_test_server
ffffffff0014fbc0 0000000000000048 d smc_ktipc_server
ffffffff00034890 0000000000000048 t sys_undefined
ffffffff00017910 000000000000004c t calloc
ffffffff0001076c 000000000000004c t thread_secondary_cpu_entry
ffffffff0000a5ec 0000000000000050 t arm_generic_timer_suspend_cpu.cfi
ffffffff0002bd7c 0000000000000050 t destroy_app_phys_mem.cfi
ffffffff0003334c 0000000000000050 t smc_fastcall_secure_monitor.cfi
ffffffff0003361c 0000000000000050 t smc_get_version_str.cfi
ffffffff00033204 0000000000000050 t smc_stdcall_secure_monitor.cfi
ffffffff0014ed68 0000000000000050 d sys_fds
ffffffff00030b74 0000000000000054 t _go_online
ffffffff0000a63c 0000000000000054 t arm_generic_timer_resume_cpu.cfi
ffffffff00007cb8 0000000000000058 t arm_ipi_reschedule_handler.cfi
ffffffff00025880 0000000000000058 t install_sys_fd_handler
ffffffff00003088 0000000000000058 t strdup
ffffffff00042b7c 000000000000005c r aes_decriptor_template_ctr
ffffffff0000a858 000000000000005c t current_time_ns
ffffffff000119b0 000000000000005c t phys_mem_obj_destroy.cfi
ffffffff00031ff4 000000000000005c t register_rcipc_init.cfi
ffffffff0003590c 0000000000000060 t apploader_service_translate_error
ffffffff00036f28 0000000000000060 t console_stdcall
ffffffff00007960 0000000000000060 t dump_iframe
ffffffff00036f2c 0000000000000060 t imx_linux_fastcall
ffffffff00036f40 0000000000000060 t memlog_stdcall
ffffffff0014e2e8 0000000000000060 d ram_arena
ffffffff00036f44 0000000000000060 t rctee_sm_fastcall
ffffffff00036f48 0000000000000060 t rctee_sm_nopcall
ffffffff00036f4c 0000000000000060 t rctee_sm_stdcall
ffffffff00036f78 0000000000000060 t smc_cpu_resume
ffffffff00036f74 0000000000000060 t smc_cpu_suspend
ffffffff00036f6c 0000000000000060 t smc_fastcall_secure_monitor
ffffffff00036f70 0000000000000060 t smc_fiq_enter
ffffffff00036f54 0000000000000060 t smc_get_smp_max_cpus
ffffffff00036f7c 0000000000000060 t smc_get_version_str
ffffffff00036f3c 0000000000000060 t smc_intc_get_next_irq
ffffffff00036f68 0000000000000060 t smc_nop_secure_monitor
ffffffff00036f64 0000000000000060 t smc_nop_stdcall
ffffffff00036f60 0000000000000060 t smc_restart_stdcall
ffffffff00036f50 0000000000000060 t smc_sm_api_version
ffffffff00036f5c 0000000000000060 t smc_stdcall_secure_monitor
ffffffff00036f80 0000000000000060 t smc_trusty_sched_share_register
ffffffff00036f84 0000000000000060 t smc_trusty_sched_share_unregister
ffffffff00036f58 0000000000000060 t smc_undefined
ffffffff00036f30 0000000000000060 t snvs_fastcall
ffffffff00025cf4 0000000000000060 t sys_readv.cfi
ffffffff00025bb4 0000000000000060 t sys_writev.cfi
ffffffff0001f7c8 0000000000000060 t test_handle_channel_cleanup.cfi
ffffffff00036f38 0000000000000060 t vpu_enc_fastcall
ffffffff00036f34 0000000000000060 t vpu_fastcall
ffffffff00012b84 0000000000000064 t pmm_set_tagged
ffffffff00001c04 0000000000000068 t libc_state_thread_init
ffffffff00011948 0000000000000068 t phys_mem_obj_get_page.cfi
ffffffff000029e8 0000000000000068 t putchar
ffffffff000030e0 0000000000000068 t strlcpy
ffffffff0015ac28 0000000000000068 b theheap
ffffffff00013748 0000000000000068 t vaddr_to_aspace
ffffffff000434c4 000000000000006c r _descr0
ffffffff00019dd0 000000000000006c t busy_test_cpu_init.cfi
ffffffff0001f9f0 000000000000006c t parse_u8
ffffffff0002be68 000000000000006c t rctee_thread_startup.cfi
ffffffff0003366c 000000000000006c t shared_mem_init.cfi
ffffffff0001f3d4 000000000000006c t test_handle_connect.cfi
ffffffff00010418 000000000000006c t thread_sleep_until_ns
ffffffff0001396c 000000000000006c t vmm_obj_slice_bind
ffffffff00012be8 0000000000000070 t pmm_free
ffffffff0014f678 0000000000000070 d sm_fastcall_function_table
ffffffff0014ef90 0000000000000070 d stdcallstate
ffffffff00034df0 0000000000000074 t apploader_service_handle_connect.cfi
ffffffff00001c6c 0000000000000074 t libc_state_thread_free
ffffffff0015bda8 0000000000000078 b g_conn_req_queue
ffffffff0003669c 0000000000000078 t hwrng_handle_connect.cfi
ffffffff0014e928 0000000000000078 d ktipctest_init.test
ffffffff0002d2dc 0000000000000078 t rctee_sm_fastcall.cfi
ffffffff0000a7dc 000000000000007c t platform_set_oneshot_timer
ffffffff00025d54 000000000000007c t sys_ioctl
ffffffff00001e68 000000000000007c t vsnprintf
ffffffff00007be8 0000000000000080 t arch_context_switch
ffffffff00159000 0000000000000080 b buf
ffffffff00159088 0000000000000080 b buf
ffffffff0015c088 0000000000000080 b rng_data
ffffffff0015a9e0 0000000000000080 b timers
ffffffff00020b10 0000000000000084 t hset_destroy.cfi
ffffffff000184ec 0000000000000084 t io_write
ffffffff00005fe0 0000000000000084 t platform_random_get_bytes
ffffffff000201a8 0000000000000088 t handle_decref
ffffffff000185f8 0000000000000088 t io_lock
ffffffff00018680 0000000000000088 t io_unlock
ffffffff00018570 0000000000000088 t io_write_commit
ffffffff00036b98 0000000000000088 t smc_service_handle_connect.cfi
ffffffff0000a74c 0000000000000090 t platform_tick.cfi
ffffffff00036fd0 0000000000000094 t add_app_ranges
ffffffff0003704c 0000000000000094 t apploader_service_init
ffffffff00036ff0 0000000000000094 t arm64_pan_init
ffffffff0003701c 0000000000000094 t arm_ffa_init
ffffffff00037008 0000000000000094 t arm_generic_timer_init_secondary_cpu
ffffffff00037004 0000000000000094 t arm_generic_timer_resume_cpu
ffffffff00037000 0000000000000094 t arm_generic_timer_suspend_cpu
ffffffff00036ffc 0000000000000094 t arm_gic_init_percpu
ffffffff00036ff8 0000000000000094 t arm_gic_resume_cpu
ffffffff00036ff4 0000000000000094 t arm_gic_suspend_cpu
ffffffff00037018 0000000000000094 t busy_test_cpu_init
ffffffff00037014 0000000000000094 t busy_test_init
ffffffff00036fc8 0000000000000094 t console_smcall_init
ffffffff0001b50c 0000000000000094 t ext_mem_obj_get_page.cfi
ffffffff00037050 0000000000000094 t generic_ta_service_init
ffffffff00020a7c 0000000000000094 t hset_poll.cfi
ffffffff00037054 0000000000000094 t hwrng_ktipc_server_init
ffffffff00036fe0 0000000000000094 t imx_linux_smcall_init
ffffffff00036fd4 0000000000000094 t init_caam_env
ffffffff00037024 0000000000000094 t ktipc_test_server_init
ffffffff00037020 0000000000000094 t ktipctest_init
ffffffff000028e8 0000000000000094 t longlong_to_string
ffffffff00037028 0000000000000094 t memlog_init
ffffffff00036fcc 0000000000000094 t platform_after_vm_init
ffffffff00036fd8 0000000000000094 t platform_init_caam
ffffffff00036fdc 0000000000000094 t platform_init_csu
ffffffff00037034 0000000000000094 t rctee_init
ffffffff00037038 0000000000000094 t rctee_sm_init
ffffffff0003703c 0000000000000094 t register_rcipc_init
ffffffff00037048 0000000000000094 t shared_mem_init
ffffffff00037044 0000000000000094 t sm_init
ffffffff00037040 0000000000000094 t sm_release_boot_args
ffffffff00037058 0000000000000094 t smc_service_init
ffffffff00036fe4 0000000000000094 t snvs_smcall_init
ffffffff00037030 0000000000000094 t start_apps
ffffffff0003702c 0000000000000094 t uctx_init
ffffffff00037010 0000000000000094 t vm_init_postheap
ffffffff0003700c 0000000000000094 t vm_init_preheap
ffffffff00036fec 0000000000000094 t vpu_enc_smcall_init
ffffffff00036fe8 0000000000000094 t vpu_smcall_init
ffffffff0015bfd8 0000000000000098 b _kernel_aspace
ffffffff0001fa5c 0000000000000098 t memlog_init.cfi
ffffffff0002bdcc 000000000000009c t rctee_app_find_by_uuid_locked
ffffffff00001dcc 000000000000009c t snprintf
ffffffff00002c04 000000000000009c t vprintf
ffffffff000209dc 00000000000000a0 t _finish_wait_handle
ffffffff00005c68 00000000000000a0 t caam_get_keybox
ffffffff0014e5c8 00000000000000a0 d mmu_initial_mappings
ffffffff00001ce0 00000000000000a4 t rctee_writev
ffffffff0002438c 00000000000000a8 t ipc_send_msg
ffffffff00025df4 00000000000000a8 t sys_gettime
ffffffff00022244 00000000000000ac t port_poll.cfi
ffffffff000327b0 00000000000000ac t sm_irq_loop.cfi
ffffffff00032e1c 00000000000000ac t smc_sm_api_version.cfi
ffffffff0001369c 00000000000000ac t vaddr_to_paddr
ffffffff0002c9dc 00000000000000b0 t vqueue_signal_avail
ffffffff00021ebc 00000000000000b4 t chan_shutdown_locked
ffffffff0002c0e8 00000000000000b4 t rctee_app_allow_mmio_range
ffffffff00009e38 00000000000000b8 t arm_gic_init_percpu.cfi
ffffffff00002a50 00000000000000b8 t puts
ffffffff0000a400 00000000000000b8 t sm_intc_fiq_enter
ffffffff0000a690 00000000000000bc t arm_generic_timer_init_secondary_cpu.cfi
ffffffff00020d18 00000000000000bc t hset_find_target
ffffffff00006c8c 00000000000000bc t vpu_enc_fastcall.cfi
ffffffff00020230 00000000000000c0 t handle_close
ffffffff000202f0 00000000000000c0 t handle_del_waiter
ffffffff0015be50 00000000000000c0 b nsirqevent
ffffffff00011888 00000000000000c0 t phys_mem_obj_check_flags.cfi
ffffffff00002b44 00000000000000c0 t printf
ffffffff0000ca44 00000000000000c0 t thread_preempt_lock_held
ffffffff00021b94 00000000000000c4 t chan_handle_destroy.cfi
ffffffff0002e5b0 00000000000000c4 t tx_data_cb.cfi
ffffffff00012abc 00000000000000c8 t pmm_set_cleared
ffffffff000128a8 00000000000000cc t pmm_vmm_obj_destroy.cfi
ffffffff00025c14 00000000000000cc t sys_brk
ffffffff00021c58 00000000000000d0 t chan_shutdown
ffffffff00020000 00000000000000d0 t memlog_commit_callback.cfi
ffffffff00017d54 00000000000000d0 t miniheap_free
ffffffff00003358 00000000000000d0 t platform_init_mmu_mappings
ffffffff0002ed4c 00000000000000d0 t rcipc_vdev_kick_vq.cfi
ffffffff0002d208 00000000000000d4 t rctee_sm_init.cfi
ffffffff000321e8 00000000000000d4 t sm_release_boot_args.cfi
ffffffff0002c908 00000000000000d4 t vqueue_destroy
ffffffff0001831c 00000000000000d8 t __debug_stdio_lock.cfi
ffffffff00007a04 00000000000000d8 t arch_clear_pages_and_tags
ffffffff000200d0 00000000000000d8 t handle_init_etc
ffffffff00003464 00000000000000d8 t init_caam_env.cfi
ffffffff0002d354 00000000000000d8 t rctee_sm_nopcall.cfi
ffffffff00005f08 00000000000000d8 t sm_deallocate_partition
ffffffff0003312c 00000000000000d8 t sm_register_entity
ffffffff000139d8 00000000000000d8 t vmm_obj_slice_bind_locked
ffffffff0000a0d8 00000000000000dc t arm_gic_init
ffffffff00033254 00000000000000dc t smc_restart_stdcall.cfi
ffffffff00033050 00000000000000dc t smc_undefined.cfi
ffffffff000137b0 00000000000000dc t vmm_obj_slice_release
ffffffff0003582c 00000000000000e0 t apploader_service_handle_channel_cleanup.cfi
ffffffff0000fdf4 00000000000000e0 t thread_unlock_prepare
ffffffff000348d8 00000000000000e0 t unittest_printf
ffffffff0001388c 00000000000000e0 t vmm_obj_del_ref
ffffffff0003596c 00000000000000e4 t apploader_service_send_response
ffffffff000148fc 00000000000000e4 t is_range_inside_region
ffffffff00011cc4 00000000000000e4 t pmm_unreserve_pages
ffffffff0014e018 00000000000000e8 d __stderr_FILE
ffffffff0014e100 00000000000000e8 d __stdin_FILE
ffffffff0014e1e8 00000000000000e8 d __stdout_FILE
ffffffff00035b4c 00000000000000e8 t generic_ta_service_handle_connect.cfi
ffffffff000347a8 00000000000000e8 t platform_cpu_priority_set
ffffffff0002e28c 00000000000000ec t _send_buf.cfi
ffffffff0001b420 00000000000000ec t ext_mem_obj_check_flags.cfi
ffffffff00010edc 00000000000000ec t insert_timer_in_queue
ffffffff0002c348 00000000000000ec t rctee_thread_exit
ffffffff0001c620 00000000000000ec t run_ktipctest.cfi
ffffffff0000a314 00000000000000ec t smc_intc_get_next_irq.cfi
ffffffff00010678 00000000000000f0 t thread_become_idle
ffffffff00028978 00000000000000f0 t uctx_handle_readv.cfi
ffffffff00028a68 00000000000000f0 t uctx_handle_writev.cfi
ffffffff000183f4 00000000000000f8 t __debug_stdio_unlock.cfi
ffffffff00036714 00000000000000f8 t hwrng_handle_msg.cfi
ffffffff00024678 00000000000000f8 t ipc_get_msg
ffffffff000127b0 00000000000000f8 t pmm_vmm_obj_get_page.cfi
ffffffff00026b38 00000000000000fc t _uctx_startup.cfi
ffffffff00034cf4 00000000000000fc t apploader_service_init.cfi
ffffffff00009a34 00000000000000fc t arm_gic_suspend_cpu.cfi
ffffffff00035a50 00000000000000fc t generic_ta_service_init.cfi
ffffffff000365a0 00000000000000fc t hwrng_ktipc_server_init.cfi
ffffffff00036a9c 00000000000000fc t smc_service_init.cfi
ffffffff00021974 00000000000000fc t user_iovec_to_membuf
ffffffff00006240 0000000000000100 t imx_linux_fastcall.cfi
ffffffff00159a50 0000000000000100 b preempt_timer
ffffffff000056f4 0000000000000100 t run_job
ffffffff00028b58 0000000000000100 t start_apps.cfi
ffffffff000322bc 0000000000000108 t resume_nsthreads
ffffffff00026a30 0000000000000108 t uctx_init.cfi
ffffffff00007adc 000000000000010c t initial_thread_func.cfi
ffffffff00023c6c 0000000000000110 t ipc_msg_queue_destroy
ffffffff00002df0 0000000000000110 t memcpy
ffffffff000255f8 0000000000000110 t memref_handle_destroy.cfi
ffffffff0000f490 0000000000000110 t thread_free
ffffffff00021860 0000000000000114 t handle_ref_wait
ffffffff000314f8 0000000000000118 t dev_acquire
ffffffff00020574 000000000000011c t handle_notify
ffffffff0000db7c 000000000000011c t thread_mp_reschedule
ffffffff00019944 0000000000000120 t secondary_cpu_bootstrap2.cfi
ffffffff00021a70 0000000000000124 t chan_poll.cfi
ffffffff00023b48 0000000000000124 t ipc_msg_queue_create
ffffffff0003285c 0000000000000124 t sm_wait_for_smcall.cfi
ffffffff00002cc0 0000000000000128 t __stdio_write
ffffffff0001b2f8 0000000000000128 t print_function_info
ffffffff00021f70 000000000000012c t chan_add_ref
ffffffff000334ec 0000000000000130 t smc_cpu_resume.cfi
ffffffff000333bc 0000000000000130 t smc_cpu_suspend.cfi
ffffffff0000a4b8 0000000000000134 t arm_gicv3_configure_irq_locked
ffffffff00024770 0000000000000134 t sys_put_msg
ffffffff00019e64 0000000000000138 t app_manifest_read_string
ffffffff00007820 0000000000000140 t print_fault_code
ffffffff0015aa70 0000000000000140 b thread_stats
ffffffff00007f80 0000000000000144 t arm64_mmu_unmap_pt
ffffffff00006b38 0000000000000144 t vpu_write_regs
ffffffff00012974 0000000000000148 t pmm_free_locked
ffffffff00013ebc 0000000000000148 t scan_gap
ffffffff00028010 0000000000000148 t sys_close
ffffffff00027ec8 0000000000000148 t sys_dup
ffffffff00028158 0000000000000148 t sys_set_cookie
ffffffff00026c34 000000000000014c t _uctx_shutdown.cfi
ffffffff00014004 000000000000014c t spot_in_gap
ffffffff0001354c 0000000000000150 t paddr_to_kvaddr
ffffffff00012c58 0000000000000158 t pmm_alloc_contiguous
ffffffff0002e134 0000000000000158 t rcipc_ext_mem_destroy.cfi
ffffffff00020880 000000000000015c t handle_list_del
ffffffff00022a28 000000000000015c t port_attach_client
ffffffff00032654 000000000000015c t sm_stdcall_loop.cfi
ffffffff0002d0ac 000000000000015c t vqueue_add_buf
ffffffff00004a58 0000000000000160 t caam_decap_blob
ffffffff000048f8 0000000000000160 t caam_gen_blob
ffffffff0000a1b4 0000000000000160 t platform_irq
ffffffff00034640 0000000000000168 t smc_trusty_sched_share_unregister.cfi
ffffffff00002f00 000000000000016c t memset
ffffffff00026d80 000000000000016c t remove_handle
ffffffff0000fc80 0000000000000174 t thread_exit_from_panic
ffffffff00027234 0000000000000174 t uctx_handle_get
ffffffff00017bdc 0000000000000178 t heap_insert_free_chunk
ffffffff00025708 0000000000000178 t memref_mmap.cfi
ffffffff000349b8 000000000000017c t send_msg_wait
ffffffff0002cf30 000000000000017c t vqueue_unmap_iovs
ffffffff0001f20c 0000000000000180 t ktipc_test_server_init.cfi
ffffffff00032ed0 0000000000000180 t platform_halt
ffffffff00022b84 0000000000000184 t chan_alloc
ffffffff00020b94 0000000000000184 t handle_set_attach
ffffffff000273a8 0000000000000188 t uctx_handle_remove
ffffffff0001ab64 0000000000000190 t arm_ffa_rx_release
ffffffff0001fe70 0000000000000190 t memlog_print_callback.cfi
ffffffff00021d28 0000000000000194 t chan_del_ref
ffffffff00014768 0000000000000194 t vmm_find_region_in_bst
ffffffff00032050 0000000000000198 t rcipc_init.cfi
ffffffff00012db0 0000000000000198 t res_group_del_ref
ffffffff0000dc98 0000000000000198 t thread_timer_callback.cfi
ffffffff00018154 000000000000019c t __debug_stdio_write_commit.cfi
ffffffff0000475c 000000000000019c t imx_rand
ffffffff00019c30 00000000000001a0 t busy_test_server.cfi
ffffffff000090d8 00000000000001a4 t arm64_tlbflush_if_asid_changed
ffffffff0002105c 00000000000001a8 t hset_waiter_notify.cfi
ffffffff0002209c 00000000000001a8 t ipc_port_create
ffffffff0002cd88 00000000000001a8 t vqueue_map_iovs
ffffffff0002c19c 00000000000001ac t rctee_app_allow_dma_range
ffffffff0001169c 00000000000001b0 t sem_post
ffffffff000168cc 00000000000001b4 t vmm_set_active_aspace
ffffffff000203b0 00000000000001b8 t handle_wait
ffffffff00006074 00000000000001bc t sys_csu_ioctl.cfi
ffffffff00023d7c 00000000000001bc t sys_send_msg
ffffffff00034b34 00000000000001c0 t unittest_loop.cfi
ffffffff00015300 00000000000001c0 t vmm_alloc
ffffffff0001c0d8 00000000000001c4 t ktipc_recv_iov
ffffffff0001f828 00000000000001c8 t test_handle_send_unblocked.cfi
ffffffff00019a64 00000000000001cc t busy_test_init.cfi
ffffffff0001b5a0 00000000000001cc t ext_mem_map_obj_id
ffffffff00028c58 00000000000001cc t rctee_init.cfi
ffffffff000282a0 00000000000001cc t sys_handle_set_create
ffffffff0001cc98 00000000000001cc t wait_for_hup
ffffffff0001bf00 00000000000001d8 t ksrv_thread.cfi
ffffffff0000de30 00000000000001d8 t thread_set_real_time
ffffffff00010484 00000000000001dc t reaper_thread_routine.cfi
ffffffff00012f48 00000000000001dc t vm_init_preheap.cfi
ffffffff0000b790 00000000000001e0 t event_wait_timeout
ffffffff00036c20 00000000000001e0 t smc_service_handle_msg.cfi
ffffffff00009ef0 00000000000001e8 t register_int_handler
ffffffff00005a7c 00000000000001ec t caam_gen_mppubk
ffffffff00005504 00000000000001f0 t caam_gen_kdfv1_root_key
ffffffff00020690 00000000000001f0 t handle_list_add
ffffffff0001d834 00000000000001f0 t send_cmd
ffffffff0000b970 00000000000001f4 t event_signal
ffffffff0000d028 0000000000000200 t insert_in_run_queue_head
ffffffff00159b80 0000000000000200 b run_queue
ffffffff00005d08 0000000000000200 t sm_alloc_pages
ffffffff0014f040 0000000000000200 d sm_fastcall_table
ffffffff000341bc 0000000000000200 t sm_mem_obj_destroy.cfi
ffffffff0014f240 0000000000000200 d sm_nopcall_table
ffffffff0014f440 0000000000000200 d sm_stdcall_table
ffffffff0000d228 0000000000000208 t insert_in_run_queue_tail
ffffffff00013344 0000000000000208 t vm_init_postheap.cfi
ffffffff000253e8 0000000000000210 t memref_create_from_vmm_obj
ffffffff0014f748 0000000000000210 d syscall_table
ffffffff0000fa70 0000000000000210 t thread_exit
ffffffff0002bed4 0000000000000214 t rctee_thread_write_elf_tables
ffffffff0000e490 0000000000000214 t thread_yield
ffffffff0000bd94 0000000000000218 t mutex_release
ffffffff000154c0 000000000000021c t vmm_alloc_no_physical
ffffffff00013124 0000000000000220 t mark_pages_in_use
ffffffff000251c4 0000000000000224 t ipc_read_msg
ffffffff000150dc 0000000000000224 t vmm_alloc_physical_etc
ffffffff0002c434 000000000000022c t rctee_app_create_and_start
ffffffff0000bb64 0000000000000230 t mutex_acquire_timeout
ffffffff00015e10 0000000000000230 t vmm_free_region_etc
ffffffff00015bdc 0000000000000234 t vmm_get_obj
ffffffff0002e378 0000000000000238 t rcipc_send_data
ffffffff00036860 000000000000023c t hwrng_handle_req_queue
ffffffff000176c0 0000000000000240 t _panic
ffffffff0000927c 0000000000000240 t arm64_mmu_unmap_pt.164
ffffffff00032980 0000000000000240 t sm_check_and_lock_api_version
ffffffff0000e6a4 0000000000000240 t thread_detach
ffffffff0001599c 0000000000000240 t vmm_get_address_description
ffffffff00024434 0000000000000244 t sys_get_msg
ffffffff000236a4 0000000000000250 t ipc_port_accept
ffffffff00006370 0000000000000254 t snvs_fastcall.cfi
ffffffff000238f4 0000000000000254 t sys_accept
ffffffff00032bc0 000000000000025c t sm_return_and_wait_for_next_stdcall
ffffffff0000cdcc 000000000000025c t thread_preempt_inner
ffffffff0002c67c 0000000000000260 t rctee_app_exit_etc
ffffffff000261e8 0000000000000264 t sys_prepare_dma
ffffffff00021204 0000000000000268 t handle_set_detach_ref
ffffffff0001b76c 0000000000000268 t ktipc_server_add_port
ffffffff0000ff18 000000000000026c t thread_sleep_ns
ffffffff00007d10 0000000000000270 t arm64_mmu_map_pt
ffffffff00027850 0000000000000278 t sys_wait_any
ffffffff00017960 000000000000027c t miniheap_memalign
ffffffff000343bc 0000000000000284 t smc_trusty_sched_share_register.cfi
ffffffff000057f4 0000000000000288 t caam_gen_bkek_key
ffffffff00020dd4 0000000000000288 t hset_attach_ref
ffffffff00022d08 000000000000028c t sys_port_create
ffffffff00014e50 000000000000028c t vmm_map_obj_locked
ffffffff000323c4 0000000000000290 t sm_init.cfi
ffffffff0001b9d4 0000000000000294 t port_event_handler.cfi
ffffffff0002eab8 0000000000000294 t rcipc_vdev_reset.cfi
ffffffff0001c70c 0000000000000294 t run_test_suite
ffffffff00010184 0000000000000294 t thread_sleep_handler.cfi
ffffffff0001bc68 0000000000000298 t chan_event_handler.cfi
ffffffff00022790 0000000000000298 t ipc_port_publish
ffffffff00009794 00000000000002a0 t arch_mmu_init_aspace
ffffffff0000b4e4 00000000000002ac t event_destroy
ffffffff000248a4 00000000000002b4 t ipc_put_msg
ffffffff00011a0c 00000000000002b8 t pmm_add_arena
ffffffff000156e4 00000000000002b8 t vmm_res_obj_destroy.cfi
ffffffff0002644c 00000000000002c0 t sys_finish_dma
ffffffff00010c1c 00000000000002c0 t timer_set
ffffffff0000cb04 00000000000002c8 t thread_cond_mp_reschedule
ffffffff000233d0 00000000000002d4 t sys_connect
ffffffff000094bc 00000000000002d8 t arch_mmu_unmap
ffffffff000258d8 00000000000002dc t sys_std_writev.cfi
ffffffff0001b018 00000000000002e0 t dump_function
ffffffff00008de0 00000000000002f8 t arm64_mmu_map_pt.150
ffffffff0001ce64 00000000000002f8 t ktipctest_blockedport.cfi
ffffffff0001c9a0 00000000000002f8 t ktipctest_connecterr.cfi
ffffffff0002ca8c 00000000000002fc t vqueue_get_avail_buf
ffffffff0002671c 0000000000000304 t sys_memref_create
ffffffff00009b30 0000000000000308 t arm_gic_resume_cpu.cfi
ffffffff00025e9c 0000000000000308 t sys_mmap
ffffffff00027530 0000000000000320 t sys_wait.cfi
ffffffff0001acf4 0000000000000324 t dump_thread_backtrace
ffffffff00017e24 0000000000000330 t __debug_stdio_write.cfi
ffffffff00010fc8 0000000000000334 t timer_tick.cfi
ffffffff00026eec 0000000000000348 t uctx_handle_install
ffffffff00016574 0000000000000358 t vmm_context_switch
ffffffff0000f12c 0000000000000364 t wait_queue_block
ffffffff0001faf4 000000000000037c t memlog_stdcall.cfi
ffffffff0001c29c 0000000000000384 t ktipctest_init.cfi
ffffffff0000eda8 0000000000000384 t thread_join
ffffffff0001f440 0000000000000388 t test_handle_msg.cfi
ffffffff000112fc 00000000000003a0 t timer_cancel_etc
ffffffff0001a7b0 00000000000003b4 t arm_ffa_sched_nonsecure
ffffffff0000bfac 00000000000003d8 t thread_create_etc
ffffffff0002146c 00000000000003e8 t handle_set_wait
ffffffff0002a8b0 00000000000003e8 t rctee_app_create
ffffffff0002e6bc 00000000000003fc t rcipc_vdev_probe.cfi
ffffffff00027ac8 0000000000000400 t rebuild_hset_all
ffffffff00013ab0 000000000000040c t alloc_spot
ffffffff00008524 0000000000000430 t arch_mmu_query
ffffffff00022f94 000000000000043c t ipc_port_connect_async
ffffffff00023f38 0000000000000454 t msg_write_locked
ffffffff000080c4 0000000000000460 t arm64_early_mmu_init
ffffffff000107b8 0000000000000464 t wait_queue_wake_one
ffffffff000149e8 0000000000000468 t vmm_alloc_obj
ffffffff00016a80 0000000000000488 t bst_update_rank_insert
ffffffff0000e008 0000000000000488 t thread_resume
ffffffff00008954 000000000000048c t arm64_mmu_map_aspace
ffffffff0000506c 0000000000000498 t caam_decap_dek_blob
ffffffff000222f0 00000000000004a0 t port_handle_destroy.cfi
ffffffff00004bb8 00000000000004b4 t caam_gen_dek_blob
ffffffff0000e8e4 00000000000004c4 t wait_queue_wake_all
ffffffff0000f5a0 00000000000004d0 t wait_queue_timeout_handler.cfi
ffffffff00011da8 00000000000004ec t pmm_alloc_from_res_group
ffffffff0002846c 000000000000050c t sys_handle_set_ctrl
ffffffff00012294 0000000000000514 t pmm_alloc_pages_locked
ffffffff00016040 0000000000000534 t vmm_free_aspace
ffffffff00006d80 0000000000000538 t arm64_secondary_entry
ffffffff000065f4 0000000000000544 t vpu_fastcall.cfi
ffffffff000072b8 0000000000000568 t arm64_sync_exception
ffffffff00014150 0000000000000618 t alloc_region
ffffffff00024b58 000000000000066c t sys_read_msg
ffffffff0000c384 00000000000006c0 t thread_set_pinned_cpu
ffffffff0001d15c 00000000000006d8 t ktipctest_echo.cfi
ffffffff0001ead0 000000000000073c t ktipctest_blockedsend.cfi
ffffffff0000d430 000000000000074c t thread_resched
ffffffff000191a0 00000000000007a4 t bootstrap2.cfi
ffffffff00016f08 00000000000007b8 t bst_delete
ffffffff0001e2e4 00000000000007ec t ktipctest_close.cfi
ffffffff00159190 0000000000000800 b int_handler_table_per_cpu
ffffffff00042c48 0000000000000800 r int_handler_table_shared
ffffffff0002fad4 0000000000000810 t rcipc_rx_thread_func.cfi
ffffffff00019f9c 0000000000000814 t arm_ffa_init.cfi
ffffffff000302e4 0000000000000890 t rcipc_tx_thread_func.cfi
ffffffff00030bc8 0000000000000898 t handle_ctrl_msg
ffffffff0001da24 00000000000008c0 t ktipctest_echo8.cfi
ffffffff00035c34 0000000000000968 t generic_ta_service_handle_msg.cfi
ffffffff00001f64 0000000000000984 t _printf_engine_internal
ffffffff00034e64 00000000000009c8 t apploader_service_handle_msg.cfi
ffffffff00031610 00000000000009e4 t ql_rcipc_handle_cmd
ffffffff00018708 0000000000000a98 t lk_main
ffffffff000336d8 0000000000000adc t ext_mem_get_vmm_obj
ffffffff0000a8b4 0000000000000c30 t arm_generic_timer_init
ffffffff00159d80 0000000000000c40 b _idle_threads
ffffffff0002ee1c 0000000000000cb8 t conn_req_thread_func.cfi
ffffffff0002d42c 0000000000000d08 t rctee_sm_stdcall.cfi
ffffffff0015ac98 0000000000001000 b early_log_buffer
ffffffff0002ac98 00000000000010e4 t load_app_config_options
ffffffff0000354c 0000000000001210 t sys_caam_ioctl.cfi
ffffffff00028e24 0000000000001a8c t app_mgr.cfi
