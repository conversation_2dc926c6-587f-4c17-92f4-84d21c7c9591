out/build-imx8mp/opensource_libs/musl/src/multibyte/wcrtomb.o: \
  opensource_libs/musl/src/multibyte/wcrtomb.c out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/rctee/lib/libc-trusty/module_config.h \
  opensource_libs/musl/src/include/stdlib.h \
  opensource_libs/musl/src/include/../../include/stdlib.h \
  opensource_libs/musl/src/include/features.h \
  opensource_libs/musl/src/include/../../include/features.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  opensource_libs/musl/src/include/wchar.h \
  opensource_libs/musl/src/include/../../include/wchar.h \
  opensource_libs/musl/src/include/errno.h \
  opensource_libs/musl/src/include/../../include/errno.h \
  opensource_libs/musl/arch/generic/bits/errno.h \
  opensource_libs/musl/src/multibyte/internal.h \
  opensource_libs/musl/include/stdint.h \
  opensource_libs/musl/arch/aarch64/bits/stdint.h \
  opensource_libs/musl/src/internal/locale_impl.h \
  opensource_libs/musl/include/locale.h \
  opensource_libs/musl/src/internal/libc.h \
  opensource_libs/musl/src/include/stdio.h \
  opensource_libs/musl/src/include/../../include/stdio.h \
  opensource_libs/musl/include/limits.h \
  opensource_libs/musl/arch/aarch64/bits/limits.h \
  kernel/rctee/lib/libc-trusty/include/trusty/libc_state.h

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/rctee/lib/libc-trusty/module_config.h:

opensource_libs/musl/src/include/stdlib.h:

opensource_libs/musl/src/include/../../include/stdlib.h:

opensource_libs/musl/src/include/features.h:

opensource_libs/musl/src/include/../../include/features.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

opensource_libs/musl/src/include/wchar.h:

opensource_libs/musl/src/include/../../include/wchar.h:

opensource_libs/musl/src/include/errno.h:

opensource_libs/musl/src/include/../../include/errno.h:

opensource_libs/musl/arch/generic/bits/errno.h:

opensource_libs/musl/src/multibyte/internal.h:

opensource_libs/musl/include/stdint.h:

opensource_libs/musl/arch/aarch64/bits/stdint.h:

opensource_libs/musl/src/internal/locale_impl.h:

opensource_libs/musl/include/locale.h:

opensource_libs/musl/src/internal/libc.h:

opensource_libs/musl/src/include/stdio.h:

opensource_libs/musl/src/include/../../include/stdio.h:

opensource_libs/musl/include/limits.h:

opensource_libs/musl/arch/aarch64/bits/limits.h:

kernel/rctee/lib/libc-trusty/include/trusty/libc_state.h:
