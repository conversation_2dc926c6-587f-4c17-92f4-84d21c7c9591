out/build-imx8mp/opensource_libs/musl/src/stdio/stdin.o: \
  opensource_libs/musl/src/stdio/stdin.c out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/rctee/lib/libc-trusty/module_config.h \
  opensource_libs/musl/src/internal/stdio_impl.h \
  kernel/lk/lib/libc/include_common/assert.h \
  kernel/lk/include/shared/lk/compiler.h \
  opensource_libs/musl/include/stddef.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  kernel/lk/include/panic.h opensource_libs/musl/src/include/stdio.h \
  opensource_libs/musl/src/include/../../include/stdio.h \
  opensource_libs/musl/src/include/features.h \
  opensource_libs/musl/src/include/../../include/features.h

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/rctee/lib/libc-trusty/module_config.h:

opensource_libs/musl/src/internal/stdio_impl.h:

kernel/lk/lib/libc/include_common/assert.h:

kernel/lk/include/shared/lk/compiler.h:

opensource_libs/musl/include/stddef.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

kernel/lk/include/panic.h:

opensource_libs/musl/src/include/stdio.h:

opensource_libs/musl/src/include/../../include/stdio.h:

opensource_libs/musl/src/include/features.h:

opensource_libs/musl/src/include/../../include/features.h:
