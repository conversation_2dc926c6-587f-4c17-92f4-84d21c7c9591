out/build-imx8mp/opensource_libs/musl/src/string/memmem.o: \
  opensource_libs/musl/src/string/memmem.c out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/rctee/lib/libc-trusty/module_config.h \
  opensource_libs/musl/src/include/string.h \
  opensource_libs/musl/src/include/../../include/string.h \
  opensource_libs/musl/src/include/features.h \
  opensource_libs/musl/src/include/../../include/features.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  opensource_libs/musl/include/strings.h \
  opensource_libs/musl/include/stdint.h \
  opensource_libs/musl/arch/aarch64/bits/stdint.h

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/rctee/lib/libc-trusty/module_config.h:

opensource_libs/musl/src/include/string.h:

opensource_libs/musl/src/include/../../include/string.h:

opensource_libs/musl/src/include/features.h:

opensource_libs/musl/src/include/../../include/features.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

opensource_libs/musl/include/strings.h:

opensource_libs/musl/include/stdint.h:

opensource_libs/musl/arch/aarch64/bits/stdint.h:
