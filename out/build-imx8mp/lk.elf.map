             VMA              LMA     Size Align Out     In      Symbol
               0                0 ffffffff00000000     1 . = 0xFFFFFFFF00000000 + 0
ffffffff00000000         56000000    370ac  4096 .text
ffffffff00000000         56000000        0     1         __code_start = .
ffffffff00000000         56000000      388     8         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(start.o):(.text.boot)
ffffffff00000000         56000000        0     1                 $x.0
ffffffff00000000         56000000        0     1                 arm_reset
ffffffff00000000         56000000        0     1                 _start
ffffffff000002e8         560002e8        0     1                 arm64_curr_cpu_num
ffffffff00000368         56000368        0     1                 $d.1
ffffffff00000800         56000800      784  2048         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(start.o):(.text.boot.early.vectab)
ffffffff00000800         56000800        0     1                 $x.2
ffffffff00001000         56001000      934  4096         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(exceptions.o):(.text.boot.vectab)
ffffffff00001000         56001000        0     1                 arm64_sync_exc_current_el_SP0
ffffffff00001000         56001000        0     1                 $x.0
ffffffff00001000         56001000        0     1                 arm64_exception_base
ffffffff00001080         56001080        0     1                 arm64_irq_current_el_SP0
ffffffff00001100         56001100        0     1                 arm64_fiq_current_el_SP0
ffffffff00001180         56001180        0     1                 arm64_err_exc_current_el_SP0
ffffffff00001200         56001200        0     1                 arm64_sync_exc_current_el_SPx
ffffffff00001280         56001280        0     1                 arm64_irq_current_el_SPx
ffffffff00001300         56001300        0     1                 arm64_fiq_current_el_SPx
ffffffff00001380         56001380        0     1                 arm64_err_exc_current_el_SPx
ffffffff00001400         56001400        0     1                 arm64_sync_exc_lower_el_64
ffffffff00001480         56001480        0     1                 arm64_irq_lower_el_64
ffffffff00001500         56001500        0     1                 arm64_fiq_lower_el_64
ffffffff00001580         56001580        0     1                 arm64_err_exc_lower_el_64
ffffffff00001600         56001600        0     1                 arm64_sync_exc_lower_el_32
ffffffff00001680         56001680        0     1                 arm64_irq_lower_el_32
ffffffff00001700         56001700        0     1                 arm64_fiq_lower_el_32
ffffffff00001780         56001780        0     1                 arm64_err_exc_lower_el_32
ffffffff00001800         56001800        0     1                 arm64_sync_exc_shared
ffffffff00001860         56001860        0     1                 arm64_exc_shared_restore_short
ffffffff000018a8         560018a8        0     1                 arm64_enter_uspace
ffffffff00001934         56001934      120     4         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(asm.o):(.text)
ffffffff00001934         56001934        0     1                 $x.0
ffffffff00001934         56001934        0     1                 arm64_context_switch
ffffffff00001994         56001994        0     1                 arm64_el3_to_el1
ffffffff000019d8         560019d8        0     1                 arm64_elX_to_el1
ffffffff000019e8         560019e8        0     1                 .notEL1
ffffffff00001a10         56001a10        0     1                 .inEL2
ffffffff00001a20         56001a20        0     1                 .confEL1
ffffffff00001a54         56001a54       40     4         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(spinlock.o):(.text)
ffffffff00001a54         56001a54        0     1                 $x.0
ffffffff00001a54         56001a54        0     1                 arch_spin_trylock
ffffffff00001a6c         56001a6c        0     1                 arch_spin_lock
ffffffff00001a8c         56001a8c        0     1                 arch_spin_unlock
ffffffff00001a94         56001a94       9c     4         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(cache-ops.o):(.text)
ffffffff00001a94         56001a94        0     1                 $x.0
ffffffff00001a94         56001a94        0     1                 arch_clean_cache_range
ffffffff00001ab4         56001ab4        0     1                 arch_clean_invalidate_cache_range
ffffffff00001ad4         56001ad4        0     1                 arch_invalidate_cache_range
ffffffff00001af4         56001af4        0     1                 arch_sync_cache_range
ffffffff00001b30         56001b30       84     4         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(usercopy.o):(.text)
ffffffff00001b30         56001b30        0     1                 $x.0
ffffffff00001b30         56001b30        0     1                 arch_copy_to_user
ffffffff00001b38         56001b38        0     1                 $x.2
ffffffff00001b50         56001b50        0     1                 arch_copy_from_user
ffffffff00001b54         56001b54        0     1                 $x.4
ffffffff00001b70         56001b70        0     1                 arch_strlcpy_from_user
ffffffff00001b74         56001b74        0     1                 $x.6
ffffffff00001bb4         56001bb4       50     4         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(safecopy.o):(.text)
ffffffff00001bb4         56001bb4        0     1                 $x.0
ffffffff00001bb4         56001bb4        0     1                 copy_from_anywhere
ffffffff00001bb8         56001bb8        0     1                 $x.2
ffffffff00001bd4         56001bd4        0     1                 $x.4
ffffffff00001bf0         56001bf0        0     1                 $x.6
ffffffff00001bf0         56001bf0        0     1                 tag_for_addr_
ffffffff00001c04         56001c04       68     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(libc_state.o):(.text.libc_state_thread_init)
ffffffff00001c04         56001c04        0     1                 $x.0
ffffffff00001c04         56001c04       68     1                 libc_state_thread_init
ffffffff00001c6c         56001c6c       74     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(libc_state.o):(.text.libc_state_thread_free)
ffffffff00001c6c         56001c6c        0     1                 $x.1
ffffffff00001c6c         56001c6c       74     1                 libc_state_thread_free
ffffffff00001ce0         56001ce0       a4     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(writev.o):(.text.rctee_writev)
ffffffff00001ce0         56001ce0        0     1                 $x.0
ffffffff00001ce0         56001ce0       a4     1                 rctee_writev
ffffffff00001d84         56001d84       14     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(io_handle.o):(.text.fd_io_handle)
ffffffff00001d84         56001d84        0     1                 $x.0
ffffffff00001d84         56001d84       14     1                 fd_io_handle
ffffffff00001d98         56001d98       34     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(io_handle.o):(.text.file_io_handle)
ffffffff00001d98         56001d98        0     1                 $x.1
ffffffff00001d98         56001d98       34     1                 file_io_handle
ffffffff00001dcc         56001dcc       9c     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(printf.o):(.text.snprintf)
ffffffff00001dcc         56001dcc        0     1                 $x.2
ffffffff00001dcc         56001dcc       9c     1                 snprintf
ffffffff00001e68         56001e68       7c     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(printf.o):(.text.vsnprintf)
ffffffff00001e68         56001e68        0     1                 $x.3
ffffffff00001e68         56001e68       7c     1                 vsnprintf
ffffffff00001ee4         56001ee4       44     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(printf.o):(.text._vsnprintf_output)
ffffffff00001ee4         56001ee4       44     1                 _vsnprintf_output
ffffffff00001ee4         56001ee4        0     1                 $x.6
ffffffff00001f28         56001f28       3c     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(printf.o):(.text._printf_engine)
ffffffff00001f28         56001f28        0     1                 $x.7
ffffffff00001f28         56001f28       3c     1                 _printf_engine
ffffffff00001f64         56001f64      984     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(printf.o):(.text._printf_engine_internal)
ffffffff00001f64         56001f64      984     1                 _printf_engine_internal
ffffffff00001f64         56001f64        0     1                 $x.8
ffffffff000028e8         560028e8       94     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(printf.o):(.text.longlong_to_string)
ffffffff000028e8         560028e8       94     1                 longlong_to_string
ffffffff000028e8         560028e8        0     1                 $x.10
ffffffff0000297c         5600297c       3c     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(printf.o):(.text.longlong_to_hexstring)
ffffffff0000297c         5600297c       3c     1                 longlong_to_hexstring
ffffffff0000297c         5600297c        0     1                 $x.11
ffffffff000029b8         560029b8        c     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(rand.o):(.text.srand)
ffffffff000029b8         560029b8        0     1                 $x.0
ffffffff000029b8         560029b8        c     1                 srand
ffffffff000029c4         560029c4       24     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(rand.o):(.text.rand)
ffffffff000029c4         560029c4        0     1                 $x.2
ffffffff000029c4         560029c4       24     1                 rand
ffffffff000029e8         560029e8       68     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdio.o):(.text.putchar)
ffffffff000029e8         560029e8        0     1                 $x.2
ffffffff000029e8         560029e8       68     1                 putchar
ffffffff00002a50         56002a50       b8     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdio.o):(.text.puts)
ffffffff00002a50         56002a50        0     1                 $x.3
ffffffff00002a50         56002a50       b8     1                 puts
ffffffff00002b08         56002b08       3c     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdio.o):(.text._fprintf_output_func)
ffffffff00002b08         56002b08       3c     1                 _fprintf_output_func
ffffffff00002b08         56002b08        0     1                 $x.9
ffffffff00002b44         56002b44       c0     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdio.o):(.text.printf)
ffffffff00002b44         56002b44        0     1                 $x.11
ffffffff00002b44         56002b44       c0     1                 printf
ffffffff00002c04         56002c04       9c     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdio.o):(.text.vprintf)
ffffffff00002c04         56002c04        0     1                 $x.12
ffffffff00002c04         56002c04       9c     1                 vprintf
ffffffff00002ca0         56002ca0        8     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_close.o):(.text.__stdio_close)
ffffffff00002ca0         56002ca0        0     1                 $x.1
ffffffff00002ca0         56002ca0        8     1                 __stdio_close
ffffffff00002ca8         56002ca8       18     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_read.o):(.text.__stdio_read)
ffffffff00002ca8         56002ca8        0     1                 $x.0
ffffffff00002ca8         56002ca8       18     1                 __stdio_read
ffffffff00002cc0         56002cc0      128     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_write.o):(.text.__stdio_write)
ffffffff00002cc0         56002cc0        0     1                 $x.0
ffffffff00002cc0         56002cc0      128     1                 __stdio_write
ffffffff00002de8         56002de8        8     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_seek.o):(.text.__stdio_seek)
ffffffff00002de8         56002de8        0     1                 $x.0
ffffffff00002de8         56002de8        8     1                 __stdio_seek
ffffffff00002df0         56002df0      110     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memcpy.o):(.text.memcpy)
ffffffff00002df0         56002df0        0     1                 $x.0
ffffffff00002df0         56002df0      110     1                 memcpy
ffffffff00002f00         56002f00      16c     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memset.o):(.text.memset)
ffffffff00002f00         56002f00        0     1                 $x.0
ffffffff00002f00         56002f00      16c     1                 memset
ffffffff0000306c         5600306c       1c     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcmp.o):(.text.strcmp)
ffffffff0000306c         5600306c        0     1                 $x.0
ffffffff0000306c         5600306c       1c     1                 strcmp
ffffffff00003088         56003088       58     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strdup.o):(.text.strdup)
ffffffff00003088         56003088        0     1                 $x.0
ffffffff00003088         56003088       58     1                 strdup
ffffffff000030e0         560030e0       68     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strlcpy.o):(.text.strlcpy)
ffffffff000030e0         560030e0        0     1                 $x.0
ffffffff000030e0         560030e0       68     1                 strlcpy
ffffffff00003148         56003148       18     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strlen.o):(.text.strlen)
ffffffff00003148         56003148        0     1                 $x.0
ffffffff00003148         56003148       18     1                 strlen
ffffffff00003160         56003160       20     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncpy.o):(.text.strncpy)
ffffffff00003160         56003160        0     1                 $x.0
ffffffff00003160         56003160       20     1                 strncpy
ffffffff00003180         56003180       40     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncmp.o):(.text.strncmp)
ffffffff00003180         56003180        0     1                 $x.0
ffffffff00003180         56003180       40     1                 strncmp
ffffffff000031c0         560031c0       30     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strnlen.o):(.text.strnlen)
ffffffff000031c0         560031c0        0     1                 $x.0
ffffffff000031c0         560031c0       30     1                 strnlen
ffffffff000031f0         560031f0       50     8         out/build-imx8mp/kernel/rctee/lib/sm.mod.a(entry.o):(.text)
ffffffff000031f0         560031f0        0     1                 $x.0
ffffffff000031f0         560031f0        0     1                 sm_sched_nonsecure
ffffffff0000321c         5600321c        0     1                 platform_early_halt
ffffffff00003238         56003238        0     1                 $d.1
ffffffff00003240         56003240       20     4         out/build-imx8mp/kernel/rctee/lib/smc.mod.a(smc.o):(.text)
ffffffff00003240         56003240        0     1                 $x.0
ffffffff00003240         56003240        0     1                 smc8
ffffffff00003260         56003260       54     4         out/build-imx8mp/kernel/rctee/lib/syscall.mod.a(syscall.o):(.text)
ffffffff00003260         56003260        0     1                 $x.0
ffffffff00003260         56003260        0     1                 arm64_syscall
ffffffff000032b4         560032b4       18     4         lto.tmp:(.text.console_smcall_init.cfi)
ffffffff000032b4         560032b4       18     1                 console_smcall_init.cfi
ffffffff000032b4         560032b4        0     1                 $x.0
ffffffff000032cc         560032cc       2c     4         lto.tmp:(.text.console_stdcall.cfi)
ffffffff000032cc         560032cc       2c     1                 console_stdcall.cfi
ffffffff000032cc         560032cc        0     1                 $x.1
ffffffff000032f8         560032f8       40     4         lto.tmp:(.text.platform_dputc)
ffffffff000032f8         560032f8       40     1                 platform_dputc
ffffffff000032f8         560032f8        0     1                 $x.2
ffffffff00003338         56003338        c     4         lto.tmp:(.text.platform_dgetc)
ffffffff00003338         56003338        c     1                 platform_dgetc
ffffffff00003338         56003338        0     1                 $x.3
ffffffff00003344         56003344       14     4         lto.tmp:(.text.platform_after_vm_init.cfi)
ffffffff00003344         56003344       14     1                 platform_after_vm_init.cfi
ffffffff00003344         56003344        0     1                 $x.4
ffffffff00003358         56003358       d0     4         lto.tmp:(.text.platform_init_mmu_mappings)
ffffffff00003358         56003358       d0     1                 platform_init_mmu_mappings
ffffffff00003358         56003358        0     1                 $x.5
ffffffff00003428         56003428       10     4         lto.tmp:(.text.smc_load_access_policy)
ffffffff00003428         56003428       10     1                 smc_load_access_policy
ffffffff00003428         56003428        0     1                 $x.6
ffffffff00003438         56003438        8     4         lto.tmp:(.text.default_access_policy.cfi)
ffffffff00003438         56003438        8     1                 default_access_policy.cfi
ffffffff00003438         56003438        0     1                 $x.7
ffffffff00003440         56003440       24     4         lto.tmp:(.text.add_app_ranges.cfi)
ffffffff00003440         56003440       24     1                 add_app_ranges.cfi
ffffffff00003440         56003440        0     1                 $x.8
ffffffff00003464         56003464       d8     4         lto.tmp:(.text.init_caam_env.cfi)
ffffffff00003464         56003464       d8     1                 init_caam_env.cfi
ffffffff00003464         56003464        0     1                 $x.9
ffffffff0000353c         5600353c       10     4         lto.tmp:(.text.platform_init_caam.cfi)
ffffffff0000353c         5600353c       10     1                 platform_init_caam.cfi
ffffffff0000353c         5600353c        0     1                 $x.10
ffffffff0000354c         5600354c     1210     4         lto.tmp:(.text.sys_caam_ioctl.cfi)
ffffffff0000354c         5600354c     1210     1                 sys_caam_ioctl.cfi
ffffffff0000354c         5600354c        0     1                 $x.11
ffffffff0000475c         5600475c      19c     4         lto.tmp:(.text.imx_rand)
ffffffff0000475c         5600475c      19c     1                 imx_rand
ffffffff0000475c         5600475c        0     1                 $x.13
ffffffff000048f8         560048f8      160     4         lto.tmp:(.text.caam_gen_blob)
ffffffff000048f8         560048f8      160     1                 caam_gen_blob
ffffffff000048f8         560048f8        0     1                 $x.14
ffffffff00004a58         56004a58      160     4         lto.tmp:(.text.caam_decap_blob)
ffffffff00004a58         56004a58      160     1                 caam_decap_blob
ffffffff00004a58         56004a58        0     1                 $x.15
ffffffff00004bb8         56004bb8      4b4     4         lto.tmp:(.text.caam_gen_dek_blob)
ffffffff00004bb8         56004bb8      4b4     1                 caam_gen_dek_blob
ffffffff00004bb8         56004bb8        0     1                 $x.16
ffffffff0000506c         5600506c      498     4         lto.tmp:(.text.caam_decap_dek_blob)
ffffffff0000506c         5600506c      498     1                 caam_decap_dek_blob
ffffffff0000506c         5600506c        0     1                 $x.17
ffffffff00005504         56005504      1f0     4         lto.tmp:(.text.caam_gen_kdfv1_root_key)
ffffffff00005504         56005504      1f0     1                 caam_gen_kdfv1_root_key
ffffffff00005504         56005504        0     1                 $x.18
ffffffff000056f4         560056f4      100     4         lto.tmp:(.text.run_job)
ffffffff000056f4         560056f4      100     1                 run_job
ffffffff000056f4         560056f4        0     1                 $x.19
ffffffff000057f4         560057f4      288     4         lto.tmp:(.text.caam_gen_bkek_key)
ffffffff000057f4         560057f4      288     1                 caam_gen_bkek_key
ffffffff000057f4         560057f4        0     1                 $x.20
ffffffff00005a7c         56005a7c      1ec     4         lto.tmp:(.text.caam_gen_mppubk)
ffffffff00005a7c         56005a7c      1ec     1                 caam_gen_mppubk
ffffffff00005a7c         56005a7c        0     1                 $x.21
ffffffff00005c68         56005c68       a0     4         lto.tmp:(.text.caam_get_keybox)
ffffffff00005c68         56005c68       a0     1                 caam_get_keybox
ffffffff00005c68         56005c68        0     1                 $x.22
ffffffff00005d08         56005d08      200     4         lto.tmp:(.text.sm_alloc_pages)
ffffffff00005d08         56005d08      200     1                 sm_alloc_pages
ffffffff00005d08         56005d08        0     1                 $x.23
ffffffff00005f08         56005f08       d8     4         lto.tmp:(.text.sm_deallocate_partition)
ffffffff00005f08         56005f08       d8     1                 sm_deallocate_partition
ffffffff00005f08         56005f08        0     1                 $x.24
ffffffff00005fe0         56005fe0       84     4         lto.tmp:(.text.platform_random_get_bytes)
ffffffff00005fe0         56005fe0       84     1                 platform_random_get_bytes
ffffffff00005fe0         56005fe0        0     1                 $x.25
ffffffff00006064         56006064       10     4         lto.tmp:(.text.platform_init_csu.cfi)
ffffffff00006064         56006064       10     1                 platform_init_csu.cfi
ffffffff00006064         56006064        0     1                 $x.26
ffffffff00006074         56006074      1bc     4         lto.tmp:(.text.sys_csu_ioctl.cfi)
ffffffff00006074         56006074      1bc     1                 sys_csu_ioctl.cfi
ffffffff00006074         56006074        0     1                 $x.27
ffffffff00006230         56006230       10     4         lto.tmp:(.text.imx_linux_smcall_init.cfi)
ffffffff00006230         56006230       10     1                 imx_linux_smcall_init.cfi
ffffffff00006230         56006230        0     1                 $x.28
ffffffff00006240         56006240      100     4         lto.tmp:(.text.imx_linux_fastcall.cfi)
ffffffff00006240         56006240      100     1                 imx_linux_fastcall.cfi
ffffffff00006240         56006240        0     1                 $x.29
ffffffff00006340         56006340       30     4         lto.tmp:(.text.snvs_smcall_init.cfi)
ffffffff00006340         56006340       30     1                 snvs_smcall_init.cfi
ffffffff00006340         56006340        0     1                 $x.31
ffffffff00006370         56006370      254     4         lto.tmp:(.text.snvs_fastcall.cfi)
ffffffff00006370         56006370      254     1                 snvs_fastcall.cfi
ffffffff00006370         56006370        0     1                 $x.32
ffffffff000065c4         560065c4       20     4         lto.tmp:(.text.monotonic_time_s)
ffffffff000065c4         560065c4       20     1                 monotonic_time_s
ffffffff000065c4         560065c4        0     1                 $x.34
ffffffff000065e4         560065e4       10     4         lto.tmp:(.text.vpu_smcall_init.cfi)
ffffffff000065e4         560065e4       10     1                 vpu_smcall_init.cfi
ffffffff000065e4         560065e4        0     1                 $x.35
ffffffff000065f4         560065f4      544     4         lto.tmp:(.text.vpu_fastcall.cfi)
ffffffff000065f4         560065f4      544     1                 vpu_fastcall.cfi
ffffffff000065f4         560065f4        0     1                 $x.36
ffffffff00006b38         56006b38      144     4         lto.tmp:(.text.vpu_write_regs)
ffffffff00006b38         56006b38      144     1                 vpu_write_regs
ffffffff00006b38         56006b38        0     1                 $x.38
ffffffff00006c7c         56006c7c       10     4         lto.tmp:(.text.vpu_enc_smcall_init.cfi)
ffffffff00006c7c         56006c7c       10     1                 vpu_enc_smcall_init.cfi
ffffffff00006c7c         56006c7c        0     1                 $x.39
ffffffff00006c8c         56006c8c       bc     4         lto.tmp:(.text.vpu_enc_fastcall.cfi)
ffffffff00006c8c         56006c8c       bc     1                 vpu_enc_fastcall.cfi
ffffffff00006c8c         56006c8c        0     1                 $x.40
ffffffff00006d48         56006d48        4     4         lto.tmp:(.text.app_thread_entry.cfi)
ffffffff00006d48         56006d48        4     1                 app_thread_entry.cfi
ffffffff00006d48         56006d48        0     1                 $x.41
ffffffff00006d4c         56006d4c       34     4         lto.tmp:(.text.arch_enter_uspace)
ffffffff00006d4c         56006d4c       34     1                 arch_enter_uspace
ffffffff00006d4c         56006d4c        0     1                 $x.42
ffffffff00006d80         56006d80      538     4         lto.tmp:(.text.arm64_secondary_entry)
ffffffff00006d80         56006d80        0     1                 $x.43
ffffffff00006d80         56006d80      538     1                 arm64_secondary_entry
ffffffff000072b8         560072b8      568     4         lto.tmp:(.text.arm64_sync_exception)
ffffffff000072b8         560072b8        0     1                 $x.44
ffffffff000072b8         560072b8      568     1                 arm64_sync_exception
ffffffff00007820         56007820      140     4         lto.tmp:(.text.print_fault_code)
ffffffff00007820         56007820      140     1                 print_fault_code
ffffffff00007820         56007820        0     1                 $x.46
ffffffff00007960         56007960       60     4         lto.tmp:(.text.dump_iframe)
ffffffff00007960         56007960       60     1                 dump_iframe
ffffffff00007960         56007960        0     1                 $x.48
ffffffff000079c0         560079c0       44     4         lto.tmp:(.text.arm64_invalid_exception)
ffffffff000079c0         560079c0        0     1                 $x.49
ffffffff000079c0         560079c0       44     1                 arm64_invalid_exception
ffffffff00007a04         56007a04       d8     4         lto.tmp:(.text.arch_clear_pages_and_tags)
ffffffff00007a04         56007a04       d8     1                 arch_clear_pages_and_tags
ffffffff00007a04         56007a04        0     1                 $x.50
ffffffff00007adc         56007adc      10c     4         lto.tmp:(.text.initial_thread_func.cfi)
ffffffff00007adc         56007adc      10c     1                 initial_thread_func.cfi
ffffffff00007adc         56007adc        0     1                 $x.51
ffffffff00007be8         56007be8       80     4         lto.tmp:(.text.arch_context_switch)
ffffffff00007be8         56007be8       80     1                 arch_context_switch
ffffffff00007be8         56007be8        0     1                 $x.52
ffffffff00007c68         56007c68       48     4         lto.tmp:(.text.arm64_pan_init.cfi)
ffffffff00007c68         56007c68       48     1                 arm64_pan_init.cfi
ffffffff00007c68         56007c68        0     1                 $x.53
ffffffff00007cb0         56007cb0        8     4         lto.tmp:(.text.arm_ipi_generic_handler.cfi)
ffffffff00007cb0         56007cb0        8     1                 arm_ipi_generic_handler.cfi
ffffffff00007cb0         56007cb0        0     1                 $x.54
ffffffff00007cb8         56007cb8       58     4         lto.tmp:(.text.arm_ipi_reschedule_handler.cfi)
ffffffff00007cb8         56007cb8       58     1                 arm_ipi_reschedule_handler.cfi
ffffffff00007cb8         56007cb8        0     1                 $x.55
ffffffff00007d10         56007d10      270     4         lto.tmp:(.text.arm64_mmu_map_pt)
ffffffff00007d10         56007d10      270     1                 arm64_mmu_map_pt
ffffffff00007d10         56007d10        0     1                 $x.56
ffffffff00007f80         56007f80      144     4         lto.tmp:(.text.arm64_mmu_unmap_pt)
ffffffff00007f80         56007f80      144     1                 arm64_mmu_unmap_pt
ffffffff00007f80         56007f80        0     1                 $x.57
ffffffff000080c4         560080c4      460     4         lto.tmp:(.text.arm64_early_mmu_init)
ffffffff000080c4         560080c4        0     1                 $x.58
ffffffff000080c4         560080c4      460     1                 arm64_early_mmu_init
ffffffff00008524         56008524      430     4         lto.tmp:(.text.arch_mmu_query)
ffffffff00008524         56008524      430     1                 arch_mmu_query
ffffffff00008524         56008524        0     1                 $x.59
ffffffff00008954         56008954      48c     4         lto.tmp:(.text.arm64_mmu_map_aspace)
ffffffff00008954         56008954      48c     1                 arm64_mmu_map_aspace
ffffffff00008954         56008954        0     1                 $x.61
ffffffff00008de0         56008de0      2f8     4         lto.tmp:(.text.arm64_mmu_map_pt.150)
ffffffff00008de0         56008de0      2f8     1                 arm64_mmu_map_pt.150
ffffffff00008de0         56008de0        0     1                 $x.63
ffffffff000090d8         560090d8      1a4     4         lto.tmp:(.text.arm64_tlbflush_if_asid_changed)
ffffffff000090d8         560090d8      1a4     1                 arm64_tlbflush_if_asid_changed
ffffffff000090d8         560090d8        0     1                 $x.64
ffffffff0000927c         5600927c      240     4         lto.tmp:(.text.arm64_mmu_unmap_pt.164)
ffffffff0000927c         5600927c      240     1                 arm64_mmu_unmap_pt.164
ffffffff0000927c         5600927c        0     1                 $x.65
ffffffff000094bc         560094bc      2d8     4         lto.tmp:(.text.arch_mmu_unmap)
ffffffff000094bc         560094bc      2d8     1                 arch_mmu_unmap
ffffffff000094bc         560094bc        0     1                 $x.66
ffffffff00009794         56009794      2a0     4         lto.tmp:(.text.arch_mmu_init_aspace)
ffffffff00009794         56009794      2a0     1                 arch_mmu_init_aspace
ffffffff00009794         56009794        0     1                 $x.67
ffffffff00009a34         56009a34       fc     4         lto.tmp:(.text.arm_gic_suspend_cpu.cfi)
ffffffff00009a34         56009a34       fc     1                 arm_gic_suspend_cpu.cfi
ffffffff00009a34         56009a34        0     1                 $x.68
ffffffff00009b30         56009b30      308     4         lto.tmp:(.text.arm_gic_resume_cpu.cfi)
ffffffff00009b30         56009b30      308     1                 arm_gic_resume_cpu.cfi
ffffffff00009b30         56009b30        0     1                 $x.69
ffffffff00009e38         56009e38       b8     4         lto.tmp:(.text.arm_gic_init_percpu.cfi)
ffffffff00009e38         56009e38       b8     1                 arm_gic_init_percpu.cfi
ffffffff00009e38         56009e38        0     1                 $x.70
ffffffff00009ef0         56009ef0      1e8     4         lto.tmp:(.text.register_int_handler)
ffffffff00009ef0         56009ef0      1e8     1                 register_int_handler
ffffffff00009ef0         56009ef0        0     1                 $x.71
ffffffff0000a0d8         5600a0d8       dc     4         lto.tmp:(.text.arm_gic_init)
ffffffff0000a0d8         5600a0d8       dc     1                 arm_gic_init
ffffffff0000a0d8         5600a0d8        0     1                 $x.72
ffffffff0000a1b4         5600a1b4      160     4         lto.tmp:(.text.platform_irq)
ffffffff0000a1b4         5600a1b4        0     1                 $x.73
ffffffff0000a1b4         5600a1b4      160     1                 platform_irq
ffffffff0000a314         5600a314       ec     4         lto.tmp:(.text.smc_intc_get_next_irq.cfi)
ffffffff0000a314         5600a314       ec     1                 smc_intc_get_next_irq.cfi
ffffffff0000a314         5600a314        0     1                 $x.74
ffffffff0000a400         5600a400       b8     4         lto.tmp:(.text.sm_intc_fiq_enter)
ffffffff0000a400         5600a400       b8     1                 sm_intc_fiq_enter
ffffffff0000a400         5600a400        0     1                 $x.75
ffffffff0000a4b8         5600a4b8      134     4         lto.tmp:(.text.arm_gicv3_configure_irq_locked)
ffffffff0000a4b8         5600a4b8      134     1                 arm_gicv3_configure_irq_locked
ffffffff0000a4b8         5600a4b8        0     1                 $x.76
ffffffff0000a5ec         5600a5ec       50     4         lto.tmp:(.text.arm_generic_timer_suspend_cpu.cfi)
ffffffff0000a5ec         5600a5ec       50     1                 arm_generic_timer_suspend_cpu.cfi
ffffffff0000a5ec         5600a5ec        0     1                 $x.77
ffffffff0000a63c         5600a63c       54     4         lto.tmp:(.text.arm_generic_timer_resume_cpu.cfi)
ffffffff0000a63c         5600a63c       54     1                 arm_generic_timer_resume_cpu.cfi
ffffffff0000a63c         5600a63c        0     1                 $x.78
ffffffff0000a690         5600a690       bc     4         lto.tmp:(.text.arm_generic_timer_init_secondary_cpu.cfi)
ffffffff0000a690         5600a690       bc     1                 arm_generic_timer_init_secondary_cpu.cfi
ffffffff0000a690         5600a690        0     1                 $x.79
ffffffff0000a74c         5600a74c       90     4         lto.tmp:(.text.platform_tick.cfi)
ffffffff0000a74c         5600a74c       90     1                 platform_tick.cfi
ffffffff0000a74c         5600a74c        0     1                 $x.80
ffffffff0000a7dc         5600a7dc       7c     4         lto.tmp:(.text.platform_set_oneshot_timer)
ffffffff0000a7dc         5600a7dc       7c     1                 platform_set_oneshot_timer
ffffffff0000a7dc         5600a7dc        0     1                 $x.81
ffffffff0000a858         5600a858       5c     4         lto.tmp:(.text.current_time_ns)
ffffffff0000a858         5600a858       5c     1                 current_time_ns
ffffffff0000a858         5600a858        0     1                 $x.82
ffffffff0000a8b4         5600a8b4      c30     4         lto.tmp:(.text.arm_generic_timer_init)
ffffffff0000a8b4         5600a8b4      c30     1                 arm_generic_timer_init
ffffffff0000a8b4         5600a8b4        0     1                 $x.83
ffffffff0000b4e4         5600b4e4      2ac     4         lto.tmp:(.text.event_destroy)
ffffffff0000b4e4         5600b4e4      2ac     1                 event_destroy
ffffffff0000b4e4         5600b4e4        0     1                 $x.84
ffffffff0000b790         5600b790      1e0     4         lto.tmp:(.text.event_wait_timeout)
ffffffff0000b790         5600b790      1e0     1                 event_wait_timeout
ffffffff0000b790         5600b790        0     1                 $x.85
ffffffff0000b970         5600b970      1f4     4         lto.tmp:(.text.event_signal)
ffffffff0000b970         5600b970      1f4     1                 event_signal
ffffffff0000b970         5600b970        0     1                 $x.86
ffffffff0000bb64         5600bb64      230     4         lto.tmp:(.text.mutex_acquire_timeout)
ffffffff0000bb64         5600bb64      230     1                 mutex_acquire_timeout
ffffffff0000bb64         5600bb64        0     1                 $x.87
ffffffff0000bd94         5600bd94      218     4         lto.tmp:(.text.mutex_release)
ffffffff0000bd94         5600bd94      218     1                 mutex_release
ffffffff0000bd94         5600bd94        0     1                 $x.88
ffffffff0000bfac         5600bfac      3d8     4         lto.tmp:(.text.thread_create_etc)
ffffffff0000bfac         5600bfac      3d8     1                 thread_create_etc
ffffffff0000bfac         5600bfac        0     1                 $x.89
ffffffff0000c384         5600c384      6c0     4         lto.tmp:(.text.thread_set_pinned_cpu)
ffffffff0000c384         5600c384      6c0     1                 thread_set_pinned_cpu
ffffffff0000c384         5600c384        0     1                 $x.90
ffffffff0000ca44         5600ca44       c0     4         lto.tmp:(.text.thread_preempt_lock_held)
ffffffff0000ca44         5600ca44       c0     1                 thread_preempt_lock_held
ffffffff0000ca44         5600ca44        0     1                 $x.92
ffffffff0000cb04         5600cb04      2c8     4         lto.tmp:(.text.thread_cond_mp_reschedule)
ffffffff0000cb04         5600cb04      2c8     1                 thread_cond_mp_reschedule
ffffffff0000cb04         5600cb04        0     1                 $x.93
ffffffff0000cdcc         5600cdcc      25c     4         lto.tmp:(.text.thread_preempt_inner)
ffffffff0000cdcc         5600cdcc      25c     1                 thread_preempt_inner
ffffffff0000cdcc         5600cdcc        0     1                 $x.94
ffffffff0000d028         5600d028      200     4         lto.tmp:(.text.insert_in_run_queue_head)
ffffffff0000d028         5600d028      200     1                 insert_in_run_queue_head
ffffffff0000d028         5600d028        0     1                 $x.95
ffffffff0000d228         5600d228      208     4         lto.tmp:(.text.insert_in_run_queue_tail)
ffffffff0000d228         5600d228      208     1                 insert_in_run_queue_tail
ffffffff0000d228         5600d228        0     1                 $x.96
ffffffff0000d430         5600d430      74c     4         lto.tmp:(.text.thread_resched)
ffffffff0000d430         5600d430      74c     1                 thread_resched
ffffffff0000d430         5600d430        0     1                 $x.97
ffffffff0000db7c         5600db7c      11c     4         lto.tmp:(.text.thread_mp_reschedule)
ffffffff0000db7c         5600db7c      11c     1                 thread_mp_reschedule
ffffffff0000db7c         5600db7c        0     1                 $x.98
ffffffff0000dc98         5600dc98      198     4         lto.tmp:(.text.thread_timer_callback.cfi)
ffffffff0000dc98         5600dc98      198     1                 thread_timer_callback.cfi
ffffffff0000dc98         5600dc98        0     1                 $x.99
ffffffff0000de30         5600de30      1d8     4         lto.tmp:(.text.thread_set_real_time)
ffffffff0000de30         5600de30      1d8     1                 thread_set_real_time
ffffffff0000de30         5600de30        0     1                 $x.100
ffffffff0000e008         5600e008      488     4         lto.tmp:(.text.thread_resume)
ffffffff0000e008         5600e008      488     1                 thread_resume
ffffffff0000e008         5600e008        0     1                 $x.101
ffffffff0000e490         5600e490      214     4         lto.tmp:(.text.thread_yield)
ffffffff0000e490         5600e490      214     1                 thread_yield
ffffffff0000e490         5600e490        0     1                 $x.102
ffffffff0000e6a4         5600e6a4      240     4         lto.tmp:(.text.thread_detach)
ffffffff0000e6a4         5600e6a4      240     1                 thread_detach
ffffffff0000e6a4         5600e6a4        0     1                 $x.103
ffffffff0000e8e4         5600e8e4      4c4     4         lto.tmp:(.text.wait_queue_wake_all)
ffffffff0000e8e4         5600e8e4      4c4     1                 wait_queue_wake_all
ffffffff0000e8e4         5600e8e4        0     1                 $x.104
ffffffff0000eda8         5600eda8      384     4         lto.tmp:(.text.thread_join)
ffffffff0000eda8         5600eda8      384     1                 thread_join
ffffffff0000eda8         5600eda8        0     1                 $x.105
ffffffff0000f12c         5600f12c      364     4         lto.tmp:(.text.wait_queue_block)
ffffffff0000f12c         5600f12c      364     1                 wait_queue_block
ffffffff0000f12c         5600f12c        0     1                 $x.106
ffffffff0000f490         5600f490      110     4         lto.tmp:(.text.thread_free)
ffffffff0000f490         5600f490      110     1                 thread_free
ffffffff0000f490         5600f490        0     1                 $x.107
ffffffff0000f5a0         5600f5a0      4d0     4         lto.tmp:(.text.wait_queue_timeout_handler.cfi)
ffffffff0000f5a0         5600f5a0      4d0     1                 wait_queue_timeout_handler.cfi
ffffffff0000f5a0         5600f5a0        0     1                 $x.108
ffffffff0000fa70         5600fa70      210     4         lto.tmp:(.text.thread_exit)
ffffffff0000fa70         5600fa70      210     1                 thread_exit
ffffffff0000fa70         5600fa70        0     1                 $x.109
ffffffff0000fc80         5600fc80      174     4         lto.tmp:(.text.thread_exit_from_panic)
ffffffff0000fc80         5600fc80      174     1                 thread_exit_from_panic
ffffffff0000fc80         5600fc80        0     1                 $x.110
ffffffff0000fdf4         5600fdf4       e0     4         lto.tmp:(.text.thread_unlock_prepare)
ffffffff0000fdf4         5600fdf4       e0     1                 thread_unlock_prepare
ffffffff0000fdf4         5600fdf4        0     1                 $x.111
ffffffff0000fed4         5600fed4       3c     4         lto.tmp:(.text.spin_unlock_restore)
ffffffff0000fed4         5600fed4       3c     1                 spin_unlock_restore
ffffffff0000fed4         5600fed4        0     1                 $x.112
ffffffff0000ff10         5600ff10        8     4         lto.tmp:(.text.thread_preempt)
ffffffff0000ff10         5600ff10        0     1                 $x.113
ffffffff0000ff10         5600ff10        8     1                 thread_preempt
ffffffff0000ff18         5600ff18      26c     4         lto.tmp:(.text.thread_sleep_ns)
ffffffff0000ff18         5600ff18      26c     1                 thread_sleep_ns
ffffffff0000ff18         5600ff18        0     1                 $x.114
ffffffff00010184         56010184      294     4         lto.tmp:(.text.thread_sleep_handler.cfi)
ffffffff00010184         56010184      294     1                 thread_sleep_handler.cfi
ffffffff00010184         56010184        0     1                 $x.115
ffffffff00010418         56010418       6c     4         lto.tmp:(.text.thread_sleep_until_ns)
ffffffff00010418         56010418       6c     1                 thread_sleep_until_ns
ffffffff00010418         56010418        0     1                 $x.116
ffffffff00010484         56010484      1dc     4         lto.tmp:(.text.reaper_thread_routine.cfi)
ffffffff00010484         56010484      1dc     1                 reaper_thread_routine.cfi
ffffffff00010484         56010484        0     1                 $x.117
ffffffff00010660         56010660       18     4         lto.tmp:(.text.thread_set_name)
ffffffff00010660         56010660       18     1                 thread_set_name
ffffffff00010660         56010660        0     1                 $x.118
ffffffff00010678         56010678       f0     4         lto.tmp:(.text.thread_become_idle)
ffffffff00010678         56010678       f0     1                 thread_become_idle
ffffffff00010678         56010678        0     1                 $x.119
ffffffff00010768         56010768        4     4         lto.tmp:(.text.arch_curr_cpu_num)
ffffffff00010768         56010768        4     1                 arch_curr_cpu_num
ffffffff00010768         56010768        0     1                 $x.120
ffffffff0001076c         5601076c       4c     4         lto.tmp:(.text.thread_secondary_cpu_entry)
ffffffff0001076c         5601076c       4c     1                 thread_secondary_cpu_entry
ffffffff0001076c         5601076c        0     1                 $x.121
ffffffff000107b8         560107b8      464     4         lto.tmp:(.text.wait_queue_wake_one)
ffffffff000107b8         560107b8      464     1                 wait_queue_wake_one
ffffffff000107b8         560107b8        0     1                 $x.122
ffffffff00010c1c         56010c1c      2c0     4         lto.tmp:(.text.timer_set)
ffffffff00010c1c         56010c1c      2c0     1                 timer_set
ffffffff00010c1c         56010c1c        0     1                 $x.123
ffffffff00010edc         56010edc       ec     4         lto.tmp:(.text.insert_timer_in_queue)
ffffffff00010edc         56010edc       ec     1                 insert_timer_in_queue
ffffffff00010edc         56010edc        0     1                 $x.124
ffffffff00010fc8         56010fc8      334     4         lto.tmp:(.text.timer_tick.cfi)
ffffffff00010fc8         56010fc8      334     1                 timer_tick.cfi
ffffffff00010fc8         56010fc8        0     1                 $x.125
ffffffff000112fc         560112fc      3a0     4         lto.tmp:(.text.timer_cancel_etc)
ffffffff000112fc         560112fc      3a0     1                 timer_cancel_etc
ffffffff000112fc         560112fc        0     1                 $x.126
ffffffff0001169c         5601169c      1b0     4         lto.tmp:(.text.sem_post)
ffffffff0001169c         5601169c      1b0     1                 sem_post
ffffffff0001169c         5601169c        0     1                 $x.127
ffffffff0001184c         5601184c       3c     4         lto.tmp:(.text.mp_set_curr_cpu_active)
ffffffff0001184c         5601184c       3c     1                 mp_set_curr_cpu_active
ffffffff0001184c         5601184c        0     1                 $x.128
ffffffff00011888         56011888       c0     4         lto.tmp:(.text.phys_mem_obj_check_flags.cfi)
ffffffff00011888         56011888       c0     1                 phys_mem_obj_check_flags.cfi
ffffffff00011888         56011888        0     1                 $x.129
ffffffff00011948         56011948       68     4         lto.tmp:(.text.phys_mem_obj_get_page.cfi)
ffffffff00011948         56011948       68     1                 phys_mem_obj_get_page.cfi
ffffffff00011948         56011948        0     1                 $x.130
ffffffff000119b0         560119b0       5c     4         lto.tmp:(.text.phys_mem_obj_destroy.cfi)
ffffffff000119b0         560119b0       5c     1                 phys_mem_obj_destroy.cfi
ffffffff000119b0         560119b0        0     1                 $x.131
ffffffff00011a0c         56011a0c      2b8     4         lto.tmp:(.text.pmm_add_arena)
ffffffff00011a0c         56011a0c      2b8     1                 pmm_add_arena
ffffffff00011a0c         56011a0c        0     1                 $x.132
ffffffff00011cc4         56011cc4       e4     4         lto.tmp:(.text.pmm_unreserve_pages)
ffffffff00011cc4         56011cc4       e4     1                 pmm_unreserve_pages
ffffffff00011cc4         56011cc4        0     1                 $x.133
ffffffff00011da8         56011da8      4ec     4         lto.tmp:(.text.pmm_alloc_from_res_group)
ffffffff00011da8         56011da8      4ec     1                 pmm_alloc_from_res_group
ffffffff00011da8         56011da8        0     1                 $x.134
ffffffff00012294         56012294      514     4         lto.tmp:(.text.pmm_alloc_pages_locked)
ffffffff00012294         56012294      514     1                 pmm_alloc_pages_locked
ffffffff00012294         56012294        0     1                 $x.135
ffffffff000127a8         560127a8        8     4         lto.tmp:(.text.pmm_vmm_obj_check_flags.cfi)
ffffffff000127a8         560127a8        8     1                 pmm_vmm_obj_check_flags.cfi
ffffffff000127a8         560127a8        0     1                 $x.136
ffffffff000127b0         560127b0       f8     4         lto.tmp:(.text.pmm_vmm_obj_get_page.cfi)
ffffffff000127b0         560127b0       f8     1                 pmm_vmm_obj_get_page.cfi
ffffffff000127b0         560127b0        0     1                 $x.137
ffffffff000128a8         560128a8       cc     4         lto.tmp:(.text.pmm_vmm_obj_destroy.cfi)
ffffffff000128a8         560128a8       cc     1                 pmm_vmm_obj_destroy.cfi
ffffffff000128a8         560128a8        0     1                 $x.138
ffffffff00012974         56012974      148     4         lto.tmp:(.text.pmm_free_locked)
ffffffff00012974         56012974      148     1                 pmm_free_locked
ffffffff00012974         56012974        0     1                 $x.139
ffffffff00012abc         56012abc       c8     4         lto.tmp:(.text.pmm_set_cleared)
ffffffff00012abc         56012abc       c8     1                 pmm_set_cleared
ffffffff00012abc         56012abc        0     1                 $x.140
ffffffff00012b84         56012b84       64     4         lto.tmp:(.text.pmm_set_tagged)
ffffffff00012b84         56012b84       64     1                 pmm_set_tagged
ffffffff00012b84         56012b84        0     1                 $x.141
ffffffff00012be8         56012be8       70     4         lto.tmp:(.text.pmm_free)
ffffffff00012be8         56012be8       70     1                 pmm_free
ffffffff00012be8         56012be8        0     1                 $x.142
ffffffff00012c58         56012c58      158     4         lto.tmp:(.text.pmm_alloc_contiguous)
ffffffff00012c58         56012c58      158     1                 pmm_alloc_contiguous
ffffffff00012c58         56012c58        0     1                 $x.143
ffffffff00012db0         56012db0      198     4         lto.tmp:(.text.res_group_del_ref)
ffffffff00012db0         56012db0      198     1                 res_group_del_ref
ffffffff00012db0         56012db0        0     1                 $x.144
ffffffff00012f48         56012f48      1dc     4         lto.tmp:(.text.vm_init_preheap.cfi)
ffffffff00012f48         56012f48      1dc     1                 vm_init_preheap.cfi
ffffffff00012f48         56012f48        0     1                 $x.145
ffffffff00013124         56013124      220     4         lto.tmp:(.text.mark_pages_in_use)
ffffffff00013124         56013124      220     1                 mark_pages_in_use
ffffffff00013124         56013124        0     1                 $x.146
ffffffff00013344         56013344      208     4         lto.tmp:(.text.vm_init_postheap.cfi)
ffffffff00013344         56013344      208     1                 vm_init_postheap.cfi
ffffffff00013344         56013344        0     1                 $x.147
ffffffff0001354c         5601354c      150     4         lto.tmp:(.text.paddr_to_kvaddr)
ffffffff0001354c         5601354c      150     1                 paddr_to_kvaddr
ffffffff0001354c         5601354c        0     1                 $x.148
ffffffff0001369c         5601369c       ac     4         lto.tmp:(.text.vaddr_to_paddr)
ffffffff0001369c         5601369c       ac     1                 vaddr_to_paddr
ffffffff0001369c         5601369c        0     1                 $x.149
ffffffff00013748         56013748       68     4         lto.tmp:(.text.vaddr_to_aspace)
ffffffff00013748         56013748       68     1                 vaddr_to_aspace
ffffffff00013748         56013748        0     1                 $x.150
ffffffff000137b0         560137b0       dc     4         lto.tmp:(.text.vmm_obj_slice_release)
ffffffff000137b0         560137b0       dc     1                 vmm_obj_slice_release
ffffffff000137b0         560137b0        0     1                 $x.151
ffffffff0001388c         5601388c       e0     4         lto.tmp:(.text.vmm_obj_del_ref)
ffffffff0001388c         5601388c       e0     1                 vmm_obj_del_ref
ffffffff0001388c         5601388c        0     1                 $x.152
ffffffff0001396c         5601396c       6c     4         lto.tmp:(.text.vmm_obj_slice_bind)
ffffffff0001396c         5601396c       6c     1                 vmm_obj_slice_bind
ffffffff0001396c         5601396c        0     1                 $x.153
ffffffff000139d8         560139d8       d8     4         lto.tmp:(.text.vmm_obj_slice_bind_locked)
ffffffff000139d8         560139d8       d8     1                 vmm_obj_slice_bind_locked
ffffffff000139d8         560139d8        0     1                 $x.154
ffffffff00013ab0         56013ab0      40c     4         lto.tmp:(.text.alloc_spot)
ffffffff00013ab0         56013ab0      40c     1                 alloc_spot
ffffffff00013ab0         56013ab0        0     1                 $x.155
ffffffff00013ebc         56013ebc      148     4         lto.tmp:(.text.scan_gap)
ffffffff00013ebc         56013ebc      148     1                 scan_gap
ffffffff00013ebc         56013ebc        0     1                 $x.156
ffffffff00014004         56014004      14c     4         lto.tmp:(.text.spot_in_gap)
ffffffff00014004         56014004      14c     1                 spot_in_gap
ffffffff00014004         56014004        0     1                 $x.157
ffffffff00014150         56014150      618     4         lto.tmp:(.text.alloc_region)
ffffffff00014150         56014150      618     1                 alloc_region
ffffffff00014150         56014150        0     1                 $x.158
ffffffff00014768         56014768      194     4         lto.tmp:(.text.vmm_find_region_in_bst)
ffffffff00014768         56014768      194     1                 vmm_find_region_in_bst
ffffffff00014768         56014768        0     1                 $x.159
ffffffff000148fc         560148fc       e4     4         lto.tmp:(.text.is_range_inside_region)
ffffffff000148fc         560148fc       e4     1                 is_range_inside_region
ffffffff000148fc         560148fc        0     1                 $x.160
ffffffff000149e0         560149e0        8     4         lto.tmp:(.text.vmm_res_obj_check_flags.cfi)
ffffffff000149e0         560149e0        8     1                 vmm_res_obj_check_flags.cfi
ffffffff000149e0         560149e0        0     1                 $x.161
ffffffff000149e8         560149e8      468     4         lto.tmp:(.text.vmm_alloc_obj)
ffffffff000149e8         560149e8      468     1                 vmm_alloc_obj
ffffffff000149e8         560149e8        0     1                 $x.163
ffffffff00014e50         56014e50      28c     4         lto.tmp:(.text.vmm_map_obj_locked)
ffffffff00014e50         56014e50      28c     1                 vmm_map_obj_locked
ffffffff00014e50         56014e50        0     1                 $x.164
ffffffff000150dc         560150dc      224     4         lto.tmp:(.text.vmm_alloc_physical_etc)
ffffffff000150dc         560150dc      224     1                 vmm_alloc_physical_etc
ffffffff000150dc         560150dc        0     1                 $x.165
ffffffff00015300         56015300      1c0     4         lto.tmp:(.text.vmm_alloc)
ffffffff00015300         56015300      1c0     1                 vmm_alloc
ffffffff00015300         56015300        0     1                 $x.166
ffffffff000154c0         560154c0      21c     4         lto.tmp:(.text.vmm_alloc_no_physical)
ffffffff000154c0         560154c0      21c     1                 vmm_alloc_no_physical
ffffffff000154c0         560154c0        0     1                 $x.167
ffffffff000156dc         560156dc        8     4         lto.tmp:(.text.vmm_res_obj_get_page.cfi)
ffffffff000156dc         560156dc        8     1                 vmm_res_obj_get_page.cfi
ffffffff000156dc         560156dc        0     1                 $x.168
ffffffff000156e4         560156e4      2b8     4         lto.tmp:(.text.vmm_res_obj_destroy.cfi)
ffffffff000156e4         560156e4      2b8     1                 vmm_res_obj_destroy.cfi
ffffffff000156e4         560156e4        0     1                 $x.169
ffffffff0001599c         5601599c      240     4         lto.tmp:(.text.vmm_get_address_description)
ffffffff0001599c         5601599c      240     1                 vmm_get_address_description
ffffffff0001599c         5601599c        0     1                 $x.170
ffffffff00015bdc         56015bdc      234     4         lto.tmp:(.text.vmm_get_obj)
ffffffff00015bdc         56015bdc      234     1                 vmm_get_obj
ffffffff00015bdc         56015bdc        0     1                 $x.171
ffffffff00015e10         56015e10      230     4         lto.tmp:(.text.vmm_free_region_etc)
ffffffff00015e10         56015e10      230     1                 vmm_free_region_etc
ffffffff00015e10         56015e10        0     1                 $x.172
ffffffff00016040         56016040      534     4         lto.tmp:(.text.vmm_free_aspace)
ffffffff00016040         56016040      534     1                 vmm_free_aspace
ffffffff00016040         56016040        0     1                 $x.173
ffffffff00016574         56016574      358     4         lto.tmp:(.text.vmm_context_switch)
ffffffff00016574         56016574      358     1                 vmm_context_switch
ffffffff00016574         56016574        0     1                 $x.174
ffffffff000168cc         560168cc      1b4     4         lto.tmp:(.text.vmm_set_active_aspace)
ffffffff000168cc         560168cc      1b4     1                 vmm_set_active_aspace
ffffffff000168cc         560168cc        0     1                 $x.175
ffffffff00016a80         56016a80      488     4         lto.tmp:(.text.bst_update_rank_insert)
ffffffff00016a80         56016a80      488     1                 bst_update_rank_insert
ffffffff00016a80         56016a80        0     1                 $x.176
ffffffff00016f08         56016f08      7b8     4         lto.tmp:(.text.bst_delete)
ffffffff00016f08         56016f08      7b8     1                 bst_delete
ffffffff00016f08         56016f08        0     1                 $x.177
ffffffff000176c0         560176c0      240     4         lto.tmp:(.text._panic)
ffffffff000176c0         560176c0        0     1                 $x.178
ffffffff000176c0         560176c0      240     1                 _panic
ffffffff00017900         56017900        c     4         lto.tmp:(.text.malloc)
ffffffff00017900         56017900        0     1                 $x.179
ffffffff00017900         56017900        c     1                 malloc
ffffffff0001790c         5601790c        4     4         lto.tmp:(.text.memalign)
ffffffff0001790c         5601790c        4     1                 memalign
ffffffff0001790c         5601790c        0     1                 $x.180
ffffffff00017910         56017910       4c     4         lto.tmp:(.text.calloc)
ffffffff00017910         56017910        0     1                 $x.181
ffffffff00017910         56017910       4c     1                 calloc
ffffffff0001795c         5601795c        4     4         lto.tmp:(.text.free)
ffffffff0001795c         5601795c        0     1                 $x.183
ffffffff0001795c         5601795c        4     1                 free
ffffffff00017960         56017960      27c     4         lto.tmp:(.text.miniheap_memalign)
ffffffff00017960         56017960      27c     1                 miniheap_memalign
ffffffff00017960         56017960        0     1                 $x.184
ffffffff00017bdc         56017bdc      178     4         lto.tmp:(.text.heap_insert_free_chunk)
ffffffff00017bdc         56017bdc      178     1                 heap_insert_free_chunk
ffffffff00017bdc         56017bdc        0     1                 $x.185
ffffffff00017d54         56017d54       d0     4         lto.tmp:(.text.miniheap_free)
ffffffff00017d54         56017d54       d0     1                 miniheap_free
ffffffff00017d54         56017d54        0     1                 $x.186
ffffffff00017e24         56017e24      330     4         lto.tmp:(.text.__debug_stdio_write.cfi)
ffffffff00017e24         56017e24      330     1                 __debug_stdio_write.cfi
ffffffff00017e24         56017e24        0     1                 $x.187
ffffffff00018154         56018154      19c     4         lto.tmp:(.text.__debug_stdio_write_commit.cfi)
ffffffff00018154         56018154      19c     1                 __debug_stdio_write_commit.cfi
ffffffff00018154         56018154        0     1                 $x.188
ffffffff000182f0         560182f0       2c     4         lto.tmp:(.text.__debug_stdio_read.cfi)
ffffffff000182f0         560182f0       2c     1                 __debug_stdio_read.cfi
ffffffff000182f0         560182f0        0     1                 $x.189
ffffffff0001831c         5601831c       d8     4         lto.tmp:(.text.__debug_stdio_lock.cfi)
ffffffff0001831c         5601831c       d8     1                 __debug_stdio_lock.cfi
ffffffff0001831c         5601831c        0     1                 $x.190
ffffffff000183f4         560183f4       f8     4         lto.tmp:(.text.__debug_stdio_unlock.cfi)
ffffffff000183f4         560183f4       f8     1                 __debug_stdio_unlock.cfi
ffffffff000183f4         560183f4        0     1                 $x.191
ffffffff000184ec         560184ec       84     4         lto.tmp:(.text.io_write)
ffffffff000184ec         560184ec        0     1                 $x.192
ffffffff000184ec         560184ec       84     1                 io_write
ffffffff00018570         56018570       88     4         lto.tmp:(.text.io_write_commit)
ffffffff00018570         56018570        0     1                 $x.193
ffffffff00018570         56018570       88     1                 io_write_commit
ffffffff000185f8         560185f8       88     4         lto.tmp:(.text.io_lock)
ffffffff000185f8         560185f8        0     1                 $x.194
ffffffff000185f8         560185f8       88     1                 io_lock
ffffffff00018680         56018680       88     4         lto.tmp:(.text.io_unlock)
ffffffff00018680         56018680        0     1                 $x.195
ffffffff00018680         56018680       88     1                 io_unlock
ffffffff00018708         56018708      a98     4         lto.tmp:(.text.lk_main)
ffffffff00018708         56018708        0     1                 $x.197
ffffffff00018708         56018708      a98     1                 lk_main
ffffffff000191a0         560191a0      7a4     4         lto.tmp:(.text.bootstrap2.cfi)
ffffffff000191a0         560191a0      7a4     1                 bootstrap2.cfi
ffffffff000191a0         560191a0        0     1                 $x.198
ffffffff00019944         56019944      120     4         lto.tmp:(.text.secondary_cpu_bootstrap2.cfi)
ffffffff00019944         56019944      120     1                 secondary_cpu_bootstrap2.cfi
ffffffff00019944         56019944        0     1                 $x.199
ffffffff00019a64         56019a64      1cc     4         lto.tmp:(.text.busy_test_init.cfi)
ffffffff00019a64         56019a64      1cc     1                 busy_test_init.cfi
ffffffff00019a64         56019a64        0     1                 $x.200
ffffffff00019c30         56019c30      1a0     4         lto.tmp:(.text.busy_test_server.cfi)
ffffffff00019c30         56019c30      1a0     1                 busy_test_server.cfi
ffffffff00019c30         56019c30        0     1                 $x.201
ffffffff00019dd0         56019dd0       6c     4         lto.tmp:(.text.busy_test_cpu_init.cfi)
ffffffff00019dd0         56019dd0       6c     1                 busy_test_cpu_init.cfi
ffffffff00019dd0         56019dd0        0     1                 $x.202
ffffffff00019e3c         56019e3c       28     4         lto.tmp:(.text.busy_test_busy_func.cfi)
ffffffff00019e3c         56019e3c       28     1                 busy_test_busy_func.cfi
ffffffff00019e3c         56019e3c        0     1                 $x.203
ffffffff00019e64         56019e64      138     4         lto.tmp:(.text.app_manifest_read_string)
ffffffff00019e64         56019e64      138     1                 app_manifest_read_string
ffffffff00019e64         56019e64        0     1                 $x.204
ffffffff00019f9c         56019f9c      814     4         lto.tmp:(.text.arm_ffa_init.cfi)
ffffffff00019f9c         56019f9c      814     1                 arm_ffa_init.cfi
ffffffff00019f9c         56019f9c        0     1                 $x.205
ffffffff0001a7b0         5601a7b0      3b4     4         lto.tmp:(.text.arm_ffa_sched_nonsecure)
ffffffff0001a7b0         5601a7b0      3b4     1                 arm_ffa_sched_nonsecure
ffffffff0001a7b0         5601a7b0        0     1                 $x.206
ffffffff0001ab64         5601ab64      190     4         lto.tmp:(.text.arm_ffa_rx_release)
ffffffff0001ab64         5601ab64      190     1                 arm_ffa_rx_release
ffffffff0001ab64         5601ab64        0     1                 $x.207
ffffffff0001acf4         5601acf4      324     4         lto.tmp:(.text.dump_thread_backtrace)
ffffffff0001acf4         5601acf4      324     1                 dump_thread_backtrace
ffffffff0001acf4         5601acf4        0     1                 $x.208
ffffffff0001b018         5601b018      2e0     4         lto.tmp:(.text.dump_function)
ffffffff0001b018         5601b018      2e0     1                 dump_function
ffffffff0001b018         5601b018        0     1                 $x.209
ffffffff0001b2f8         5601b2f8      128     4         lto.tmp:(.text.print_function_info)
ffffffff0001b2f8         5601b2f8      128     1                 print_function_info
ffffffff0001b2f8         5601b2f8        0     1                 $x.210
ffffffff0001b420         5601b420       ec     4         lto.tmp:(.text.ext_mem_obj_check_flags.cfi)
ffffffff0001b420         5601b420       ec     1                 ext_mem_obj_check_flags.cfi
ffffffff0001b420         5601b420        0     1                 $x.211
ffffffff0001b50c         5601b50c       94     4         lto.tmp:(.text.ext_mem_obj_get_page.cfi)
ffffffff0001b50c         5601b50c       94     1                 ext_mem_obj_get_page.cfi
ffffffff0001b50c         5601b50c        0     1                 $x.212
ffffffff0001b5a0         5601b5a0      1cc     4         lto.tmp:(.text.ext_mem_map_obj_id)
ffffffff0001b5a0         5601b5a0      1cc     1                 ext_mem_map_obj_id
ffffffff0001b5a0         5601b5a0        0     1                 $x.213
ffffffff0001b76c         5601b76c      268     4         lto.tmp:(.text.ktipc_server_add_port)
ffffffff0001b76c         5601b76c      268     1                 ktipc_server_add_port
ffffffff0001b76c         5601b76c        0     1                 $x.214
ffffffff0001b9d4         5601b9d4      294     4         lto.tmp:(.text.port_event_handler.cfi)
ffffffff0001b9d4         5601b9d4      294     1                 port_event_handler.cfi
ffffffff0001b9d4         5601b9d4        0     1                 $x.215
ffffffff0001bc68         5601bc68      298     4         lto.tmp:(.text.chan_event_handler.cfi)
ffffffff0001bc68         5601bc68      298     1                 chan_event_handler.cfi
ffffffff0001bc68         5601bc68        0     1                 $x.216
ffffffff0001bf00         5601bf00      1d8     4         lto.tmp:(.text.ksrv_thread.cfi)
ffffffff0001bf00         5601bf00      1d8     1                 ksrv_thread.cfi
ffffffff0001bf00         5601bf00        0     1                 $x.217
ffffffff0001c0d8         5601c0d8      1c4     4         lto.tmp:(.text.ktipc_recv_iov)
ffffffff0001c0d8         5601c0d8      1c4     1                 ktipc_recv_iov
ffffffff0001c0d8         5601c0d8        0     1                 $x.218
ffffffff0001c29c         5601c29c      384     4         lto.tmp:(.text.ktipctest_init.cfi)
ffffffff0001c29c         5601c29c      384     1                 ktipctest_init.cfi
ffffffff0001c29c         5601c29c        0     1                 $x.219
ffffffff0001c620         5601c620       ec     4         lto.tmp:(.text.run_ktipctest.cfi)
ffffffff0001c620         5601c620       ec     1                 run_ktipctest.cfi
ffffffff0001c620         5601c620        0     1                 $x.220
ffffffff0001c70c         5601c70c      294     4         lto.tmp:(.text.run_test_suite)
ffffffff0001c70c         5601c70c      294     1                 run_test_suite
ffffffff0001c70c         5601c70c        0     1                 $x.221
ffffffff0001c9a0         5601c9a0      2f8     4         lto.tmp:(.text.ktipctest_connecterr.cfi)
ffffffff0001c9a0         5601c9a0      2f8     1                 ktipctest_connecterr.cfi
ffffffff0001c9a0         5601c9a0        0     1                 $x.222
ffffffff0001cc98         5601cc98      1cc     4         lto.tmp:(.text.wait_for_hup)
ffffffff0001cc98         5601cc98      1cc     1                 wait_for_hup
ffffffff0001cc98         5601cc98        0     1                 $x.223
ffffffff0001ce64         5601ce64      2f8     4         lto.tmp:(.text.ktipctest_blockedport.cfi)
ffffffff0001ce64         5601ce64      2f8     1                 ktipctest_blockedport.cfi
ffffffff0001ce64         5601ce64        0     1                 $x.224
ffffffff0001d15c         5601d15c      6d8     4         lto.tmp:(.text.ktipctest_echo.cfi)
ffffffff0001d15c         5601d15c      6d8     1                 ktipctest_echo.cfi
ffffffff0001d15c         5601d15c        0     1                 $x.225
ffffffff0001d834         5601d834      1f0     4         lto.tmp:(.text.send_cmd)
ffffffff0001d834         5601d834      1f0     1                 send_cmd
ffffffff0001d834         5601d834        0     1                 $x.226
ffffffff0001da24         5601da24      8c0     4         lto.tmp:(.text.ktipctest_echo8.cfi)
ffffffff0001da24         5601da24      8c0     1                 ktipctest_echo8.cfi
ffffffff0001da24         5601da24        0     1                 $x.227
ffffffff0001e2e4         5601e2e4      7ec     4         lto.tmp:(.text.ktipctest_close.cfi)
ffffffff0001e2e4         5601e2e4      7ec     1                 ktipctest_close.cfi
ffffffff0001e2e4         5601e2e4        0     1                 $x.228
ffffffff0001ead0         5601ead0      73c     4         lto.tmp:(.text.ktipctest_blockedsend.cfi)
ffffffff0001ead0         5601ead0      73c     1                 ktipctest_blockedsend.cfi
ffffffff0001ead0         5601ead0        0     1                 $x.229
ffffffff0001f20c         5601f20c      180     4         lto.tmp:(.text.ktipc_test_server_init.cfi)
ffffffff0001f20c         5601f20c      180     1                 ktipc_test_server_init.cfi
ffffffff0001f20c         5601f20c        0     1                 $x.230
ffffffff0001f38c         5601f38c       3c     4         lto.tmp:(.text.connecterr_handle_connect.cfi)
ffffffff0001f38c         5601f38c       3c     1                 connecterr_handle_connect.cfi
ffffffff0001f38c         5601f38c        0     1                 $x.231
ffffffff0001f3c8         5601f3c8        8     4         lto.tmp:(.text.nop_handle_msg.cfi)
ffffffff0001f3c8         5601f3c8        8     1                 nop_handle_msg.cfi
ffffffff0001f3c8         5601f3c8        0     1                 $x.232
ffffffff0001f3d0         5601f3d0        4     4         lto.tmp:(.text.nop_handle_channel_cleanup.cfi)
ffffffff0001f3d0         5601f3d0        4     1                 nop_handle_channel_cleanup.cfi
ffffffff0001f3d0         5601f3d0        0     1                 $x.233
ffffffff0001f3d4         5601f3d4       6c     4         lto.tmp:(.text.test_handle_connect.cfi)
ffffffff0001f3d4         5601f3d4       6c     1                 test_handle_connect.cfi
ffffffff0001f3d4         5601f3d4        0     1                 $x.234
ffffffff0001f440         5601f440      388     4         lto.tmp:(.text.test_handle_msg.cfi)
ffffffff0001f440         5601f440      388     1                 test_handle_msg.cfi
ffffffff0001f440         5601f440        0     1                 $x.235
ffffffff0001f7c8         5601f7c8       60     4         lto.tmp:(.text.test_handle_channel_cleanup.cfi)
ffffffff0001f7c8         5601f7c8       60     1                 test_handle_channel_cleanup.cfi
ffffffff0001f7c8         5601f7c8        0     1                 $x.237
ffffffff0001f828         5601f828      1c8     4         lto.tmp:(.text.test_handle_send_unblocked.cfi)
ffffffff0001f828         5601f828      1c8     1                 test_handle_send_unblocked.cfi
ffffffff0001f828         5601f828        0     1                 $x.238
ffffffff0001f9f0         5601f9f0       6c     4         lto.tmp:(.text.parse_u8)
ffffffff0001f9f0         5601f9f0       6c     1                 parse_u8
ffffffff0001f9f0         5601f9f0        0     1                 $x.239
ffffffff0001fa5c         5601fa5c       98     4         lto.tmp:(.text.memlog_init.cfi)
ffffffff0001fa5c         5601fa5c       98     1                 memlog_init.cfi
ffffffff0001fa5c         5601fa5c        0     1                 $x.240
ffffffff0001faf4         5601faf4      37c     4         lto.tmp:(.text.memlog_stdcall.cfi)
ffffffff0001faf4         5601faf4      37c     1                 memlog_stdcall.cfi
ffffffff0001faf4         5601faf4        0     1                 $x.241
ffffffff0001fe70         5601fe70      190     4         lto.tmp:(.text.memlog_print_callback.cfi)
ffffffff0001fe70         5601fe70      190     1                 memlog_print_callback.cfi
ffffffff0001fe70         5601fe70        0     1                 $x.242
ffffffff00020000         56020000       d0     4         lto.tmp:(.text.memlog_commit_callback.cfi)
ffffffff00020000         56020000       d0     1                 memlog_commit_callback.cfi
ffffffff00020000         56020000        0     1                 $x.243
ffffffff000200d0         560200d0       d8     4         lto.tmp:(.text.handle_init_etc)
ffffffff000200d0         560200d0       d8     1                 handle_init_etc
ffffffff000200d0         560200d0        0     1                 $x.244
ffffffff000201a8         560201a8       88     4         lto.tmp:(.text.handle_decref)
ffffffff000201a8         560201a8       88     1                 handle_decref
ffffffff000201a8         560201a8        0     1                 $x.245
ffffffff00020230         56020230       c0     4         lto.tmp:(.text.handle_close)
ffffffff00020230         56020230       c0     1                 handle_close
ffffffff00020230         56020230        0     1                 $x.247
ffffffff000202f0         560202f0       c0     4         lto.tmp:(.text.handle_del_waiter)
ffffffff000202f0         560202f0       c0     1                 handle_del_waiter
ffffffff000202f0         560202f0        0     1                 $x.248
ffffffff000203b0         560203b0      1b8     4         lto.tmp:(.text.handle_wait)
ffffffff000203b0         560203b0      1b8     1                 handle_wait
ffffffff000203b0         560203b0        0     1                 $x.249
ffffffff00020568         56020568        c     4         lto.tmp:(.text.handle_event_waiter_notify.cfi)
ffffffff00020568         56020568        c     1                 handle_event_waiter_notify.cfi
ffffffff00020568         56020568        0     1                 $x.250
ffffffff00020574         56020574      11c     4         lto.tmp:(.text.handle_notify)
ffffffff00020574         56020574      11c     1                 handle_notify
ffffffff00020574         56020574        0     1                 $x.251
ffffffff00020690         56020690      1f0     4         lto.tmp:(.text.handle_list_add)
ffffffff00020690         56020690      1f0     1                 handle_list_add
ffffffff00020690         56020690        0     1                 $x.252
ffffffff00020880         56020880      15c     4         lto.tmp:(.text.handle_list_del)
ffffffff00020880         56020880      15c     1                 handle_list_del
ffffffff00020880         56020880        0     1                 $x.253
ffffffff000209dc         560209dc       a0     4         lto.tmp:(.text._finish_wait_handle)
ffffffff000209dc         560209dc       a0     1                 _finish_wait_handle
ffffffff000209dc         560209dc        0     1                 $x.254
ffffffff00020a7c         56020a7c       94     4         lto.tmp:(.text.hset_poll.cfi)
ffffffff00020a7c         56020a7c       94     1                 hset_poll.cfi
ffffffff00020a7c         56020a7c        0     1                 $x.255
ffffffff00020b10         56020b10       84     4         lto.tmp:(.text.hset_destroy.cfi)
ffffffff00020b10         56020b10       84     1                 hset_destroy.cfi
ffffffff00020b10         56020b10        0     1                 $x.256
ffffffff00020b94         56020b94      184     4         lto.tmp:(.text.handle_set_attach)
ffffffff00020b94         56020b94      184     1                 handle_set_attach
ffffffff00020b94         56020b94        0     1                 $x.257
ffffffff00020d18         56020d18       bc     4         lto.tmp:(.text.hset_find_target)
ffffffff00020d18         56020d18       bc     1                 hset_find_target
ffffffff00020d18         56020d18        0     1                 $x.258
ffffffff00020dd4         56020dd4      288     4         lto.tmp:(.text.hset_attach_ref)
ffffffff00020dd4         56020dd4      288     1                 hset_attach_ref
ffffffff00020dd4         56020dd4        0     1                 $x.259
ffffffff0002105c         5602105c      1a8     4         lto.tmp:(.text.hset_waiter_notify.cfi)
ffffffff0002105c         5602105c      1a8     1                 hset_waiter_notify.cfi
ffffffff0002105c         5602105c        0     1                 $x.260
ffffffff00021204         56021204      268     4         lto.tmp:(.text.handle_set_detach_ref)
ffffffff00021204         56021204      268     1                 handle_set_detach_ref
ffffffff00021204         56021204        0     1                 $x.261
ffffffff0002146c         5602146c      3e8     4         lto.tmp:(.text.handle_set_wait)
ffffffff0002146c         5602146c      3e8     1                 handle_set_wait
ffffffff0002146c         5602146c        0     1                 $x.262
ffffffff00021854         56021854        c     4         lto.tmp:(.text.handle_event_waiter_notify.991.cfi)
ffffffff00021854         56021854        c     1                 handle_event_waiter_notify.991.cfi
ffffffff00021854         56021854        0     1                 $x.263
ffffffff00021860         56021860      114     4         lto.tmp:(.text.handle_ref_wait)
ffffffff00021860         56021860      114     1                 handle_ref_wait
ffffffff00021860         56021860        0     1                 $x.264
ffffffff00021974         56021974       fc     4         lto.tmp:(.text.user_iovec_to_membuf)
ffffffff00021974         56021974       fc     1                 user_iovec_to_membuf
ffffffff00021974         56021974        0     1                 $x.265
ffffffff00021a70         56021a70      124     4         lto.tmp:(.text.chan_poll.cfi)
ffffffff00021a70         56021a70      124     1                 chan_poll.cfi
ffffffff00021a70         56021a70        0     1                 $x.266
ffffffff00021b94         56021b94       c4     4         lto.tmp:(.text.chan_handle_destroy.cfi)
ffffffff00021b94         56021b94       c4     1                 chan_handle_destroy.cfi
ffffffff00021b94         56021b94        0     1                 $x.267
ffffffff00021c58         56021c58       d0     4         lto.tmp:(.text.chan_shutdown)
ffffffff00021c58         56021c58       d0     1                 chan_shutdown
ffffffff00021c58         56021c58        0     1                 $x.268
ffffffff00021d28         56021d28      194     4         lto.tmp:(.text.chan_del_ref)
ffffffff00021d28         56021d28      194     1                 chan_del_ref
ffffffff00021d28         56021d28        0     1                 $x.269
ffffffff00021ebc         56021ebc       b4     4         lto.tmp:(.text.chan_shutdown_locked)
ffffffff00021ebc         56021ebc       b4     1                 chan_shutdown_locked
ffffffff00021ebc         56021ebc        0     1                 $x.270
ffffffff00021f70         56021f70      12c     4         lto.tmp:(.text.chan_add_ref)
ffffffff00021f70         56021f70      12c     1                 chan_add_ref
ffffffff00021f70         56021f70        0     1                 $x.271
ffffffff0002209c         5602209c      1a8     4         lto.tmp:(.text.ipc_port_create)
ffffffff0002209c         5602209c      1a8     1                 ipc_port_create
ffffffff0002209c         5602209c        0     1                 $x.272
ffffffff00022244         56022244       ac     4         lto.tmp:(.text.port_poll.cfi)
ffffffff00022244         56022244       ac     1                 port_poll.cfi
ffffffff00022244         56022244        0     1                 $x.273
ffffffff000222f0         560222f0      4a0     4         lto.tmp:(.text.port_handle_destroy.cfi)
ffffffff000222f0         560222f0      4a0     1                 port_handle_destroy.cfi
ffffffff000222f0         560222f0        0     1                 $x.274
ffffffff00022790         56022790      298     4         lto.tmp:(.text.ipc_port_publish)
ffffffff00022790         56022790      298     1                 ipc_port_publish
ffffffff00022790         56022790        0     1                 $x.275
ffffffff00022a28         56022a28      15c     4         lto.tmp:(.text.port_attach_client)
ffffffff00022a28         56022a28      15c     1                 port_attach_client
ffffffff00022a28         56022a28        0     1                 $x.276
ffffffff00022b84         56022b84      184     4         lto.tmp:(.text.chan_alloc)
ffffffff00022b84         56022b84      184     1                 chan_alloc
ffffffff00022b84         56022b84        0     1                 $x.277
ffffffff00022d08         56022d08      28c     4         lto.tmp:(.text.sys_port_create)
ffffffff00022d08         56022d08      28c     1                 sys_port_create
ffffffff00022d08         56022d08        0     1                 $x.278
ffffffff00022f94         56022f94      43c     4         lto.tmp:(.text.ipc_port_connect_async)
ffffffff00022f94         56022f94      43c     1                 ipc_port_connect_async
ffffffff00022f94         56022f94        0     1                 $x.279
ffffffff000233d0         560233d0      2d4     4         lto.tmp:(.text.sys_connect)
ffffffff000233d0         560233d0      2d4     1                 sys_connect
ffffffff000233d0         560233d0        0     1                 $x.280
ffffffff000236a4         560236a4      250     4         lto.tmp:(.text.ipc_port_accept)
ffffffff000236a4         560236a4      250     1                 ipc_port_accept
ffffffff000236a4         560236a4        0     1                 $x.281
ffffffff000238f4         560238f4      254     4         lto.tmp:(.text.sys_accept)
ffffffff000238f4         560238f4      254     1                 sys_accept
ffffffff000238f4         560238f4        0     1                 $x.282
ffffffff00023b48         56023b48      124     4         lto.tmp:(.text.ipc_msg_queue_create)
ffffffff00023b48         56023b48      124     1                 ipc_msg_queue_create
ffffffff00023b48         56023b48        0     1                 $x.283
ffffffff00023c6c         56023c6c      110     4         lto.tmp:(.text.ipc_msg_queue_destroy)
ffffffff00023c6c         56023c6c      110     1                 ipc_msg_queue_destroy
ffffffff00023c6c         56023c6c        0     1                 $x.284
ffffffff00023d7c         56023d7c      1bc     4         lto.tmp:(.text.sys_send_msg)
ffffffff00023d7c         56023d7c      1bc     1                 sys_send_msg
ffffffff00023d7c         56023d7c        0     1                 $x.285
ffffffff00023f38         56023f38      454     4         lto.tmp:(.text.msg_write_locked)
ffffffff00023f38         56023f38      454     1                 msg_write_locked
ffffffff00023f38         56023f38        0     1                 $x.286
ffffffff0002438c         5602438c       a8     4         lto.tmp:(.text.ipc_send_msg)
ffffffff0002438c         5602438c       a8     1                 ipc_send_msg
ffffffff0002438c         5602438c        0     1                 $x.287
ffffffff00024434         56024434      244     4         lto.tmp:(.text.sys_get_msg)
ffffffff00024434         56024434      244     1                 sys_get_msg
ffffffff00024434         56024434        0     1                 $x.288
ffffffff00024678         56024678       f8     4         lto.tmp:(.text.ipc_get_msg)
ffffffff00024678         56024678       f8     1                 ipc_get_msg
ffffffff00024678         56024678        0     1                 $x.289
ffffffff00024770         56024770      134     4         lto.tmp:(.text.sys_put_msg)
ffffffff00024770         56024770      134     1                 sys_put_msg
ffffffff00024770         56024770        0     1                 $x.290
ffffffff000248a4         560248a4      2b4     4         lto.tmp:(.text.ipc_put_msg)
ffffffff000248a4         560248a4      2b4     1                 ipc_put_msg
ffffffff000248a4         560248a4        0     1                 $x.291
ffffffff00024b58         56024b58      66c     4         lto.tmp:(.text.sys_read_msg)
ffffffff00024b58         56024b58      66c     1                 sys_read_msg
ffffffff00024b58         56024b58        0     1                 $x.292
ffffffff000251c4         560251c4      224     4         lto.tmp:(.text.ipc_read_msg)
ffffffff000251c4         560251c4      224     1                 ipc_read_msg
ffffffff000251c4         560251c4        0     1                 $x.293
ffffffff000253e8         560253e8      210     4         lto.tmp:(.text.memref_create_from_vmm_obj)
ffffffff000253e8         560253e8      210     1                 memref_create_from_vmm_obj
ffffffff000253e8         560253e8        0     1                 $x.294
ffffffff000255f8         560255f8      110     4         lto.tmp:(.text.memref_handle_destroy.cfi)
ffffffff000255f8         560255f8      110     1                 memref_handle_destroy.cfi
ffffffff000255f8         560255f8        0     1                 $x.295
ffffffff00025708         56025708      178     4         lto.tmp:(.text.memref_mmap.cfi)
ffffffff00025708         56025708      178     1                 memref_mmap.cfi
ffffffff00025708         56025708        0     1                 $x.296
ffffffff00025880         56025880       58     4         lto.tmp:(.text.install_sys_fd_handler)
ffffffff00025880         56025880       58     1                 install_sys_fd_handler
ffffffff00025880         56025880        0     1                 $x.297
ffffffff000258d8         560258d8      2dc     4         lto.tmp:(.text.sys_std_writev.cfi)
ffffffff000258d8         560258d8      2dc     1                 sys_std_writev.cfi
ffffffff000258d8         560258d8        0     1                 $x.298
ffffffff00025bb4         56025bb4       60     4         lto.tmp:(.text.sys_writev.cfi)
ffffffff00025bb4         56025bb4       60     1                 sys_writev.cfi
ffffffff00025bb4         56025bb4        0     1                 $x.299
ffffffff00025c14         56025c14       cc     4         lto.tmp:(.text.sys_brk)
ffffffff00025c14         56025c14       cc     1                 sys_brk
ffffffff00025c14         56025c14        0     1                 $x.300
ffffffff00025ce0         56025ce0       14     4         lto.tmp:(.text.sys_exit_etc)
ffffffff00025ce0         56025ce0       14     1                 sys_exit_etc
ffffffff00025ce0         56025ce0        0     1                 $x.301
ffffffff00025cf4         56025cf4       60     4         lto.tmp:(.text.sys_readv.cfi)
ffffffff00025cf4         56025cf4       60     1                 sys_readv.cfi
ffffffff00025cf4         56025cf4        0     1                 $x.302
ffffffff00025d54         56025d54       7c     4         lto.tmp:(.text.sys_ioctl)
ffffffff00025d54         56025d54       7c     1                 sys_ioctl
ffffffff00025d54         56025d54        0     1                 $x.303
ffffffff00025dd0         56025dd0       24     4         lto.tmp:(.text.sys_nanosleep)
ffffffff00025dd0         56025dd0       24     1                 sys_nanosleep
ffffffff00025dd0         56025dd0        0     1                 $x.304
ffffffff00025df4         56025df4       a8     4         lto.tmp:(.text.sys_gettime)
ffffffff00025df4         56025df4       a8     1                 sys_gettime
ffffffff00025df4         56025df4        0     1                 $x.305
ffffffff00025e9c         56025e9c      308     4         lto.tmp:(.text.sys_mmap)
ffffffff00025e9c         56025e9c      308     1                 sys_mmap
ffffffff00025e9c         56025e9c        0     1                 $x.306
ffffffff000261a4         560261a4       44     4         lto.tmp:(.text.sys_munmap)
ffffffff000261a4         560261a4       44     1                 sys_munmap
ffffffff000261a4         560261a4        0     1                 $x.307
ffffffff000261e8         560261e8      264     4         lto.tmp:(.text.sys_prepare_dma)
ffffffff000261e8         560261e8      264     1                 sys_prepare_dma
ffffffff000261e8         560261e8        0     1                 $x.308
ffffffff0002644c         5602644c      2c0     4         lto.tmp:(.text.sys_finish_dma)
ffffffff0002644c         5602644c      2c0     1                 sys_finish_dma
ffffffff0002644c         5602644c        0     1                 $x.309
ffffffff0002670c         5602670c       10     4         lto.tmp:(.text.sys_set_user_tls)
ffffffff0002670c         5602670c       10     1                 sys_set_user_tls
ffffffff0002670c         5602670c        0     1                 $x.310
ffffffff0002671c         5602671c      304     4         lto.tmp:(.text.sys_memref_create)
ffffffff0002671c         5602671c      304     1                 sys_memref_create
ffffffff0002671c         5602671c        0     1                 $x.311
ffffffff00026a20         56026a20       10     4         lto.tmp:(.text.sys_dump_memory_info)
ffffffff00026a20         56026a20       10     1                 sys_dump_memory_info
ffffffff00026a20         56026a20        0     1                 $x.312
ffffffff00026a30         56026a30      108     4         lto.tmp:(.text.uctx_init.cfi)
ffffffff00026a30         56026a30      108     1                 uctx_init.cfi
ffffffff00026a30         56026a30        0     1                 $x.313
ffffffff00026b38         56026b38       fc     4         lto.tmp:(.text._uctx_startup.cfi)
ffffffff00026b38         56026b38       fc     1                 _uctx_startup.cfi
ffffffff00026b38         56026b38        0     1                 $x.314
ffffffff00026c34         56026c34      14c     4         lto.tmp:(.text._uctx_shutdown.cfi)
ffffffff00026c34         56026c34      14c     1                 _uctx_shutdown.cfi
ffffffff00026c34         56026c34        0     1                 $x.315
ffffffff00026d80         56026d80      16c     4         lto.tmp:(.text.remove_handle)
ffffffff00026d80         56026d80      16c     1                 remove_handle
ffffffff00026d80         56026d80        0     1                 $x.316
ffffffff00026eec         56026eec      348     4         lto.tmp:(.text.uctx_handle_install)
ffffffff00026eec         56026eec      348     1                 uctx_handle_install
ffffffff00026eec         56026eec        0     1                 $x.317
ffffffff00027234         56027234      174     4         lto.tmp:(.text.uctx_handle_get)
ffffffff00027234         56027234      174     1                 uctx_handle_get
ffffffff00027234         56027234        0     1                 $x.318
ffffffff000273a8         560273a8      188     4         lto.tmp:(.text.uctx_handle_remove)
ffffffff000273a8         560273a8      188     1                 uctx_handle_remove
ffffffff000273a8         560273a8        0     1                 $x.319
ffffffff00027530         56027530      320     4         lto.tmp:(.text.sys_wait.cfi)
ffffffff00027530         56027530      320     1                 sys_wait.cfi
ffffffff00027530         56027530        0     1                 $x.320
ffffffff00027850         56027850      278     4         lto.tmp:(.text.sys_wait_any)
ffffffff00027850         56027850      278     1                 sys_wait_any
ffffffff00027850         56027850        0     1                 $x.321
ffffffff00027ac8         56027ac8      400     4         lto.tmp:(.text.rebuild_hset_all)
ffffffff00027ac8         56027ac8      400     1                 rebuild_hset_all
ffffffff00027ac8         56027ac8        0     1                 $x.322
ffffffff00027ec8         56027ec8      148     4         lto.tmp:(.text.sys_dup)
ffffffff00027ec8         56027ec8      148     1                 sys_dup
ffffffff00027ec8         56027ec8        0     1                 $x.323
ffffffff00028010         56028010      148     4         lto.tmp:(.text.sys_close)
ffffffff00028010         56028010      148     1                 sys_close
ffffffff00028010         56028010        0     1                 $x.324
ffffffff00028158         56028158      148     4         lto.tmp:(.text.sys_set_cookie)
ffffffff00028158         56028158      148     1                 sys_set_cookie
ffffffff00028158         56028158        0     1                 $x.325
ffffffff000282a0         560282a0      1cc     4         lto.tmp:(.text.sys_handle_set_create)
ffffffff000282a0         560282a0      1cc     1                 sys_handle_set_create
ffffffff000282a0         560282a0        0     1                 $x.326
ffffffff0002846c         5602846c      50c     4         lto.tmp:(.text.sys_handle_set_ctrl)
ffffffff0002846c         5602846c      50c     1                 sys_handle_set_ctrl
ffffffff0002846c         5602846c        0     1                 $x.327
ffffffff00028978         56028978       f0     4         lto.tmp:(.text.uctx_handle_readv.cfi)
ffffffff00028978         56028978       f0     1                 uctx_handle_readv.cfi
ffffffff00028978         56028978        0     1                 $x.328
ffffffff00028a68         56028a68       f0     4         lto.tmp:(.text.uctx_handle_writev.cfi)
ffffffff00028a68         56028a68       f0     1                 uctx_handle_writev.cfi
ffffffff00028a68         56028a68        0     1                 $x.329
ffffffff00028b58         56028b58      100     4         lto.tmp:(.text.start_apps.cfi)
ffffffff00028b58         56028b58      100     1                 start_apps.cfi
ffffffff00028b58         56028b58        0     1                 $x.330
ffffffff00028c58         56028c58      1cc     4         lto.tmp:(.text.rctee_init.cfi)
ffffffff00028c58         56028c58      1cc     1                 rctee_init.cfi
ffffffff00028c58         56028c58        0     1                 $x.331
ffffffff00028e24         56028e24     1a8c     4         lto.tmp:(.text.app_mgr.cfi)
ffffffff00028e24         56028e24     1a8c     1                 app_mgr.cfi
ffffffff00028e24         56028e24        0     1                 $x.332
ffffffff0002a8b0         5602a8b0      3e8     4         lto.tmp:(.text.rctee_app_create)
ffffffff0002a8b0         5602a8b0      3e8     1                 rctee_app_create
ffffffff0002a8b0         5602a8b0        0     1                 $x.333
ffffffff0002ac98         5602ac98     10e4     4         lto.tmp:(.text.load_app_config_options)
ffffffff0002ac98         5602ac98     10e4     1                 load_app_config_options
ffffffff0002ac98         5602ac98        0     1                 $x.334
ffffffff0002bd7c         5602bd7c       50     4         lto.tmp:(.text.destroy_app_phys_mem.cfi)
ffffffff0002bd7c         5602bd7c       50     1                 destroy_app_phys_mem.cfi
ffffffff0002bd7c         5602bd7c        0     1                 $x.336
ffffffff0002bdcc         5602bdcc       9c     4         lto.tmp:(.text.rctee_app_find_by_uuid_locked)
ffffffff0002bdcc         5602bdcc       9c     1                 rctee_app_find_by_uuid_locked
ffffffff0002bdcc         5602bdcc        0     1                 $x.337
ffffffff0002be68         5602be68       6c     4         lto.tmp:(.text.rctee_thread_startup.cfi)
ffffffff0002be68         5602be68       6c     1                 rctee_thread_startup.cfi
ffffffff0002be68         5602be68        0     1                 $x.338
ffffffff0002bed4         5602bed4      214     4         lto.tmp:(.text.rctee_thread_write_elf_tables)
ffffffff0002bed4         5602bed4      214     1                 rctee_thread_write_elf_tables
ffffffff0002bed4         5602bed4        0     1                 $x.339
ffffffff0002c0e8         5602c0e8       b4     4         lto.tmp:(.text.rctee_app_allow_mmio_range)
ffffffff0002c0e8         5602c0e8       b4     1                 rctee_app_allow_mmio_range
ffffffff0002c0e8         5602c0e8        0     1                 $x.340
ffffffff0002c19c         5602c19c      1ac     4         lto.tmp:(.text.rctee_app_allow_dma_range)
ffffffff0002c19c         5602c19c      1ac     1                 rctee_app_allow_dma_range
ffffffff0002c19c         5602c19c        0     1                 $x.341
ffffffff0002c348         5602c348       ec     4         lto.tmp:(.text.rctee_thread_exit)
ffffffff0002c348         5602c348       ec     1                 rctee_thread_exit
ffffffff0002c348         5602c348        0     1                 $x.342
ffffffff0002c434         5602c434      22c     4         lto.tmp:(.text.rctee_app_create_and_start)
ffffffff0002c434         5602c434      22c     1                 rctee_app_create_and_start
ffffffff0002c434         5602c434        0     1                 $x.343
ffffffff0002c660         5602c660       1c     4         lto.tmp:(.text.rctee_app_exit)
ffffffff0002c660         5602c660       1c     1                 rctee_app_exit
ffffffff0002c660         5602c660        0     1                 $x.344
ffffffff0002c67c         5602c67c      260     4         lto.tmp:(.text.rctee_app_exit_etc)
ffffffff0002c67c         5602c67c      260     1                 rctee_app_exit_etc
ffffffff0002c67c         5602c67c        0     1                 $x.345
ffffffff0002c8dc         5602c8dc       10     4         lto.tmp:(.text.mutex_acquire)
ffffffff0002c8dc         5602c8dc       10     1                 mutex_acquire
ffffffff0002c8dc         5602c8dc        0     1                 $x.346
ffffffff0002c8ec         5602c8ec       1c     4         lto.tmp:(.text.rctee_app_crash)
ffffffff0002c8ec         5602c8ec       1c     1                 rctee_app_crash
ffffffff0002c8ec         5602c8ec        0     1                 $x.347
ffffffff0002c908         5602c908       d4     4         lto.tmp:(.text.vqueue_destroy)
ffffffff0002c908         5602c908       d4     1                 vqueue_destroy
ffffffff0002c908         5602c908        0     1                 $x.348
ffffffff0002c9dc         5602c9dc       b0     4         lto.tmp:(.text.vqueue_signal_avail)
ffffffff0002c9dc         5602c9dc       b0     1                 vqueue_signal_avail
ffffffff0002c9dc         5602c9dc        0     1                 $x.349
ffffffff0002ca8c         5602ca8c      2fc     4         lto.tmp:(.text.vqueue_get_avail_buf)
ffffffff0002ca8c         5602ca8c      2fc     1                 vqueue_get_avail_buf
ffffffff0002ca8c         5602ca8c        0     1                 $x.350
ffffffff0002cd88         5602cd88      1a8     4         lto.tmp:(.text.vqueue_map_iovs)
ffffffff0002cd88         5602cd88      1a8     1                 vqueue_map_iovs
ffffffff0002cd88         5602cd88        0     1                 $x.351
ffffffff0002cf30         5602cf30      17c     4         lto.tmp:(.text.vqueue_unmap_iovs)
ffffffff0002cf30         5602cf30      17c     1                 vqueue_unmap_iovs
ffffffff0002cf30         5602cf30        0     1                 $x.352
ffffffff0002d0ac         5602d0ac      15c     4         lto.tmp:(.text.vqueue_add_buf)
ffffffff0002d0ac         5602d0ac      15c     1                 vqueue_add_buf
ffffffff0002d0ac         5602d0ac        0     1                 $x.353
ffffffff0002d208         5602d208       d4     4         lto.tmp:(.text.rctee_sm_init.cfi)
ffffffff0002d208         5602d208       d4     1                 rctee_sm_init.cfi
ffffffff0002d208         5602d208        0     1                 $x.354
ffffffff0002d2dc         5602d2dc       78     4         lto.tmp:(.text.rctee_sm_fastcall.cfi)
ffffffff0002d2dc         5602d2dc       78     1                 rctee_sm_fastcall.cfi
ffffffff0002d2dc         5602d2dc        0     1                 $x.355
ffffffff0002d354         5602d354       d8     4         lto.tmp:(.text.rctee_sm_nopcall.cfi)
ffffffff0002d354         5602d354       d8     1                 rctee_sm_nopcall.cfi
ffffffff0002d354         5602d354        0     1                 $x.356
ffffffff0002d42c         5602d42c      d08     4         lto.tmp:(.text.rctee_sm_stdcall.cfi)
ffffffff0002d42c         5602d42c      d08     1                 rctee_sm_stdcall.cfi
ffffffff0002d42c         5602d42c        0     1                 $x.357
ffffffff0002e134         5602e134      158     4         lto.tmp:(.text.rcipc_ext_mem_destroy.cfi)
ffffffff0002e134         5602e134      158     1                 rcipc_ext_mem_destroy.cfi
ffffffff0002e134         5602e134        0     1                 $x.359
ffffffff0002e28c         5602e28c       ec     4         lto.tmp:(.text._send_buf.cfi)
ffffffff0002e28c         5602e28c       ec     1                 _send_buf.cfi
ffffffff0002e28c         5602e28c        0     1                 $x.360
ffffffff0002e378         5602e378      238     4         lto.tmp:(.text.rcipc_send_data)
ffffffff0002e378         5602e378      238     1                 rcipc_send_data
ffffffff0002e378         5602e378        0     1                 $x.361
ffffffff0002e5b0         5602e5b0       c4     4         lto.tmp:(.text.tx_data_cb.cfi)
ffffffff0002e5b0         5602e5b0       c4     1                 tx_data_cb.cfi
ffffffff0002e5b0         5602e5b0        0     1                 $x.362
ffffffff0002e674         5602e674        8     4         lto.tmp:(.text.rcipc_descr_size.cfi)
ffffffff0002e674         5602e674        8     1                 rcipc_descr_size.cfi
ffffffff0002e674         5602e674        0     1                 $x.363
ffffffff0002e67c         5602e67c       40     4         lto.tmp:(.text.rcipc_get_vdev_descr.cfi)
ffffffff0002e67c         5602e67c       40     1                 rcipc_get_vdev_descr.cfi
ffffffff0002e67c         5602e67c        0     1                 $x.364
ffffffff0002e6bc         5602e6bc      3fc     4         lto.tmp:(.text.rcipc_vdev_probe.cfi)
ffffffff0002e6bc         5602e6bc      3fc     1                 rcipc_vdev_probe.cfi
ffffffff0002e6bc         5602e6bc        0     1                 $x.365
ffffffff0002eab8         5602eab8      294     4         lto.tmp:(.text.rcipc_vdev_reset.cfi)
ffffffff0002eab8         5602eab8      294     1                 rcipc_vdev_reset.cfi
ffffffff0002eab8         5602eab8        0     1                 $x.366
ffffffff0002ed4c         5602ed4c       d0     4         lto.tmp:(.text.rcipc_vdev_kick_vq.cfi)
ffffffff0002ed4c         5602ed4c       d0     1                 rcipc_vdev_kick_vq.cfi
ffffffff0002ed4c         5602ed4c        0     1                 $x.367
ffffffff0002ee1c         5602ee1c      cb8     4         lto.tmp:(.text.conn_req_thread_func.cfi)
ffffffff0002ee1c         5602ee1c      cb8     1                 conn_req_thread_func.cfi
ffffffff0002ee1c         5602ee1c        0     1                 $x.368
ffffffff0002fad4         5602fad4      810     4         lto.tmp:(.text.rcipc_rx_thread_func.cfi)
ffffffff0002fad4         5602fad4      810     1                 rcipc_rx_thread_func.cfi
ffffffff0002fad4         5602fad4        0     1                 $x.369
ffffffff000302e4         560302e4      890     4         lto.tmp:(.text.rcipc_tx_thread_func.cfi)
ffffffff000302e4         560302e4      890     1                 rcipc_tx_thread_func.cfi
ffffffff000302e4         560302e4        0     1                 $x.370
ffffffff00030b74         56030b74       54     4         lto.tmp:(.text._go_online)
ffffffff00030b74         56030b74       54     1                 _go_online
ffffffff00030b74         56030b74        0     1                 $x.371
ffffffff00030bc8         56030bc8      898     4         lto.tmp:(.text.handle_ctrl_msg)
ffffffff00030bc8         56030bc8      898     1                 handle_ctrl_msg
ffffffff00030bc8         56030bc8        0     1                 $x.372
ffffffff00031460         56031460       2c     4         lto.tmp:(.text.rcipc_ext_mem_check_flags.cfi)
ffffffff00031460         56031460       2c     1                 rcipc_ext_mem_check_flags.cfi
ffffffff00031460         56031460        0     1                 $x.373
ffffffff0003148c         5603148c       2c     4         lto.tmp:(.text.rcipc_ext_mem_get_page.cfi)
ffffffff0003148c         5603148c       2c     1                 rcipc_ext_mem_get_page.cfi
ffffffff0003148c         5603148c        0     1                 $x.374
ffffffff000314b8         560314b8       20     4         lto.tmp:(.text.rcipc_tx_vq_notify_cb.cfi)
ffffffff000314b8         560314b8       20     1                 rcipc_tx_vq_notify_cb.cfi
ffffffff000314b8         560314b8        0     1                 $x.375
ffffffff000314d8         560314d8       20     4         lto.tmp:(.text.rcipc_rx_vq_notify_cb.cfi)
ffffffff000314d8         560314d8       20     1                 rcipc_rx_vq_notify_cb.cfi
ffffffff000314d8         560314d8        0     1                 $x.376
ffffffff000314f8         560314f8      118     4         lto.tmp:(.text.dev_acquire)
ffffffff000314f8         560314f8      118     1                 dev_acquire
ffffffff000314f8         560314f8        0     1                 $x.377
ffffffff00031610         56031610      9e4     4         lto.tmp:(.text.ql_rcipc_handle_cmd)
ffffffff00031610         56031610      9e4     1                 ql_rcipc_handle_cmd
ffffffff00031610         56031610        0     1                 $x.378
ffffffff00031ff4         56031ff4       5c     4         lto.tmp:(.text.register_rcipc_init.cfi)
ffffffff00031ff4         56031ff4       5c     1                 register_rcipc_init.cfi
ffffffff00031ff4         56031ff4        0     1                 $x.380
ffffffff00032050         56032050      198     4         lto.tmp:(.text.rcipc_init.cfi)
ffffffff00032050         56032050      198     1                 rcipc_init.cfi
ffffffff00032050         56032050        0     1                 $x.381
ffffffff000321e8         560321e8       d4     4         lto.tmp:(.text.sm_release_boot_args.cfi)
ffffffff000321e8         560321e8       d4     1                 sm_release_boot_args.cfi
ffffffff000321e8         560321e8        0     1                 $x.382
ffffffff000322bc         560322bc      108     4         lto.tmp:(.text.resume_nsthreads)
ffffffff000322bc         560322bc      108     1                 resume_nsthreads
ffffffff000322bc         560322bc        0     1                 $x.383
ffffffff000323c4         560323c4      290     4         lto.tmp:(.text.sm_init.cfi)
ffffffff000323c4         560323c4      290     1                 sm_init.cfi
ffffffff000323c4         560323c4        0     1                 $x.384
ffffffff00032654         56032654      15c     4         lto.tmp:(.text.sm_stdcall_loop.cfi)
ffffffff00032654         56032654      15c     1                 sm_stdcall_loop.cfi
ffffffff00032654         56032654        0     1                 $x.385
ffffffff000327b0         560327b0       ac     4         lto.tmp:(.text.sm_irq_loop.cfi)
ffffffff000327b0         560327b0       ac     1                 sm_irq_loop.cfi
ffffffff000327b0         560327b0        0     1                 $x.386
ffffffff0003285c         5603285c      124     4         lto.tmp:(.text.sm_wait_for_smcall.cfi)
ffffffff0003285c         5603285c      124     1                 sm_wait_for_smcall.cfi
ffffffff0003285c         5603285c        0     1                 $x.387
ffffffff00032980         56032980      240     4         lto.tmp:(.text.sm_check_and_lock_api_version)
ffffffff00032980         56032980      240     1                 sm_check_and_lock_api_version
ffffffff00032980         56032980        0     1                 $x.388
ffffffff00032bc0         56032bc0      25c     4         lto.tmp:(.text.sm_return_and_wait_for_next_stdcall)
ffffffff00032bc0         56032bc0      25c     1                 sm_return_and_wait_for_next_stdcall
ffffffff00032bc0         56032bc0        0     1                 $x.389
ffffffff00032e1c         56032e1c       ac     4         lto.tmp:(.text.smc_sm_api_version.cfi)
ffffffff00032e1c         56032e1c       ac     1                 smc_sm_api_version.cfi
ffffffff00032e1c         56032e1c        0     1                 $x.390
ffffffff00032ec8         56032ec8        8     4         lto.tmp:(.text.smc_get_smp_max_cpus.cfi)
ffffffff00032ec8         56032ec8        8     1                 smc_get_smp_max_cpus.cfi
ffffffff00032ec8         56032ec8        0     1                 $x.391
ffffffff00032ed0         56032ed0      180     4         lto.tmp:(.text.platform_halt)
ffffffff00032ed0         56032ed0      180     1                 platform_halt
ffffffff00032ed0         56032ed0        0     1                 $x.392
ffffffff00033050         56033050       dc     4         lto.tmp:(.text.smc_undefined.cfi)
ffffffff00033050         56033050       dc     1                 smc_undefined.cfi
ffffffff00033050         56033050        0     1                 $x.393
ffffffff0003312c         5603312c       d8     4         lto.tmp:(.text.sm_register_entity)
ffffffff0003312c         5603312c       d8     1                 sm_register_entity
ffffffff0003312c         5603312c        0     1                 $x.394
ffffffff00033204         56033204       50     4         lto.tmp:(.text.smc_stdcall_secure_monitor.cfi)
ffffffff00033204         56033204       50     1                 smc_stdcall_secure_monitor.cfi
ffffffff00033204         56033204        0     1                 $x.395
ffffffff00033254         56033254       dc     4         lto.tmp:(.text.smc_restart_stdcall.cfi)
ffffffff00033254         56033254       dc     1                 smc_restart_stdcall.cfi
ffffffff00033254         56033254        0     1                 $x.396
ffffffff00033330         56033330        8     4         lto.tmp:(.text.smc_nop_stdcall.cfi)
ffffffff00033330         56033330        8     1                 smc_nop_stdcall.cfi
ffffffff00033330         56033330        0     1                 $x.397
ffffffff00033338         56033338       14     4         lto.tmp:(.text.smc_nop_secure_monitor.cfi)
ffffffff00033338         56033338       14     1                 smc_nop_secure_monitor.cfi
ffffffff00033338         56033338        0     1                 $x.398
ffffffff0003334c         5603334c       50     4         lto.tmp:(.text.smc_fastcall_secure_monitor.cfi)
ffffffff0003334c         5603334c       50     1                 smc_fastcall_secure_monitor.cfi
ffffffff0003334c         5603334c        0     1                 $x.399
ffffffff0003339c         5603339c       20     4         lto.tmp:(.text.smc_fiq_enter.cfi)
ffffffff0003339c         5603339c       20     1                 smc_fiq_enter.cfi
ffffffff0003339c         5603339c        0     1                 $x.400
ffffffff000333bc         560333bc      130     4         lto.tmp:(.text.smc_cpu_suspend.cfi)
ffffffff000333bc         560333bc      130     1                 smc_cpu_suspend.cfi
ffffffff000333bc         560333bc        0     1                 $x.401
ffffffff000334ec         560334ec      130     4         lto.tmp:(.text.smc_cpu_resume.cfi)
ffffffff000334ec         560334ec      130     1                 smc_cpu_resume.cfi
ffffffff000334ec         560334ec        0     1                 $x.402
ffffffff0003361c         5603361c       50     4         lto.tmp:(.text.smc_get_version_str.cfi)
ffffffff0003361c         5603361c       50     1                 smc_get_version_str.cfi
ffffffff0003361c         5603361c        0     1                 $x.403
ffffffff0003366c         5603366c       6c     4         lto.tmp:(.text.shared_mem_init.cfi)
ffffffff0003366c         5603366c       6c     1                 shared_mem_init.cfi
ffffffff0003366c         5603366c        0     1                 $x.404
ffffffff000336d8         560336d8      adc     4         lto.tmp:(.text.ext_mem_get_vmm_obj)
ffffffff000336d8         560336d8      adc     1                 ext_mem_get_vmm_obj
ffffffff000336d8         560336d8        0     1                 $x.405
ffffffff000341b4         560341b4        8     4         lto.tmp:(.text.sm_mem_obj_compat_destroy.cfi)
ffffffff000341b4         560341b4        8     1                 sm_mem_obj_compat_destroy.cfi
ffffffff000341b4         560341b4        0     1                 $x.407
ffffffff000341bc         560341bc      200     4         lto.tmp:(.text.sm_mem_obj_destroy.cfi)
ffffffff000341bc         560341bc      200     1                 sm_mem_obj_destroy.cfi
ffffffff000341bc         560341bc        0     1                 $x.408
ffffffff000343bc         560343bc      284     4         lto.tmp:(.text.smc_trusty_sched_share_register.cfi)
ffffffff000343bc         560343bc      284     1                 smc_trusty_sched_share_register.cfi
ffffffff000343bc         560343bc        0     1                 $x.409
ffffffff00034640         56034640      168     4         lto.tmp:(.text.smc_trusty_sched_share_unregister.cfi)
ffffffff00034640         56034640      168     1                 smc_trusty_sched_share_unregister.cfi
ffffffff00034640         56034640        0     1                 $x.410
ffffffff000347a8         560347a8       e8     4         lto.tmp:(.text.platform_cpu_priority_set)
ffffffff000347a8         560347a8       e8     1                 platform_cpu_priority_set
ffffffff000347a8         560347a8        0     1                 $x.411
ffffffff00034890         56034890       48     4         lto.tmp:(.text.sys_undefined)
ffffffff00034890         56034890        0     1                 $x.412
ffffffff00034890         56034890       48     1                 sys_undefined
ffffffff000348d8         560348d8       e0     4         lto.tmp:(.text.unittest_printf)
ffffffff000348d8         560348d8       e0     1                 unittest_printf
ffffffff000348d8         560348d8        0     1                 $x.413
ffffffff000349b8         560349b8      17c     4         lto.tmp:(.text.send_msg_wait)
ffffffff000349b8         560349b8      17c     1                 send_msg_wait
ffffffff000349b8         560349b8        0     1                 $x.414
ffffffff00034b34         56034b34      1c0     4         lto.tmp:(.text.unittest_loop.cfi)
ffffffff00034b34         56034b34      1c0     1                 unittest_loop.cfi
ffffffff00034b34         56034b34        0     1                 $x.415
ffffffff00034cf4         56034cf4       fc     4         lto.tmp:(.text.apploader_service_init.cfi)
ffffffff00034cf4         56034cf4       fc     1                 apploader_service_init.cfi
ffffffff00034cf4         56034cf4        0     1                 $x.416
ffffffff00034df0         56034df0       74     4         lto.tmp:(.text.apploader_service_handle_connect.cfi)
ffffffff00034df0         56034df0       74     1                 apploader_service_handle_connect.cfi
ffffffff00034df0         56034df0        0     1                 $x.417
ffffffff00034e64         56034e64      9c8     4         lto.tmp:(.text.apploader_service_handle_msg.cfi)
ffffffff00034e64         56034e64      9c8     1                 apploader_service_handle_msg.cfi
ffffffff00034e64         56034e64        0     1                 $x.418
ffffffff0003582c         5603582c       e0     4         lto.tmp:(.text.apploader_service_handle_channel_cleanup.cfi)
ffffffff0003582c         5603582c       e0     1                 apploader_service_handle_channel_cleanup.cfi
ffffffff0003582c         5603582c        0     1                 $x.419
ffffffff0003590c         5603590c       60     4         lto.tmp:(.text.apploader_service_translate_error)
ffffffff0003590c         5603590c       60     1                 apploader_service_translate_error
ffffffff0003590c         5603590c        0     1                 $x.420
ffffffff0003596c         5603596c       e4     4         lto.tmp:(.text.apploader_service_send_response)
ffffffff0003596c         5603596c       e4     1                 apploader_service_send_response
ffffffff0003596c         5603596c        0     1                 $x.421
ffffffff00035a50         56035a50       fc     4         lto.tmp:(.text.generic_ta_service_init.cfi)
ffffffff00035a50         56035a50       fc     1                 generic_ta_service_init.cfi
ffffffff00035a50         56035a50        0     1                 $x.422
ffffffff00035b4c         56035b4c       e8     4         lto.tmp:(.text.generic_ta_service_handle_connect.cfi)
ffffffff00035b4c         56035b4c       e8     1                 generic_ta_service_handle_connect.cfi
ffffffff00035b4c         56035b4c        0     1                 $x.423
ffffffff00035c34         56035c34      968     4         lto.tmp:(.text.generic_ta_service_handle_msg.cfi)
ffffffff00035c34         56035c34      968     1                 generic_ta_service_handle_msg.cfi
ffffffff00035c34         56035c34        0     1                 $x.424
ffffffff0003659c         5603659c        4     4         lto.tmp:(.text.generic_ta_service_handle_channel_cleanup.cfi)
ffffffff0003659c         5603659c        4     1                 generic_ta_service_handle_channel_cleanup.cfi
ffffffff0003659c         5603659c        0     1                 $x.426
ffffffff000365a0         560365a0       fc     4         lto.tmp:(.text.hwrng_ktipc_server_init.cfi)
ffffffff000365a0         560365a0       fc     1                 hwrng_ktipc_server_init.cfi
ffffffff000365a0         560365a0        0     1                 $x.427
ffffffff0003669c         5603669c       78     4         lto.tmp:(.text.hwrng_handle_connect.cfi)
ffffffff0003669c         5603669c       78     1                 hwrng_handle_connect.cfi
ffffffff0003669c         5603669c        0     1                 $x.428
ffffffff00036714         56036714       f8     4         lto.tmp:(.text.hwrng_handle_msg.cfi)
ffffffff00036714         56036714       f8     1                 hwrng_handle_msg.cfi
ffffffff00036714         56036714        0     1                 $x.429
ffffffff0003680c         5603680c       1c     4         lto.tmp:(.text.hwrng_handle_channel_cleanup.cfi)
ffffffff0003680c         5603680c       1c     1                 hwrng_handle_channel_cleanup.cfi
ffffffff0003680c         5603680c        0     1                 $x.430
ffffffff00036828         56036828       38     4         lto.tmp:(.text.hwrng_handle_send_unblocked.cfi)
ffffffff00036828         56036828       38     1                 hwrng_handle_send_unblocked.cfi
ffffffff00036828         56036828        0     1                 $x.431
ffffffff00036860         56036860      23c     4         lto.tmp:(.text.hwrng_handle_req_queue)
ffffffff00036860         56036860      23c     1                 hwrng_handle_req_queue
ffffffff00036860         56036860        0     1                 $x.432
ffffffff00036a9c         56036a9c       fc     4         lto.tmp:(.text.smc_service_init.cfi)
ffffffff00036a9c         56036a9c       fc     1                 smc_service_init.cfi
ffffffff00036a9c         56036a9c        0     1                 $x.433
ffffffff00036b98         56036b98       88     4         lto.tmp:(.text.smc_service_handle_connect.cfi)
ffffffff00036b98         56036b98       88     1                 smc_service_handle_connect.cfi
ffffffff00036b98         56036b98        0     1                 $x.434
ffffffff00036c20         56036c20      1e0     4         lto.tmp:(.text.smc_service_handle_msg.cfi)
ffffffff00036c20         56036c20      1e0     1                 smc_service_handle_msg.cfi
ffffffff00036c20         56036c20        0     1                 $x.435
ffffffff00036e00         56036e00        4     4         lto.tmp:(.text.smc_service_handle_channel_cleanup.cfi)
ffffffff00036e00         56036e00        4     1                 smc_service_handle_channel_cleanup.cfi
ffffffff00036e00         56036e00        0     1                 $x.436
ffffffff00036e04         56036e04        4     4         lto.tmp:(.text..L.cfi.jumptable)
ffffffff00036e04         56036e04        4     1                 default_access_policy
ffffffff00036e04         56036e04        0     1                 $x.437
ffffffff00036e04         56036e04        1     1                 __typeid__ZTSFijE_global_addr
ffffffff00036e08         56036e08        8     4         lto.tmp:(.text..L.cfi.jumptable.1524)
ffffffff00036e08         56036e08        0     1                 $x.438
ffffffff00036e08         56036e08        8     1                 sys_caam_ioctl
ffffffff00036e08         56036e08        1     1                 __typeid__ZTSFijjmE_global_addr
ffffffff00036e0c         56036e0c        8     1                 sys_csu_ioctl
ffffffff00036e10         56036e10        c     4         lto.tmp:(.text..L.cfi.jumptable.1525)
ffffffff00036e10         56036e10        c     1                 arm_ipi_generic_handler
ffffffff00036e10         56036e10        0     1                 $x.439
ffffffff00036e10         56036e10        1     1                 __typeid__ZTSF14handler_returnPvE_global_addr
ffffffff00036e14         56036e14        c     1                 arm_ipi_reschedule_handler
ffffffff00036e18         56036e18        c     1                 platform_tick
ffffffff00036e1c         56036e1c        c     4         lto.tmp:(.text..L.cfi.jumptable.1526)
ffffffff00036e1c         56036e1c        c     1                 thread_timer_callback
ffffffff00036e1c         56036e1c        0     1                 $x.440
ffffffff00036e1c         56036e1c        1     1                 __typeid__ZTSF14handler_returnP5timeryPvE_global_addr
ffffffff00036e20         56036e20        c     1                 wait_queue_timeout_handler
ffffffff00036e24         56036e24        c     1                 thread_sleep_handler
ffffffff00036e28         56036e28        4     4         lto.tmp:(.text..L.cfi.jumptable.1527)
ffffffff00036e28         56036e28        4     1                 timer_tick
ffffffff00036e28         56036e28        0     1                 $x.441
ffffffff00036e28         56036e28        1     1                 __typeid__ZTSF14handler_returnPvyE_global_addr
ffffffff00036e2c         56036e2c        4     4         lto.tmp:(.text..L.cfi.jumptable.1529)
ffffffff00036e2c         56036e2c        0     1                 $x.443
ffffffff00036e2c         56036e2c        4     1                 __debug_stdio_write
ffffffff00036e2c         56036e2c        1     1                 __typeid__ZTSFlP9io_handlePKcmE_global_addr
ffffffff00036e30         56036e30        4     4         lto.tmp:(.text..L.cfi.jumptable.1530)
ffffffff00036e30         56036e30        0     1                 $x.444
ffffffff00036e30         56036e30        4     1                 __debug_stdio_read
ffffffff00036e30         56036e30        1     1                 __typeid__ZTSFlP9io_handlePcmE_global_addr
ffffffff00036e34         56036e34        c     4         lto.tmp:(.text..L.cfi.jumptable.1531)
ffffffff00036e34         56036e34        0     1                 $x.445
ffffffff00036e34         56036e34        c     1                 __debug_stdio_write_commit
ffffffff00036e34         56036e34        1     1                 __typeid__ZTSFvP9io_handleE_global_addr
ffffffff00036e38         56036e38        c     1                 __debug_stdio_lock
ffffffff00036e3c         56036e3c        c     1                 __debug_stdio_unlock
ffffffff00036e40         56036e40        8     4         lto.tmp:(.text..L.cfi.jumptable.1532)
ffffffff00036e40         56036e40        8     1                 port_event_handler
ffffffff00036e40         56036e40        0     1                 $x.446
ffffffff00036e40         56036e40        1     1                 __typeid__ZTSFvP12ktipc_serverP18ksrv_event_handlerjE_global_addr
ffffffff00036e44         56036e44        8     1                 chan_event_handler
ffffffff00036e48         56036e48        4     4         lto.tmp:(.text..L.cfi.jumptable.1533)
ffffffff00036e48         56036e48        0     1                 $x.447
ffffffff00036e48         56036e48        4     1                 run_ktipctest
ffffffff00036e48         56036e48        1     1                 __typeid__ZTSFbP8unittestE_global_addr
ffffffff00036e4c         56036e4c       1c     4         lto.tmp:(.text..L.cfi.jumptable.1534)
ffffffff00036e4c         56036e4c       1c     1                 initial_thread_func
ffffffff00036e4c         56036e4c        0     1                 $x.448
ffffffff00036e4c         56036e4c        1     1                 __typeid__ZTSFvvE_global_addr
ffffffff00036e50         56036e50       1c     1                 ktipctest_connecterr
ffffffff00036e54         56036e54       1c     1                 ktipctest_blockedport
ffffffff00036e58         56036e58       1c     1                 ktipctest_echo
ffffffff00036e5c         56036e5c       1c     1                 ktipctest_echo8
ffffffff00036e60         56036e60       1c     1                 ktipctest_close
ffffffff00036e64         56036e64       1c     1                 ktipctest_blockedsend
ffffffff00036e68         56036e68        4     4         lto.tmp:(.text..L.cfi.jumptable.1535)
ffffffff00036e68         56036e68        4     1                 memlog_print_callback
ffffffff00036e68         56036e68        0     1                 $x.449
ffffffff00036e68         56036e68        1     1                 __typeid__ZTSFvP16__print_callbackPKcmE_global_addr
ffffffff00036e6c         56036e6c        4     4         lto.tmp:(.text..L.cfi.jumptable.1536)
ffffffff00036e6c         56036e6c        4     1                 memlog_commit_callback
ffffffff00036e6c         56036e6c        0     1                 $x.450
ffffffff00036e6c         56036e6c        1     1                 __typeid__ZTSFvP16__print_callbackE_global_addr
ffffffff00036e70         56036e70        c     4         lto.tmp:(.text..L.cfi.jumptable.1538)
ffffffff00036e70         56036e70        c     1                 handle_event_waiter_notify
ffffffff00036e70         56036e70        0     1                 $x.452
ffffffff00036e70         56036e70        1     1                 __typeid__ZTSFvP13handle_waiterE_global_addr
ffffffff00036e74         56036e74        c     1                 hset_waiter_notify
ffffffff00036e78         56036e78        c     1                 handle_event_waiter_notify.991
ffffffff00036e7c         56036e7c        c     4         lto.tmp:(.text..L.cfi.jumptable.1539)
ffffffff00036e7c         56036e7c        0     1                 $x.453
ffffffff00036e7c         56036e7c        c     1                 hset_poll
ffffffff00036e7c         56036e7c        1     1                 __typeid__ZTSFjP6handlejbE_global_addr
ffffffff00036e80         56036e80        c     1                 chan_poll
ffffffff00036e84         56036e84        c     1                 port_poll
ffffffff00036e88         56036e88       10     4         lto.tmp:(.text..L.cfi.jumptable.1540)
ffffffff00036e88         56036e88        0     1                 $x.454
ffffffff00036e88         56036e88       10     1                 hset_destroy
ffffffff00036e88         56036e88        1     1                 __typeid__ZTSFvP6handleE_global_addr
ffffffff00036e8c         56036e8c       10     1                 chan_handle_destroy
ffffffff00036e90         56036e90       10     1                 port_handle_destroy
ffffffff00036e94         56036e94       10     1                 memref_handle_destroy
ffffffff00036e98         56036e98        4     4         lto.tmp:(.text..L.cfi.jumptable.1541)
ffffffff00036e98         56036e98        0     1                 $x.455
ffffffff00036e98         56036e98        4     1                 memref_mmap
ffffffff00036e98         56036e98        1     1                 __typeid__ZTSFiP6handlemmjPmE_global_addr
ffffffff00036e9c         56036e9c        8     4         lto.tmp:(.text..L.cfi.jumptable.1542)
ffffffff00036e9c         56036e9c        0     1                 $x.456
ffffffff00036e9c         56036e9c        8     1                 _uctx_startup
ffffffff00036e9c         56036e9c        1     1                 __typeid__ZTSFiP9rctee_appE_global_addr
ffffffff00036ea0         56036ea0        8     1                 _uctx_shutdown
ffffffff00036ea4         56036ea4       18     4         lto.tmp:(.text..L.cfi.jumptable.1543)
ffffffff00036ea4         56036ea4        0     1                 $x.457
ffffffff00036ea4         56036ea4       18     1                 sys_std_writev
ffffffff00036ea4         56036ea4        1     1                 __typeid__ZTSFljmjE_global_addr
ffffffff00036ea8         56036ea8       18     1                 sys_writev
ffffffff00036eac         56036eac       18     1                 sys_readv
ffffffff00036eb0         56036eb0       18     1                 sys_wait
ffffffff00036eb4         56036eb4       18     1                 uctx_handle_readv
ffffffff00036eb8         56036eb8       18     1                 uctx_handle_writev
ffffffff00036ebc         56036ebc        4     4         lto.tmp:(.text..L.cfi.jumptable.1544)
ffffffff00036ebc         56036ebc        4     1                 destroy_app_phys_mem
ffffffff00036ebc         56036ebc        0     1                 $x.458
ffffffff00036ebc         56036ebc        1     1                 __typeid__ZTSFvP12phys_mem_objE_global_addr
ffffffff00036ec0         56036ec0        8     4         lto.tmp:(.text..L.cfi.jumptable.1545)
ffffffff00036ec0         56036ec0        8     1                 _send_buf
ffffffff00036ec0         56036ec0        0     1                 $x.459
ffffffff00036ec0         56036ec0        1     1                 __typeid__ZTSFiPhmPvE_global_addr
ffffffff00036ec4         56036ec4        8     1                 tx_data_cb
ffffffff00036ec8         56036ec8        4     4         lto.tmp:(.text..L.cfi.jumptable.1546)
ffffffff00036ec8         56036ec8        0     1                 $x.460
ffffffff00036ec8         56036ec8        4     1                 rcipc_descr_size
ffffffff00036ec8         56036ec8        1     1                 __typeid__ZTSFmP4vdevE_global_addr
ffffffff00036ecc         56036ecc        4     4         lto.tmp:(.text..L.cfi.jumptable.1547)
ffffffff00036ecc         56036ecc        0     1                 $x.461
ffffffff00036ecc         56036ecc        4     1                 rcipc_get_vdev_descr
ffffffff00036ecc         56036ecc        1     1                 __typeid__ZTSFlP4vdevPvE_global_addr
ffffffff00036ed0         56036ed0        4     4         lto.tmp:(.text..L.cfi.jumptable.1548)
ffffffff00036ed0         56036ed0        0     1                 $x.462
ffffffff00036ed0         56036ed0        4     1                 rcipc_vdev_probe
ffffffff00036ed0         56036ed0        1     1                 __typeid__ZTSFiP4vdevPvE_global_addr
ffffffff00036ed4         56036ed4        4     4         lto.tmp:(.text..L.cfi.jumptable.1549)
ffffffff00036ed4         56036ed4        0     1                 $x.463
ffffffff00036ed4         56036ed4        4     1                 rcipc_vdev_reset
ffffffff00036ed4         56036ed4        1     1                 __typeid__ZTSFiP4vdevE_global_addr
ffffffff00036ed8         56036ed8        4     4         lto.tmp:(.text..L.cfi.jumptable.1550)
ffffffff00036ed8         56036ed8        0     1                 $x.464
ffffffff00036ed8         56036ed8        4     1                 rcipc_vdev_kick_vq
ffffffff00036ed8         56036ed8        1     1                 __typeid__ZTSFiP4vdevjE_global_addr
ffffffff00036edc         56036edc       14     4         lto.tmp:(.text..L.cfi.jumptable.1551)
ffffffff00036edc         56036edc        0     1                 $x.465
ffffffff00036edc         56036edc       14     1                 phys_mem_obj_check_flags
ffffffff00036edc         56036edc        1     1                 __typeid__ZTSFiP7vmm_objPjE_global_addr
ffffffff00036ee0         56036ee0       14     1                 pmm_vmm_obj_check_flags
ffffffff00036ee4         56036ee4       14     1                 vmm_res_obj_check_flags
ffffffff00036ee8         56036ee8       14     1                 ext_mem_obj_check_flags
ffffffff00036eec         56036eec       14     1                 rcipc_ext_mem_check_flags
ffffffff00036ef0         56036ef0       14     4         lto.tmp:(.text..L.cfi.jumptable.1552)
ffffffff00036ef0         56036ef0        0     1                 $x.466
ffffffff00036ef0         56036ef0       14     1                 phys_mem_obj_get_page
ffffffff00036ef0         56036ef0        1     1                 __typeid__ZTSFiP7vmm_objmPmS1_E_global_addr
ffffffff00036ef4         56036ef4       14     1                 pmm_vmm_obj_get_page
ffffffff00036ef8         56036ef8       14     1                 vmm_res_obj_get_page
ffffffff00036efc         56036efc       14     1                 ext_mem_obj_get_page
ffffffff00036f00         56036f00       14     1                 rcipc_ext_mem_get_page
ffffffff00036f04         56036f04        8     4         lto.tmp:(.text..L.cfi.jumptable.1553)
ffffffff00036f04         56036f04        0     1                 $x.467
ffffffff00036f04         56036f04        8     1                 rcipc_tx_vq_notify_cb
ffffffff00036f04         56036f04        1     1                 __typeid__ZTSFiP6vqueuePvE_global_addr
ffffffff00036f08         56036f08        8     1                 rcipc_rx_vq_notify_cb
ffffffff00036f0c         56036f0c        4     4         lto.tmp:(.text..L.cfi.jumptable.1554)
ffffffff00036f0c         56036f0c        0     1                 $x.468
ffffffff00036f0c         56036f0c        4     1                 rcipc_init
ffffffff00036f0c         56036f0c        1     1                 __typeid__ZTSFiP16rctee_virtio_busE_global_addr
ffffffff00036f10         56036f10       18     4         lto.tmp:(.text..L.cfi.jumptable.1555)
ffffffff00036f10         56036f10        0     1                 $x.469
ffffffff00036f10         56036f10       18     1                 phys_mem_obj_destroy
ffffffff00036f10         56036f10        1     1                 __typeid__ZTSFvP7vmm_objE_global_addr
ffffffff00036f14         56036f14       18     1                 pmm_vmm_obj_destroy
ffffffff00036f18         56036f18       18     1                 vmm_res_obj_destroy
ffffffff00036f1c         56036f1c       18     1                 rcipc_ext_mem_destroy
ffffffff00036f20         56036f20       18     1                 sm_mem_obj_compat_destroy
ffffffff00036f24         56036f24       18     1                 sm_mem_obj_destroy
ffffffff00036f28         56036f28       60     4         lto.tmp:(.text..L.cfi.jumptable.1556)
ffffffff00036f28         56036f28        0     1                 $x.470
ffffffff00036f28         56036f28       60     1                 console_stdcall
ffffffff00036f28         56036f28        1     1                 __typeid__ZTSFlP10smc32_argsE_global_addr
ffffffff00036f2c         56036f2c       60     1                 imx_linux_fastcall
ffffffff00036f30         56036f30       60     1                 snvs_fastcall
ffffffff00036f34         56036f34       60     1                 vpu_fastcall
ffffffff00036f38         56036f38       60     1                 vpu_enc_fastcall
ffffffff00036f3c         56036f3c       60     1                 smc_intc_get_next_irq
ffffffff00036f40         56036f40       60     1                 memlog_stdcall
ffffffff00036f44         56036f44       60     1                 rctee_sm_fastcall
ffffffff00036f48         56036f48       60     1                 rctee_sm_nopcall
ffffffff00036f4c         56036f4c       60     1                 rctee_sm_stdcall
ffffffff00036f50         56036f50       60     1                 smc_sm_api_version
ffffffff00036f54         56036f54       60     1                 smc_get_smp_max_cpus
ffffffff00036f58         56036f58       60     1                 smc_undefined
ffffffff00036f5c         56036f5c       60     1                 smc_stdcall_secure_monitor
ffffffff00036f60         56036f60       60     1                 smc_restart_stdcall
ffffffff00036f64         56036f64       60     1                 smc_nop_stdcall
ffffffff00036f68         56036f68       60     1                 smc_nop_secure_monitor
ffffffff00036f6c         56036f6c       60     1                 smc_fastcall_secure_monitor
ffffffff00036f70         56036f70       60     1                 smc_fiq_enter
ffffffff00036f74         56036f74       60     1                 smc_cpu_suspend
ffffffff00036f78         56036f78       60     1                 smc_cpu_resume
ffffffff00036f7c         56036f7c       60     1                 smc_get_version_str
ffffffff00036f80         56036f80       60     1                 smc_trusty_sched_share_register
ffffffff00036f84         56036f84       60     1                 smc_trusty_sched_share_unregister
ffffffff00036f88         56036f88       40     4         lto.tmp:(.text..L.cfi.jumptable.1557)
ffffffff00036f88         56036f88       40     1                 app_thread_entry
ffffffff00036f88         56036f88        0     1                 $x.471
ffffffff00036f88         56036f88        1     1                 __typeid__ZTSFiPvE_global_addr
ffffffff00036f8c         56036f8c       40     1                 reaper_thread_routine
ffffffff00036f90         56036f90       40     1                 bootstrap2
ffffffff00036f94         56036f94       40     1                 secondary_cpu_bootstrap2
ffffffff00036f98         56036f98       40     1                 busy_test_server
ffffffff00036f9c         56036f9c       40     1                 busy_test_busy_func
ffffffff00036fa0         56036fa0       40     1                 ksrv_thread
ffffffff00036fa4         56036fa4       40     1                 app_mgr
ffffffff00036fa8         56036fa8       40     1                 rctee_thread_startup
ffffffff00036fac         56036fac       40     1                 conn_req_thread_func
ffffffff00036fb0         56036fb0       40     1                 rcipc_rx_thread_func
ffffffff00036fb4         56036fb4       40     1                 rcipc_tx_thread_func
ffffffff00036fb8         56036fb8       40     1                 sm_stdcall_loop
ffffffff00036fbc         56036fbc       40     1                 sm_irq_loop
ffffffff00036fc0         56036fc0       40     1                 sm_wait_for_smcall
ffffffff00036fc4         56036fc4       40     1                 unittest_loop
ffffffff00036fc8         56036fc8       94     4         lto.tmp:(.text..L.cfi.jumptable.1558)
ffffffff00036fc8         56036fc8        0     1                 $x.472
ffffffff00036fc8         56036fc8       94     1                 console_smcall_init
ffffffff00036fc8         56036fc8        1     1                 __typeid__ZTSFvjE_global_addr
ffffffff00036fcc         56036fcc       94     1                 platform_after_vm_init
ffffffff00036fd0         56036fd0       94     1                 add_app_ranges
ffffffff00036fd4         56036fd4       94     1                 init_caam_env
ffffffff00036fd8         56036fd8       94     1                 platform_init_caam
ffffffff00036fdc         56036fdc       94     1                 platform_init_csu
ffffffff00036fe0         56036fe0       94     1                 imx_linux_smcall_init
ffffffff00036fe4         56036fe4       94     1                 snvs_smcall_init
ffffffff00036fe8         56036fe8       94     1                 vpu_smcall_init
ffffffff00036fec         56036fec       94     1                 vpu_enc_smcall_init
ffffffff00036ff0         56036ff0       94     1                 arm64_pan_init
ffffffff00036ff4         56036ff4       94     1                 arm_gic_suspend_cpu
ffffffff00036ff8         56036ff8       94     1                 arm_gic_resume_cpu
ffffffff00036ffc         56036ffc       94     1                 arm_gic_init_percpu
ffffffff00037000         56037000       94     1                 arm_generic_timer_suspend_cpu
ffffffff00037004         56037004       94     1                 arm_generic_timer_resume_cpu
ffffffff00037008         56037008       94     1                 arm_generic_timer_init_secondary_cpu
ffffffff0003700c         5603700c       94     1                 vm_init_preheap
ffffffff00037010         56037010       94     1                 vm_init_postheap
ffffffff00037014         56037014       94     1                 busy_test_init
ffffffff00037018         56037018       94     1                 busy_test_cpu_init
ffffffff0003701c         5603701c       94     1                 arm_ffa_init
ffffffff00037020         56037020       94     1                 ktipctest_init
ffffffff00037024         56037024       94     1                 ktipc_test_server_init
ffffffff00037028         56037028       94     1                 memlog_init
ffffffff0003702c         5603702c       94     1                 uctx_init
ffffffff00037030         56037030       94     1                 start_apps
ffffffff00037034         56037034       94     1                 rctee_init
ffffffff00037038         56037038       94     1                 rctee_sm_init
ffffffff0003703c         5603703c       94     1                 register_rcipc_init
ffffffff00037040         56037040       94     1                 sm_release_boot_args
ffffffff00037044         56037044       94     1                 sm_init
ffffffff00037048         56037048       94     1                 shared_mem_init
ffffffff0003704c         5603704c       94     1                 apploader_service_init
ffffffff00037050         56037050       94     1                 generic_ta_service_init
ffffffff00037054         56037054       94     1                 hwrng_ktipc_server_init
ffffffff00037058         56037058       94     1                 smc_service_init
ffffffff0003705c         5603705c       18     4         lto.tmp:(.text..L.cfi.jumptable.1559)
ffffffff0003705c         5603705c        0     1                 $x.473
ffffffff0003705c         5603705c       18     1                 connecterr_handle_connect
ffffffff0003705c         5603705c        1     1                 __typeid__ZTSFiPK10ktipc_portP6handlePK4uuidPPvE_global_addr
ffffffff00037060         56037060       18     1                 test_handle_connect
ffffffff00037064         56037064       18     1                 apploader_service_handle_connect
ffffffff00037068         56037068       18     1                 generic_ta_service_handle_connect
ffffffff0003706c         5603706c       18     1                 hwrng_handle_connect
ffffffff00037070         56037070       18     1                 smc_service_handle_connect
ffffffff00037074         56037074       20     4         lto.tmp:(.text..L.cfi.jumptable.1560)
ffffffff00037074         56037074        0     1                 $x.474
ffffffff00037074         56037074       20     1                 nop_handle_msg
ffffffff00037074         56037074        1     1                 __typeid__ZTSFiPK10ktipc_portP6handlePvE_global_addr
ffffffff00037078         56037078       20     1                 test_handle_msg
ffffffff0003707c         5603707c       20     1                 test_handle_send_unblocked
ffffffff00037080         56037080       20     1                 apploader_service_handle_msg
ffffffff00037084         56037084       20     1                 generic_ta_service_handle_msg
ffffffff00037088         56037088       20     1                 hwrng_handle_msg
ffffffff0003708c         5603708c       20     1                 hwrng_handle_send_unblocked
ffffffff00037090         56037090       20     1                 smc_service_handle_msg
ffffffff00037094         56037094       18     4         lto.tmp:(.text..L.cfi.jumptable.1561)
ffffffff00037094         56037094        0     1                 $x.475
ffffffff00037094         56037094       18     1                 nop_handle_channel_cleanup
ffffffff00037094         56037094        1     1                 __typeid__ZTSFvPvE_global_addr
ffffffff00037098         56037098       18     1                 test_handle_channel_cleanup
ffffffff0003709c         5603709c       18     1                 apploader_service_handle_channel_cleanup
ffffffff000370a0         560370a0       18     1                 generic_ta_service_handle_channel_cleanup
ffffffff000370a4         560370a4       18     1                 hwrng_handle_channel_cleanup
ffffffff000370a8         560370a8       18     1                 smc_service_handle_channel_cleanup
ffffffff000370ac         560370ac       20     4 .hash
ffffffff000370ac         560370ac       20     4         <internal>:(.hash)
ffffffff000370d0         560370d0       48     8 .dynsym
ffffffff000370d0         560370d0       48     8         <internal>:(.dynsym)
ffffffff00037118         56037118       21     1 .dynstr
ffffffff00037118         56037118       21     1         <internal>:(.dynstr)
ffffffff00037140         56037140       1c     8 .gnu.hash
ffffffff00037140         56037140       1c     8         <internal>:(.gnu.hash)
ffffffff0003715c         5603715c        0     1 
ffffffff0003715c         5603715c        0     1 __exidx_start = .
ffffffff0003715c         5603715c        0     1 __exidx_end = .
ffffffff0003715c         5603715c        0     1 .fake_post_text
ffffffff0003715c         5603715c        0     1         __code_end = .
ffffffff00038000         56038000     b5b0  4096 .rodata
ffffffff00038000         56038000        0     1         __rodata_start = .
ffffffff00038000         56038000        0     1         __fault_handler_table_start = .
ffffffff00038000         56038000       30     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(usercopy.o):(.rodata.fault_handler_table)
ffffffff00038000         56038000        0     1                 $d.1
ffffffff00038010         56038010        0     1                 $d.3
ffffffff00038020         56038020        0     1                 $d.5
ffffffff00038030         56038030       30     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(safecopy.o):(.rodata.fault_handler_table)
ffffffff00038030         56038030        0     1                 $d.1
ffffffff00038040         56038040        0     1                 $d.3
ffffffff00038050         56038050        0     1                 $d.5
ffffffff00038060         56038060        0     1         __fault_handler_table_end = .
ffffffff00038060         56038060     a63e     1         <internal>:(.rodata)
ffffffff0004269e         5604269e       f6     2         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(printf.o):(.rodata._printf_engine_internal)
ffffffff0004269e         5604269e        0     1                 $d.9
ffffffff00042794         56042794       20     1         <internal>:(.rodata)
ffffffff000427b8         560427b8       30     8         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(c_locale.o):(.rodata.__c_locale)
ffffffff000427b8         560427b8        0     1                 $d.2
ffffffff000427b8         560427b8       30     1                 __c_locale
ffffffff000427e8         560427e8       44     4         lto.tmp:(.rodata.sys_caam_ioctl.cfi)
ffffffff000427e8         560427e8        0     1                 $d.12
ffffffff0004282c         5604282c       15     1         lto.tmp:(.rodata.imx_linux_fastcall.cfi)
ffffffff0004282c         5604282c        0     1                 $d.30
ffffffff00042841         56042841       3a     1         lto.tmp:(.rodata.snvs_fastcall.cfi)
ffffffff00042841         56042841        0     1                 $d.33
ffffffff0004287c         5604287c        a     2         lto.tmp:(.rodata.vpu_fastcall.cfi)
ffffffff0004287c         5604287c        0     1                 $d.37
ffffffff00042888         56042888       d8     4         lto.tmp:(.rodata.arm64_sync_exception)
ffffffff00042888         56042888        0     1                 $d.45
ffffffff00042960         56042960       36     1         lto.tmp:(.rodata.print_fault_code)
ffffffff00042960         56042960        0     1                 $d.47
ffffffff00042998         56042998       10     4         lto.tmp:(.rodata.arch_mmu_query)
ffffffff00042998         56042998        0     1                 $d.60
ffffffff000429a8         560429a8       10     4         lto.tmp:(.rodata.arm64_mmu_map_aspace)
ffffffff000429a8         560429a8        0     1                 $d.62
ffffffff000429b8         560429b8       14     4         lto.tmp:(.rodata.thread_set_pinned_cpu)
ffffffff000429b8         560429b8        0     1                 $d.91
ffffffff000429cc         560429cc        4     1         lto.tmp:(.rodata.test_handle_msg.cfi)
ffffffff000429cc         560429cc        0     1                 $d.236
ffffffff000429d0         560429d0       6c     4         lto.tmp:(.rodata.load_app_config_options)
ffffffff000429d0         560429d0        0     1                 $d.335
ffffffff00042a3c         56042a3c       34     4         lto.tmp:(.rodata.rctee_sm_stdcall.cfi)
ffffffff00042a3c         56042a3c        0     1                 $d.358
ffffffff00042a70         56042a70       14     4         lto.tmp:(.rodata.ql_rcipc_handle_cmd)
ffffffff00042a70         56042a70        0     1                 $d.379
ffffffff00042a84         56042a84       70     4         lto.tmp:(.rodata.ext_mem_get_vmm_obj)
ffffffff00042a84         56042a84        0     1                 $d.406
ffffffff00042af4         56042af4       10     4         lto.tmp:(.rodata.generic_ta_service_handle_msg.cfi)
ffffffff00042af4         56042af4        0     1                 $d.425
ffffffff00042b04         56042b04       24     4         lto.tmp:(.rodata.decriptor_template_gen_dek_blob)
ffffffff00042b04         56042b04       24     1                 decriptor_template_gen_dek_blob
ffffffff00042b04         56042b04        0     1                 $d.491
ffffffff00042b28         56042b28       24     4         lto.tmp:(.rodata.decriptor_template_decap_dek_blob)
ffffffff00042b28         56042b28       24     1                 decriptor_template_decap_dek_blob
ffffffff00042b28         56042b28        0     1                 $d.492
ffffffff00042b4c         56042b4c       30     4         lto.tmp:(.rodata.aes_decriptor_template_ecb_cbc)
ffffffff00042b4c         56042b4c       30     1                 aes_decriptor_template_ecb_cbc
ffffffff00042b4c         56042b4c        0     1                 $d.493
ffffffff00042b7c         56042b7c       5c     4         lto.tmp:(.rodata.aes_decriptor_template_ctr)
ffffffff00042b7c         56042b7c       5c     1                 aes_decriptor_template_ctr
ffffffff00042b7c         56042b7c        0     1                 $d.494
ffffffff00042bd8         56042bd8       40     4         lto.tmp:(.rodata.aes_decriptor_template_gcm)
ffffffff00042bd8         56042bd8       40     1                 aes_decriptor_template_gcm
ffffffff00042bd8         56042bd8        0     1                 $d.495
ffffffff00042c18         56042c18       30     4         lto.tmp:(.rodata.des_decriptor_template_ede_cbc)
ffffffff00042c18         56042c18       30     1                 des_decriptor_template_ede_cbc
ffffffff00042c18         56042c18        0     1                 $d.496
ffffffff00042c48         56042c48      800     8         lto.tmp:(.rodata.int_handler_table_shared)
ffffffff00042c48         56042c48      800     1                 int_handler_table_shared
ffffffff00042c48         56042c48        0     1                 $d.517
ffffffff00043448         56043448       20     4         <internal>:(.rodata)
ffffffff00043468         56043468       18     8         lto.tmp:(.rodata..Lswitch.table.arm_ffa_init)
ffffffff00043468         56043468        0     1                 $d.586
ffffffff00043480         56043480       18     4         lto.tmp:(.rodata..Lswitch.table.arm_ffa_init.64)
ffffffff00043480         56043480        0     1                 $d.587
ffffffff00043498         56043498       1c     4         lto.tmp:(.rodata..Lswitch.table.rctee_app_request_start_by_apploder)
ffffffff00043498         56043498        0     1                 $d.648
ffffffff000434b4         560434b4       10     4         lto.tmp:(.rodata.zero_uuid)
ffffffff000434b4         560434b4       10     1                 zero_uuid
ffffffff000434b4         560434b4        0     1                 $d.660
ffffffff000434c4         560434c4       6c     4         lto.tmp:(.rodata._descr0)
ffffffff000434c4         560434c4       6c     1                 _descr0
ffffffff000434c4         560434c4        0     1                 $d.662
ffffffff00043530         56043530       10     4         lto.tmp:(.rodata.apploader_user_uuid)
ffffffff00043530         56043530       10     1                 apploader_user_uuid
ffffffff00043530         56043530        0     1                 $d.704
ffffffff00043540         56043540       18     8         lto.tmp:(.rodata.generic_ta_service_port_acl)
ffffffff00043540         56043540       18     1                 generic_ta_service_port_acl
ffffffff00043540         56043540        0     1                 $d.709
ffffffff00043558         56043558       18     8         lto.tmp:(.rodata.hwrng_srv_port_acl)
ffffffff00043558         56043558       18     1                 hwrng_srv_port_acl
ffffffff00043558         56043558        0     1                 $d.714
ffffffff00043570         56043570       10     4         lto.tmp:(.rodata.kernel_uuid)
ffffffff00043570         56043570       10     1                 kernel_uuid
ffffffff00043570         56043570        0     1                 $d.722
ffffffff00043580         56043580       18     8         lto.tmp:(.rodata.smc_service_port_acl)
ffffffff00043580         56043580       18     1                 smc_service_port_acl
ffffffff00043580         56043580        0     1                 $d.723
ffffffff00043598         56043598       18     8         lto.tmp:(.rodata..Lswitch.table.arm64_early_mmu_init)
ffffffff00043598         56043598        0     1                 $d.724
ffffffff000435b0         560435b0      20c     4 .__manifest_data
ffffffff000435b0         560435b0       50     4         out/build-imx8mp/user_product/ta/apploader/apploader.ko:(.ta.manifest)
ffffffff000435b0         560435b0        0     1                 .ta.manifest.start
ffffffff000435b0         560435b0        0     1                 $d.1
ffffffff00043600         56043600        0     1                 .ta.manifest.end
ffffffff00043600         56043600       44     4         out/build-imx8mp/user_product/ta/hwcrypto/hwcrypto.ko:(.ta.manifest)
ffffffff00043600         56043600        0     1                 .ta.manifest.start
ffffffff00043600         56043600        0     1                 $d.1
ffffffff00043644         56043644        0     1                 .ta.manifest.end
ffffffff00043644         56043644       78     4         out/build-imx8mp/user_product/ta/receiver/receiver.ko:(.ta.manifest)
ffffffff00043644         56043644        0     1                 .ta.manifest.start
ffffffff00043644         56043644        0     1                 $d.1
ffffffff000436bc         560436bc        0     1                 .ta.manifest.end
ffffffff000436bc         560436bc       a8     4         out/build-imx8mp/user_product/ta/manifest-test/manifest-test.ko:(.ta.manifest)
ffffffff000436bc         560436bc        0     1                 .ta.manifest.start
ffffffff000436bc         560436bc        0     1                 $d.1
ffffffff00043764         56043764        0     1                 .ta.manifest.end
ffffffff00043764         56043764       58     4         out/build-imx8mp/user_product/ta/tongsuo_crypto_test/tongsuo_crypto_test.ko:(.ta.manifest)
ffffffff00043764         56043764        0     1                 .ta.manifest.start
ffffffff00043764         56043764        0     1                 $d.1
ffffffff000437bc         560437bc        0     1                 .ta.manifest.end
ffffffff000437bc         560437bc   118844     1 .__rctee_app
ffffffff000437bc         560437bc      844     1         . = ALIGN ( 0x1000 )
ffffffff00044000         56044000    c3000     1         out/build-imx8mp/user_product/ta/apploader/apploader.ko:(.ta.data)
ffffffff00044000         56044000        0     1                 .ta.body.start
ffffffff00044000         56044000        0     1                 $d.0
ffffffff00107000         56107000        0     1                 .ta.body.end
ffffffff00107000         56107000    28000     1         out/build-imx8mp/user_product/ta/hwcrypto/hwcrypto.ko:(.ta.data)
ffffffff00107000         56107000        0     1                 .ta.body.start
ffffffff00107000         56107000        0     1                 $d.0
ffffffff0012f000         5612f000        0     1                 .ta.body.end
ffffffff0012f000         5612f000    11000     1         out/build-imx8mp/user_product/ta/receiver/receiver.ko:(.ta.data)
ffffffff0012f000         5612f000        0     1                 .ta.body.start
ffffffff0012f000         5612f000        0     1                 $d.0
ffffffff00140000         56140000        0     1                 .ta.body.end
ffffffff00140000         56140000     d000     1         out/build-imx8mp/user_product/ta/manifest-test/manifest-test.ko:(.ta.data)
ffffffff00140000         56140000        0     1                 .ta.body.start
ffffffff00140000         56140000        0     1                 $d.0
ffffffff0014d000         5614d000        0     1                 .ta.body.end
ffffffff0014d000         5614d000     f000     1         out/build-imx8mp/user_product/ta/tongsuo_crypto_test/tongsuo_crypto_test.ko:(.ta.data)
ffffffff0014d000         5614d000        0     1                 .ta.body.start
ffffffff0014d000         5614d000        0     1                 $d.0
ffffffff0015c000         5615c000        0     1                 .ta.body.end
ffffffff0015c000         5615c000        0     1         . = ALIGN ( 0x1000 )
ffffffff0015c000         5615c000       a0     1 .__rctee_app_list
ffffffff0015c000         5615c000        0     1         __rctee_app_list_start = .
ffffffff0015c000         5615c000       20     1         out/build-imx8mp/user_product/ta/apploader/apploader.ko:(.ta.list)
ffffffff0015c000         5615c000        0     1                 $d.2
ffffffff0015c020         5615c020       20     1         out/build-imx8mp/user_product/ta/hwcrypto/hwcrypto.ko:(.ta.list)
ffffffff0015c020         5615c020        0     1                 $d.2
ffffffff0015c040         5615c040       20     1         out/build-imx8mp/user_product/ta/receiver/receiver.ko:(.ta.list)
ffffffff0015c040         5615c040        0     1                 $d.2
ffffffff0015c060         5615c060       20     1         out/build-imx8mp/user_product/ta/manifest-test/manifest-test.ko:(.ta.list)
ffffffff0015c060         5615c060        0     1                 $d.2
ffffffff0015c080         5615c080       20     1         out/build-imx8mp/user_product/ta/tongsuo_crypto_test/tongsuo_crypto_test.ko:(.ta.list)
ffffffff0015c080         5615c080        0     1                 $d.2
ffffffff0015c0a0         5615c0a0        0     1         __rctee_app_list_end = .
ffffffff0015c0a0         5615c0a0        0     1 .drivers
ffffffff0015c0a0         5615c0a0        0     1         __drivers = .
ffffffff0015c0a0         5615c0a0        0     1         __drivers_end = .
ffffffff0015c0a0         5615c0a0        0     1 .apps
ffffffff0015c0a0         5615c0a0        0     1         __apps_start = .
ffffffff0015c0a0         5615c0a0        0     1         __apps_end = .
ffffffff0015c0a0         5615c0a0      378     8 .lk_init
ffffffff0015c0a0         5615c0a0        0     1         __lk_init = .
ffffffff0015c0a0         5615c0a0      378     8         lto.tmp:(.lk_init)
ffffffff0015c0a0         5615c0a0        0     1                 $d.479
ffffffff0015c0a0         5615c0a0       18     1                 _init_struct_uart_console
ffffffff0015c0b8         5615c0b8       18     1                 _init_struct_platform_after_vm
ffffffff0015c0d0         5615c0d0       18     1                 _init_struct_allowed_app_ranges
ffffffff0015c0e8         5615c0e8       18     1                 _init_struct_imx_caam
ffffffff0015c100         5615c100       18     1                 _init_struct_caam_dev_init
ffffffff0015c118         5615c118       18     1                 _init_struct_csu_dev_init
ffffffff0015c130         5615c130       18     1                 _init_struct_imx_linux_driver
ffffffff0015c148         5615c148       18     1                 _init_struct_snvs_driver
ffffffff0015c160         5615c160       18     1                 _init_struct_vpu_driver
ffffffff0015c178         5615c178       18     1                 _init_struct_vpu_enc_driver
ffffffff0015c190         5615c190       18     1                 _init_struct_arm64_pan_init
ffffffff0015c1a8         5615c1a8       18     1                 _init_struct_arm_gic_init_percpu
ffffffff0015c1c0         5615c1c0       18     1                 _init_struct_arm_gic_suspend_cpu
ffffffff0015c1d8         5615c1d8       18     1                 _init_struct_arm_gic_resume_cpu
ffffffff0015c1f0         5615c1f0       18     1                 _init_struct_arm_generic_timer_init_secondary_cpu
ffffffff0015c208         5615c208       18     1                 _init_struct_arm_generic_timer_suspend_cpu
ffffffff0015c220         5615c220       18     1                 _init_struct_arm_generic_timer_resume_cpu
ffffffff0015c238         5615c238       18     1                 _init_struct_vm_preheap
ffffffff0015c250         5615c250       18     1                 _init_struct_vm
ffffffff0015c268         5615c268       18     1                 _init_struct_busy_test_init
ffffffff0015c280         5615c280       18     1                 _init_struct_busy_test_cpu_init
ffffffff0015c298         5615c298       18     1                 _init_struct_arm_ffa_init
ffffffff0015c2b0         5615c2b0       18     1                 _init_struct_ktipctest
ffffffff0015c2c8         5615c2c8       18     1                 _init_struct_ktipc_test_server_init
ffffffff0015c2e0         5615c2e0       18     1                 _init_struct_memlog
ffffffff0015c2f8         5615c2f8       18     1                 _init_struct_uctx
ffffffff0015c310         5615c310       18     1                 _init_struct_librctee_apps
ffffffff0015c328         5615c328       18     1                 _init_struct_librctee
ffffffff0015c340         5615c340       18     1                 _init_struct_rctee_smcall
ffffffff0015c358         5615c358       18     1                 _init_struct_register_rcipc_init
ffffffff0015c370         5615c370       18     1                 _init_struct_libsm
ffffffff0015c388         5615c388       18     1                 _init_struct_libsm_bootargs
ffffffff0015c3a0         5615c3a0       18     1                 _init_struct_shared_mem
ffffffff0015c3b8         5615c3b8       18     1                 _init_struct_apploader
ffffffff0015c3d0         5615c3d0       18     1                 _init_struct_generic_ta
ffffffff0015c3e8         5615c3e8       18     1                 _init_struct_hwrng_ktipc_server_init
ffffffff0015c400         5615c400       18     1                 _init_struct_smc
ffffffff0015c418         5615c418        0     1         __lk_init_end = .
ffffffff0015c418         5615c418        0     1 
ffffffff0015c418         5615c418        0     1 
ffffffff0015c418         5615c418       a0     8 .relr.dyn
ffffffff0015c418         5615c418        0     1         __relr_start = .
ffffffff0015c418         5615c418       a0     8         <internal>:(.relr.dyn)
ffffffff0015c4b8         5615c4b8        0     1         __relr_end = .
ffffffff0015c4b8         5615c4b8        0     8 .ctors
ffffffff0015c4b8         5615c4b8        0     1         __ctor_list = .
ffffffff0015c4b8         5615c4b8        0     1         __ctor_end = .
ffffffff0015c4b8         5615c4b8        0     8 .dtors
ffffffff0015c4b8         5615c4b8        0     1         __dtor_list = .
ffffffff0015c4b8         5615c4b8        0     1         __dtor_end = .
ffffffff0015c4b8         5615c4b8       d0     8 .dynamic
ffffffff0015c4b8         5615c4b8       d0     8         <internal>:(.dynamic)
ffffffff0015c588         5615c588       78     8 .got
ffffffff0015c588         5615c588       78     8         <internal>:(.got)
ffffffff0015c600         5615c600        0     1 .fake_post_rodata
ffffffff0015c600         5615c600        0     1         __rodata_end = .
ffffffff0015d000         5615d000     1c58  4096 .data
ffffffff0015d000         5615d000        0     1         __data_start_rom = .
ffffffff0015d000         5615d000        0     1         __data_start = .
ffffffff0015d000         5615d000       10     8         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(start.o):(.data)
ffffffff0015d000         5615d000        0     1                 $d.3
ffffffff0015d000         5615d000        0     1                 mmu_on_vaddr_ptr
ffffffff0015d008         5615d008        0     1                 tt_trampoline_not_ready
ffffffff0015d00c         5615d00c        0     1                 page_tables_not_ready
ffffffff0015d010         5615d010        4     4         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(rand.o):(.data.randseed)
ffffffff0015d010         5615d010        4     1                 randseed
ffffffff0015d010         5615d010        0     1                 $d.3
ffffffff0015d018         5615d018       e8     8         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stderr.o):(.data.__stderr_FILE)
ffffffff0015d018         5615d018        0     1                 $d.1
ffffffff0015d018         5615d018       e8     1                 __stderr_FILE
ffffffff0015d100         5615d100       e8     8         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdin.o):(.data.__stdin_FILE)
ffffffff0015d100         5615d100        0     1                 $d.1
ffffffff0015d100         5615d100       e8     1                 __stdin_FILE
ffffffff0015d1e8         5615d1e8       e8     8         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdout.o):(.data.__stdout_FILE)
ffffffff0015d1e8         5615d1e8        0     1                 $d.1
ffffffff0015d1e8         5615d1e8       e8     1                 __stdout_FILE
ffffffff0015d2d0         5615d2d0       18     8         lto.tmp:(.data.console_entity)
ffffffff0015d2d0         5615d2d0       18     1                 console_entity
ffffffff0015d2d0         5615d2d0        0     1                 $d.477
ffffffff0015d2e8         5615d2e8       60     8         lto.tmp:(.data.ram_arena)
ffffffff0015d2e8         5615d2e8       60     1                 ram_arena
ffffffff0015d2e8         5615d2e8        0     1                 $d.480
ffffffff0015d348         5615d348       30     8         lto.tmp:(.data.oemcrypto_range)
ffffffff0015d348         5615d348       30     1                 oemcrypto_range
ffffffff0015d348         5615d348        0     1                 $d.481
ffffffff0015d378         5615d378       30     8         lto.tmp:(.data.confirmation_ui_range)
ffffffff0015d378         5615d378       30     1                 confirmation_ui_range
ffffffff0015d378         5615d378        0     1                 $d.482
ffffffff0015d3c0         5615d3c0       20    64         lto.tmp:(.data.entropy)
ffffffff0015d3c0         5615d3c0       20     1                 entropy
ffffffff0015d3c0         5615d3c0        0     1                 $d.487
ffffffff0015d3e0         5615d3e0       38     8         lto.tmp:(.data.lock)
ffffffff0015d3e0         5615d3e0       38     1                 lock
ffffffff0015d3e0         5615d3e0        0     1                 $d.488
ffffffff0015d418         5615d418       18     8         lto.tmp:(.data.rel.ro.caam_ops)
ffffffff0015d418         5615d418       18     1                 caam_ops
ffffffff0015d418         5615d418        0     1                 $d.489
ffffffff0015d430         5615d430       10     4         lto.tmp:(.data.hwcrypto_ta_uuid)
ffffffff0015d430         5615d430       10     1                 hwcrypto_ta_uuid
ffffffff0015d430         5615d430        0     1                 $d.490
ffffffff0015d440         5615d440       18     8         lto.tmp:(.data.rel.ro.csu_ops)
ffffffff0015d440         5615d440       18     1                 csu_ops
ffffffff0015d440         5615d440        0     1                 $d.497
ffffffff0015d458         5615d458       10     4         lto.tmp:(.data.hwsecure_ta_uuid)
ffffffff0015d458         5615d458       10     1                 hwsecure_ta_uuid
ffffffff0015d458         5615d458        0     1                 $d.498
ffffffff0015d468         5615d468       10     4         lto.tmp:(.data.secure_fb_impl_ta_uuid)
ffffffff0015d468         5615d468       10     1                 secure_fb_impl_ta_uuid
ffffffff0015d468         5615d468        0     1                 $d.499
ffffffff0015d478         5615d478       18     8         lto.tmp:(.data.imx_linux_entity)
ffffffff0015d478         5615d478       18     1                 imx_linux_entity
ffffffff0015d478         5615d478        0     1                 $d.503
ffffffff0015d490         5615d490       18     8         lto.tmp:(.data.snvs_entity)
ffffffff0015d490         5615d490       18     1                 snvs_entity
ffffffff0015d490         5615d490        0     1                 $d.504
ffffffff0015d4a8         5615d4a8       18     8         lto.tmp:(.data.vpu_entity)
ffffffff0015d4a8         5615d4a8       18     1                 vpu_entity
ffffffff0015d4a8         5615d4a8        0     1                 $d.505
ffffffff0015d4c0         5615d4c0       10     4         lto.tmp:(.data.inout_buffer_paddr)
ffffffff0015d4c0         5615d4c0       10     1                 inout_buffer_paddr
ffffffff0015d4c0         5615d4c0        0     1                 $d.506
ffffffff0015d4d0         5615d4d0       28     4         lto.tmp:(.data.inout_buffer_g2_paddr)
ffffffff0015d4d0         5615d4d0       28     1                 inout_buffer_g2_paddr
ffffffff0015d4d0         5615d4d0        0     1                 $d.507
ffffffff0015d4f8         5615d4f8       18     8         lto.tmp:(.data.vpu_enc_entity)
ffffffff0015d4f8         5615d4f8       18     1                 vpu_enc_entity
ffffffff0015d4f8         5615d4f8        0     1                 $d.508
ffffffff0015d510         5615d510        8     8         lto.tmp:(.data.arm_boot_cpu_lock)
ffffffff0015d510         5615d510        8     1                 arm_boot_cpu_lock
ffffffff0015d510         5615d510        0     1                 $d.510
ffffffff0015d518         5615d518       18     8         lto.tmp:(.data.phys_mem_obj_ops)
ffffffff0015d518         5615d518       18     1                 phys_mem_obj_ops
ffffffff0015d518         5615d518        0     1                 $d.548
ffffffff0015d530         5615d530       10     8         lto.tmp:(.data.arena_list)
ffffffff0015d530         5615d530       10     1                 arena_list
ffffffff0015d530         5615d530        0     1                 $d.549
ffffffff0015d540         5615d540       38     8         lto.tmp:(.data.lock.406)
ffffffff0015d540         5615d540       38     1                 lock.406
ffffffff0015d540         5615d540        0     1                 $d.551
ffffffff0015d578         5615d578       18     8         lto.tmp:(.data.pmm_vmm_obj_ops)
ffffffff0015d578         5615d578       18     1                 pmm_vmm_obj_ops
ffffffff0015d578         5615d578        0     1                 $d.552
ffffffff0015d590         5615d590       38     8         lto.tmp:(.data.res_group_lock)
ffffffff0015d590         5615d590       38     1                 res_group_lock
ffffffff0015d590         5615d590        0     1                 $d.553
ffffffff0015d5c8         5615d5c8       a0     8         lto.tmp:(.data.mmu_initial_mappings)
ffffffff0015d5c8         5615d5c8       a0     1                 mmu_initial_mappings
ffffffff0015d5c8         5615d5c8        0     1                 $d.554
ffffffff0015d668         5615d668        8     8         lto.tmp:(.data.boot_alloc_end)
ffffffff0015d668         5615d668        8     1                 boot_alloc_end
ffffffff0015d668         5615d668        0     1                 $d.556
ffffffff0015d670         5615d670       10     8         lto.tmp:(.data.aspace_list)
ffffffff0015d670         5615d670       10     1                 aspace_list
ffffffff0015d670         5615d670        0     1                 $d.557
ffffffff0015d680         5615d680       38     8         lto.tmp:(.data.vmm_lock)
ffffffff0015d680         5615d680       38     1                 vmm_lock
ffffffff0015d680         5615d680        0     1                 $d.558
ffffffff0015d6b8         5615d6b8       18     8         lto.tmp:(.data.vmm_res_obj_ops)
ffffffff0015d6b8         5615d6b8       18     1                 vmm_res_obj_ops
ffffffff0015d6b8         5615d6b8        0     1                 $d.559
ffffffff0015d6d0         5615d6d0        4     4         lto.tmp:(.data.thread_lock_owner)
ffffffff0015d6d0         5615d6d0        4     1                 thread_lock_owner
ffffffff0015d6d0         5615d6d0        0     1                 $d.560
ffffffff0015d6d8         5615d6d8        8     8         lto.tmp:(.data.early_log_writeptr)
ffffffff0015d6d8         5615d6d8        8     1                 early_log_writeptr
ffffffff0015d6d8         5615d6d8        0     1                 $d.564
ffffffff0015d6e0         5615d6e0       10     8         lto.tmp:(.data.print_callbacks)
ffffffff0015d6e0         5615d6e0       10     1                 print_callbacks
ffffffff0015d6e0         5615d6e0        0     1                 $d.566
ffffffff0015d6f0         5615d6f0       28     8         lto.tmp:(.data.rel.ro.console_io_hooks)
ffffffff0015d6f0         5615d6f0       28     1                 console_io_hooks
ffffffff0015d6f0         5615d6f0        0     1                 $d.567
ffffffff0015d718         5615d718       10     8         lto.tmp:(.data.console_io)
ffffffff0015d718         5615d718        0     1                 $d.568
ffffffff0015d718         5615d718       10     1                 console_io
ffffffff0015d728         5615d728        4     4         lto.tmp:(.data.lock_held_by)
ffffffff0015d728         5615d728        4     1                 lock_held_by
ffffffff0015d728         5615d728        0     1                 $d.569
ffffffff0015d730         5615d730        8     8         lto.tmp:(.data.early_log_end)
ffffffff0015d730         5615d730        8     1                 early_log_end
ffffffff0015d730         5615d730        0     1                 $d.570
ffffffff0015d738         5615d738       38     8         lto.tmp:(.data.print_mutex)
ffffffff0015d738         5615d738       38     1                 print_mutex
ffffffff0015d738         5615d738        0     1                 $d.572
ffffffff0015d770         5615d770       30     8         lto.tmp:(.data.busy_test_event)
ffffffff0015d770         5615d770       30     1                 busy_test_event
ffffffff0015d770         5615d770        0     1                 $d.575
ffffffff0015d7a0         5615d7a0       38     8         lto.tmp:(.data.ffa_rxtx_buffer_lock)
ffffffff0015d7a0         5615d7a0       38     1                 ffa_rxtx_buffer_lock
ffffffff0015d7a0         5615d7a0        0     1                 $d.578
ffffffff0015d7d8         5615d7d8       10     8         lto.tmp:(.data._test_list)
ffffffff0015d7d8         5615d7d8       10     1                 _test_list
ffffffff0015d7d8         5615d7d8        0     1                 $d.588
ffffffff0015d7e8         5615d7e8       30     8         lto.tmp:(.data.ktipctest_connecterr_node)
ffffffff0015d7e8         5615d7e8       30     1                 ktipctest_connecterr_node
ffffffff0015d7e8         5615d7e8        0     1                 $d.589
ffffffff0015d818         5615d818       30     8         lto.tmp:(.data.ktipctest_blockedport_node)
ffffffff0015d818         5615d818       30     1                 ktipctest_blockedport_node
ffffffff0015d818         5615d818        0     1                 $d.590
ffffffff0015d848         5615d848       30     8         lto.tmp:(.data.ktipctest_echo_node)
ffffffff0015d848         5615d848       30     1                 ktipctest_echo_node
ffffffff0015d848         5615d848        0     1                 $d.591
ffffffff0015d878         5615d878       30     8         lto.tmp:(.data.ktipctest_echo8_node)
ffffffff0015d878         5615d878       30     1                 ktipctest_echo8_node
ffffffff0015d878         5615d878        0     1                 $d.592
ffffffff0015d8a8         5615d8a8       30     8         lto.tmp:(.data.ktipctest_close_node)
ffffffff0015d8a8         5615d8a8       30     1                 ktipctest_close_node
ffffffff0015d8a8         5615d8a8        0     1                 $d.593
ffffffff0015d8d8         5615d8d8       30     8         lto.tmp:(.data.ktipctest_blockedsend_node)
ffffffff0015d8d8         5615d8d8       30     1                 ktipctest_blockedsend_node
ffffffff0015d8d8         5615d8d8        0     1                 $d.594
ffffffff0015d908         5615d908       1c     1         lto.tmp:(.data.test_pattern)
ffffffff0015d908         5615d908       1c     1                 test_pattern
ffffffff0015d908         5615d908        0     1                 $d.605
ffffffff0015d928         5615d928       78     8         lto.tmp:(.data.ktipctest_init.test)
ffffffff0015d928         5615d928       78     1                 ktipctest_init.test
ffffffff0015d928         5615d928        0     1                 $d.606
ffffffff0015d9a0         5615d9a0       10     8         lto.tmp:(.data._test_param_list)
ffffffff0015d9a0         5615d9a0       10     1                 _test_param_list
ffffffff0015d9a0         5615d9a0        0     1                 $d.607
ffffffff0015d9b0         5615d9b0       48     8         lto.tmp:(.data.ktipc_test_server)
ffffffff0015d9b0         5615d9b0       48     1                 ktipc_test_server
ffffffff0015d9b0         5615d9b0        0     1                 $d.608
ffffffff0015d9f8         5615d9f8       28     8         lto.tmp:(.data.rel.ro.test_srv_port)
ffffffff0015d9f8         5615d9f8       28     1                 test_srv_port
ffffffff0015d9f8         5615d9f8        0     1                 $d.609
ffffffff0015da20         5615da20       28     8         lto.tmp:(.data.rel.ro.test_srv_ops)
ffffffff0015da20         5615da20       28     1                 test_srv_ops
ffffffff0015da20         5615da20        0     1                 $d.610
ffffffff0015da48         5615da48       28     8         lto.tmp:(.data.rel.ro.blocked_srv_port)
ffffffff0015da48         5615da48       28     1                 blocked_srv_port
ffffffff0015da48         5615da48        0     1                 $d.611
ffffffff0015da70         5615da70       28     8         lto.tmp:(.data.rel.ro.connecterr_srv_port)
ffffffff0015da70         5615da70       28     1                 connecterr_srv_port
ffffffff0015da70         5615da70        0     1                 $d.612
ffffffff0015da98         5615da98       28     8         lto.tmp:(.data.rel.ro.connecterr_srv_ops)
ffffffff0015da98         5615da98       28     1                 connecterr_srv_ops
ffffffff0015da98         5615da98        0     1                 $d.613
ffffffff0015dac0         5615dac0       18     8         lto.tmp:(.data.rel.ro.test_srv_port_acl)
ffffffff0015dac0         5615dac0       18     1                 test_srv_port_acl
ffffffff0015dac0         5615dac0        0     1                 $d.614
ffffffff0015dad8         5615dad8        8     8         lto.tmp:(.data.valid_uuids)
ffffffff0015dad8         5615dad8        8     1                 valid_uuids
ffffffff0015dad8         5615dad8        0     1                 $d.615
ffffffff0015dae0         5615dae0       80     8         lto.tmp:(.data.rel.ro..L__const.test_handle_msg.iov)
ffffffff0015dae0         5615dae0        0     1                 $d.618
ffffffff0015db60         5615db60       18     8         lto.tmp:(.data.rel.ro.blocked_srv_port_acl)
ffffffff0015db60         5615db60       18     1                 blocked_srv_port_acl
ffffffff0015db60         5615db60        0     1                 $d.619
ffffffff0015db78         5615db78        8     8         lto.tmp:(.data.bad_uuids)
ffffffff0015db78         5615db78        8     1                 bad_uuids
ffffffff0015db78         5615db78        0     1                 $d.620
ffffffff0015db80         5615db80       18     8         lto.tmp:(.data.rel.ro.connecterr_srv_port_acl)
ffffffff0015db80         5615db80       18     1                 connecterr_srv_port_acl
ffffffff0015db80         5615db80        0     1                 $d.621
ffffffff0015db98         5615db98       10     8         lto.tmp:(.data.log_list)
ffffffff0015db98         5615db98       10     1                 log_list
ffffffff0015db98         5615db98        0     1                 $d.623
ffffffff0015dba8         5615dba8       38     8         lto.tmp:(.data.es_lock)
ffffffff0015dba8         5615dba8       38     1                 es_lock
ffffffff0015dba8         5615dba8        0     1                 $d.624
ffffffff0015dbe0         5615dbe0       38     8         lto.tmp:(.data.g_hset_lock)
ffffffff0015dbe0         5615dbe0       38     1                 g_hset_lock
ffffffff0015dbe0         5615dbe0        0     1                 $d.625
ffffffff0015dc18         5615dc18       30     8         lto.tmp:(.data.hset_ops)
ffffffff0015dc18         5615dc18       30     1                 hset_ops
ffffffff0015dc18         5615dc18        0     1                 $d.626
ffffffff0015dc48         5615dc48       30     8         lto.tmp:(.data.ipc_chan_handle_ops)
ffffffff0015dc48         5615dc48       30     1                 ipc_chan_handle_ops
ffffffff0015dc48         5615dc48        0     1                 $d.627
ffffffff0015dc78         5615dc78       30     8         lto.tmp:(.data.ipc_port_handle_ops)
ffffffff0015dc78         5615dc78       30     1                 ipc_port_handle_ops
ffffffff0015dc78         5615dc78        0     1                 $d.628
ffffffff0015dca8         5615dca8       38     8         lto.tmp:(.data.ipc_port_lock)
ffffffff0015dca8         5615dca8       38     1                 ipc_port_lock
ffffffff0015dca8         5615dca8        0     1                 $d.629
ffffffff0015dce0         5615dce0       10     8         lto.tmp:(.data.waiting_for_port_chan_list)
ffffffff0015dce0         5615dce0       10     1                 waiting_for_port_chan_list
ffffffff0015dce0         5615dce0        0     1                 $d.630
ffffffff0015dcf0         5615dcf0       10     8         lto.tmp:(.data.ipc_port_list)
ffffffff0015dcf0         5615dcf0       10     1                 ipc_port_list
ffffffff0015dcf0         5615dcf0        0     1                 $d.631
ffffffff0015dd00         5615dd00       30     8         lto.tmp:(.data.memref_handle_ops)
ffffffff0015dd00         5615dd00       30     1                 memref_handle_ops
ffffffff0015dd00         5615dd00        0     1                 $d.632
ffffffff0015dd30         5615dd30       38     8         lto.tmp:(.data.fd_lock)
ffffffff0015dd30         5615dd30       38     1                 fd_lock
ffffffff0015dd30         5615dd30        0     1                 $d.633
ffffffff0015dd68         5615dd68       50     8         lto.tmp:(.data.sys_fds)
ffffffff0015dd68         5615dd68       50     1                 sys_fds
ffffffff0015dd68         5615dd68        0     1                 $d.634
ffffffff0015ddb8         5615ddb8       18     8         lto.tmp:(.data.rel.ro.sys_std_fd_op)
ffffffff0015ddb8         5615ddb8       18     1                 sys_std_fd_op
ffffffff0015ddb8         5615ddb8        0     1                 $d.635
ffffffff0015ddd0         5615ddd0       18     8         lto.tmp:(.data.rel.ro.fd_op)
ffffffff0015ddd0         5615ddd0       18     1                 fd_op
ffffffff0015ddd0         5615ddd0        0     1                 $d.637
ffffffff0015dde8         5615dde8       28     8         lto.tmp:(.data._uctx_notifier)
ffffffff0015dde8         5615dde8       28     1                 _uctx_notifier
ffffffff0015dde8         5615dde8        0     1                 $d.638
ffffffff0015de10         5615de10       38     8         lto.tmp:(.data.apps_lock)
ffffffff0015de10         5615de10       38     1                 apps_lock
ffffffff0015de10         5615de10        0     1                 $d.639
ffffffff0015de48         5615de48       10     8         lto.tmp:(.data.allowed_mmio_ranges_list)
ffffffff0015de48         5615de48       10     1                 allowed_mmio_ranges_list
ffffffff0015de48         5615de48        0     1                 $d.640
ffffffff0015de58         5615de58       10     8         lto.tmp:(.data.app_notifier_list)
ffffffff0015de58         5615de58       10     1                 app_notifier_list
ffffffff0015de58         5615de58        0     1                 $d.642
ffffffff0015de68         5615de68       10     8         lto.tmp:(.data.rctee_app_list)
ffffffff0015de68         5615de68       10     1                 rctee_app_list
ffffffff0015de68         5615de68        0     1                 $d.645
ffffffff0015de78         5615de78       30     8         lto.tmp:(.data.app_mgr_event)
ffffffff0015de78         5615de78       30     1                 app_mgr_event
ffffffff0015de78         5615de78        0     1                 $d.647
ffffffff0015dea8         5615dea8       38     8         lto.tmp:(.data.virtio_bus_notifier_lock)
ffffffff0015dea8         5615dea8       38     1                 virtio_bus_notifier_lock
ffffffff0015dea8         5615dea8        0     1                 $d.649
ffffffff0015dee0         5615dee0       10     8         lto.tmp:(.data.virtio_bus_notifier_list)
ffffffff0015dee0         5615dee0       10     1                 virtio_bus_notifier_list
ffffffff0015dee0         5615dee0        0     1                 $d.650
ffffffff0015def0         5615def0       28     8         lto.tmp:(.data._virtio_bus)
ffffffff0015def0         5615def0       28     1                 _virtio_bus
ffffffff0015def0         5615def0        0     1                 $d.651
ffffffff0015df18         5615df18       28     8         lto.tmp:(.data.rel.ro._rcipc_dev_ops)
ffffffff0015df18         5615df18       28     1                 _rcipc_dev_ops
ffffffff0015df18         5615df18        0     1                 $d.652
ffffffff0015df40         5615df40       18     8         lto.tmp:(.data.rcipc_ext_mem_ops)
ffffffff0015df40         5615df40       18     1                 rcipc_ext_mem_ops
ffffffff0015df40         5615df40        0     1                 $d.653
ffffffff0015df58         5615df58       10     8         lto.tmp:(.data.rel.ro.notify_cbs)
ffffffff0015df58         5615df58       10     1                 notify_cbs
ffffffff0015df58         5615df58        0     1                 $d.656
ffffffff0015df68         5615df68       10     8         lto.tmp:(.data._dev_list)
ffffffff0015df68         5615df68       10     1                 _dev_list
ffffffff0015df68         5615df68        0     1                 $d.659
ffffffff0015df78         5615df78       18     8         lto.tmp:(.data.register_rcipc_init.vb_notifier)
ffffffff0015df78         5615df78       18     1                 register_rcipc_init.vb_notifier
ffffffff0015df78         5615df78        0     1                 $d.661
ffffffff0015df90         5615df90       70     8         lto.tmp:(.data.stdcallstate)
ffffffff0015df90         5615df90       70     1                 stdcallstate
ffffffff0015df90         5615df90        0     1                 $d.663
ffffffff0015e000         5615e000        4     4         lto.tmp:(.data.sm_api_version_max)
ffffffff0015e000         5615e000        4     1                 sm_api_version_max
ffffffff0015e000         5615e000        0     1                 $d.665
ffffffff0015e008         5615e008       38     8         lto.tmp:(.data.boot_args_lock)
ffffffff0015e008         5615e008       38     1                 boot_args_lock
ffffffff0015e008         5615e008        0     1                 $d.672
ffffffff0015e040         5615e040      200     8         lto.tmp:(.data.sm_fastcall_table)
ffffffff0015e040         5615e040      200     1                 sm_fastcall_table
ffffffff0015e040         5615e040        0     1                 $d.681
ffffffff0015e240         5615e240      200     8         lto.tmp:(.data.sm_nopcall_table)
ffffffff0015e240         5615e240      200     1                 sm_nopcall_table
ffffffff0015e240         5615e240        0     1                 $d.682
ffffffff0015e440         5615e440      200     8         lto.tmp:(.data.sm_stdcall_table)
ffffffff0015e440         5615e440      200     1                 sm_stdcall_table
ffffffff0015e440         5615e440        0     1                 $d.683
ffffffff0015e640         5615e640       38     8         lto.tmp:(.data.smc_table_lock)
ffffffff0015e640         5615e640       38     1                 smc_table_lock
ffffffff0015e640         5615e640        0     1                 $d.684
ffffffff0015e678         5615e678       70     8         lto.tmp:(.data.rel.ro.sm_fastcall_function_table)
ffffffff0015e678         5615e678       70     1                 sm_fastcall_function_table
ffffffff0015e678         5615e678        0     1                 $d.685
ffffffff0015e6e8         5615e6e8       30     8         lto.tmp:(.data.rel.ro.sm_stdcall_function_table)
ffffffff0015e6e8         5615e6e8       30     1                 sm_stdcall_function_table
ffffffff0015e6e8         5615e6e8        0     1                 $d.686
ffffffff0015e718         5615e718       18     8         lto.tmp:(.data.sm_mem_obj_ops)
ffffffff0015e718         5615e718       18     1                 sm_mem_obj_ops
ffffffff0015e718         5615e718        0     1                 $d.687
ffffffff0015e730         5615e730       18     8         lto.tmp:(.data.sm_mem_obj_compat_ops)
ffffffff0015e730         5615e730       18     1                 sm_mem_obj_compat_ops
ffffffff0015e730         5615e730        0     1                 $d.688
ffffffff0015e748         5615e748      210     8         lto.tmp:(.data.rel.ro.syscall_table)
ffffffff0015e748         5615e748        0     1                 $d.692
ffffffff0015e748         5615e748      210     1                 syscall_table
ffffffff0015e958         5615e958        8     8         lto.tmp:(.data.nr_syscalls)
ffffffff0015e958         5615e958        0     1                 $d.693
ffffffff0015e958         5615e958        8     1                 nr_syscalls
ffffffff0015e960         5615e960       38     8         lto.tmp:(.data.unittest_lock)
ffffffff0015e960         5615e960       38     1                 unittest_lock
ffffffff0015e960         5615e960        0     1                 $d.694
ffffffff0015e998         5615e998       2d     1         lto.tmp:(.data.lk_version)
ffffffff0015e998         5615e998       2d     1                 lk_version
ffffffff0015e998         5615e998        0     1                 $d.698
ffffffff0015e9c8         5615e9c8       48     8         lto.tmp:(.data.apploader_ktipc_server)
ffffffff0015e9c8         5615e9c8       48     1                 apploader_ktipc_server
ffffffff0015e9c8         5615e9c8        0     1                 $d.699
ffffffff0015ea10         5615ea10       28     8         lto.tmp:(.data.rel.ro.apploader_service_port)
ffffffff0015ea10         5615ea10       28     1                 apploader_service_port
ffffffff0015ea10         5615ea10        0     1                 $d.700
ffffffff0015ea38         5615ea38       28     8         lto.tmp:(.data.rel.ro.apploader_service_ops)
ffffffff0015ea38         5615ea38       28     1                 apploader_service_ops
ffffffff0015ea38         5615ea38        0     1                 $d.701
ffffffff0015ea60         5615ea60       18     8         lto.tmp:(.data.rel.ro.apploader_service_port_acl)
ffffffff0015ea60         5615ea60       18     1                 apploader_service_port_acl
ffffffff0015ea60         5615ea60        0     1                 $d.702
ffffffff0015ea78         5615ea78        8     8         lto.tmp:(.data.apploader_service_uuids)
ffffffff0015ea78         5615ea78        8     1                 apploader_service_uuids
ffffffff0015ea78         5615ea78        0     1                 $d.703
ffffffff0015ea80         5615ea80       48     8         lto.tmp:(.data.generic_ta_ktipc_server)
ffffffff0015ea80         5615ea80       48     1                 generic_ta_ktipc_server
ffffffff0015ea80         5615ea80        0     1                 $d.706
ffffffff0015eac8         5615eac8       28     8         lto.tmp:(.data.rel.ro.generic_ta_service_port)
ffffffff0015eac8         5615eac8       28     1                 generic_ta_service_port
ffffffff0015eac8         5615eac8        0     1                 $d.707
ffffffff0015eaf0         5615eaf0       28     8         lto.tmp:(.data.rel.ro.generic_ta_service_ops)
ffffffff0015eaf0         5615eaf0       28     1                 generic_ta_service_ops
ffffffff0015eaf0         5615eaf0        0     1                 $d.708
ffffffff0015eb18         5615eb18       48     8         lto.tmp:(.data.hwrng_ktipc_server)
ffffffff0015eb18         5615eb18       48     1                 hwrng_ktipc_server
ffffffff0015eb18         5615eb18        0     1                 $d.711
ffffffff0015eb60         5615eb60       28     8         lto.tmp:(.data.rel.ro.hwrng_srv_port)
ffffffff0015eb60         5615eb60       28     1                 hwrng_srv_port
ffffffff0015eb60         5615eb60        0     1                 $d.712
ffffffff0015eb88         5615eb88       28     8         lto.tmp:(.data.rel.ro.hwrng_srv_ops)
ffffffff0015eb88         5615eb88       28     1                 hwrng_srv_ops
ffffffff0015eb88         5615eb88        0     1                 $d.713
ffffffff0015ebb0         5615ebb0       10     8         lto.tmp:(.data.hwrng_req_list)
ffffffff0015ebb0         5615ebb0       10     1                 hwrng_req_list
ffffffff0015ebb0         5615ebb0        0     1                 $d.715
ffffffff0015ebc0         5615ebc0       48     8         lto.tmp:(.data.smc_ktipc_server)
ffffffff0015ebc0         5615ebc0       48     1                 smc_ktipc_server
ffffffff0015ebc0         5615ebc0        0     1                 $d.719
ffffffff0015ec08         5615ec08       28     8         lto.tmp:(.data.rel.ro.smc_service_port)
ffffffff0015ec08         5615ec08       28     1                 smc_service_port
ffffffff0015ec08         5615ec08        0     1                 $d.720
ffffffff0015ec30         5615ec30       28     8         lto.tmp:(.data.rel.ro.smc_service_ops)
ffffffff0015ec30         5615ec30       28     1                 smc_service_ops
ffffffff0015ec30         5615ec30        0     1                 $d.721
ffffffff0015ec58         5615ec58        0     1 .devices
ffffffff0015ec58         5615ec58        0     1         __devices = .
ffffffff0015ec58         5615ec58        0     1         __devices_end = .
ffffffff0015ec58         5615ec58        0     1 .fake_post_data
ffffffff0015ec58         5615ec58        0     1         __data_end = .
ffffffff0015f000         5615f000     c110  4096 .bss
ffffffff0015f000         5615f000        0     1         __bss_start = .
ffffffff0015f000         5615f000     4000    16         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(start.o):(.bss.prebss.stack)
ffffffff0015f000         5615f000        0     1                 $d.4
ffffffff0015f000         5615f000        0     1                 __stack
ffffffff00163000         56163000        0     1                 __stack_end
ffffffff00163000         56163000     4000     8         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(start.o):(.bss.prebss.shadow_stack)
ffffffff00163000         56163000        0     1                 $d.5
ffffffff00163000         56163000        0     1                 __shadow_stack
ffffffff00167000         56167000       40    16         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(start.o):(.bss.prebss.sp_el1_bufs)
ffffffff00167000         56167000        0     1                 $d.6
ffffffff00167000         56167000        0     1                 sp_el1_bufs
ffffffff00167800         56167800      800  2048         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(start.o):(.bss.prebss.translation_table)
ffffffff00167800         56167800        0     1                 $d.7
ffffffff00167800         56167800        0     1                 tt_trampoline
ffffffff00168000         56168000        0     1         . = ALIGN ( 8 )
ffffffff00168000         56168000        0     1         __post_prebss_bss_start = .
ffffffff00168000         56168000       80     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stderr.o):(.bss.buf)
ffffffff00168000         56168000       80     1                 buf
ffffffff00168000         56168000        0     1                 $d.0
ffffffff00168080         56168080        8     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdin.o):(.bss.buf)
ffffffff00168080         56168080        8     1                 buf
ffffffff00168080         56168080        0     1                 $d.0
ffffffff00168088         56168088       80     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdout.o):(.bss.buf)
ffffffff00168088         56168088       80     1                 buf
ffffffff00168088         56168088        0     1                 $d.0
ffffffff00168108         56168108        1     4         /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/bin/../runtimes_ndk_cxx/libclang_rt.builtins-aarch64-android.a(cpu_model.c.o):(.bss.__aarch64_have_lse_atomics)
ffffffff00168108         56168108        0     1                 $d.3
ffffffff00168108         56168108        1     1                 __aarch64_have_lse_atomics
ffffffff0016810c         5616810c        1     4         lto.tmp:(.bss.no_console)
ffffffff0016810c         5616810c        1     1                 no_console
ffffffff0016810c         5616810c        0     1                 $d.476
ffffffff00168110         56168110        8     8         lto.tmp:(.bss.g_rings)
ffffffff00168110         56168110        8     1                 g_rings
ffffffff00168110         56168110        0     1                 $d.483
ffffffff00168118         56168118        8     8         lto.tmp:(.bss.g_job)
ffffffff00168118         56168118        8     1                 g_job
ffffffff00168118         56168118        0     1                 $d.484
ffffffff00168120         56168120        8     8         lto.tmp:(.bss.sram_base)
ffffffff00168120         56168120        8     1                 sram_base
ffffffff00168120         56168120        0     1                 $d.485
ffffffff00168128         56168128        1     1         lto.tmp:(.bss.caam_ready)
ffffffff00168128         56168128        1     1                 caam_ready
ffffffff00168128         56168128        0     1                 $d.486
ffffffff00168129         56168129        1     1         lto.tmp:(.bss.tee_ctrl_lcdif)
ffffffff00168129         56168129        1     1                 tee_ctrl_lcdif
ffffffff00168129         56168129        0     1                 $d.500
ffffffff0016812c         5616812c        4     4         lto.tmp:(.bss.last_tee_fb_addr)
ffffffff0016812c         5616812c        4     1                 last_tee_fb_addr
ffffffff0016812c         5616812c        0     1                 $d.501
ffffffff00168130         56168130        4     4         lto.tmp:(.bss.last_linux_fb_addr)
ffffffff00168130         56168130        4     1                 last_linux_fb_addr
ffffffff00168130         56168130        0     1                 $d.502
ffffffff00168134         56168134        4     4         lto.tmp:(.bss.secondaries_to_init)
ffffffff00168134         56168134        4     1                 secondaries_to_init
ffffffff00168134         56168134        0     1                 $d.509
ffffffff00168138         56168138       20     8         lto.tmp:(.bss.current_fpstate)
ffffffff00168138         56168138       20     1                 current_fpstate
ffffffff00168138         56168138        0     1                 $d.511
ffffffff00168158         56168158        1     4         lto.tmp:(.bss.arm64_mte_enabled)
ffffffff00168158         56168158        0     1                 $d.512
ffffffff00168158         56168158        1     1                 arm64_mte_enabled
ffffffff00168160         56168160       20    32         lto.tmp:(.bss.arm64_kernel_translation_table)
ffffffff00168160         56168160        0     1                 $d.513
ffffffff00168160         56168160       20     1                 arm64_kernel_translation_table
ffffffff00168180         56168180        8     8         lto.tmp:(.bss.gicd_lock)
ffffffff00168180         56168180        8     1                 gicd_lock
ffffffff00168180         56168180        0     1                 $d.514
ffffffff00168188         56168188        1     1         lto.tmp:(.bss.doorbell_enabled)
ffffffff00168188         56168188        1     1                 doorbell_enabled
ffffffff00168188         56168188        0     1                 $d.515
ffffffff00168190         56168190      800     8         lto.tmp:(.bss.int_handler_table_per_cpu)
ffffffff00168190         56168190      800     1                 int_handler_table_per_cpu
ffffffff00168190         56168190        0     1                 $d.516
ffffffff00168990         56168990        1     8         lto.tmp:(.bss.arm_gics.0)
ffffffff00168990         56168990        1     1                 arm_gics.0
ffffffff00168990         56168990        0     1                 $d.518
ffffffff00168998         56168998        1     8         lto.tmp:(.bss.arm_gics.2)
ffffffff00168998         56168998        1     1                 arm_gics.2
ffffffff00168998         56168998        0     1                 $d.519
ffffffff0016899c         5616899c        4     4         lto.tmp:(.bss.enabled_spi_mask.0)
ffffffff0016899c         5616899c        4     1                 enabled_spi_mask.0
ffffffff0016899c         5616899c        0     1                 $d.520
ffffffff001689a0         561689a0        4     4         lto.tmp:(.bss.enabled_spi_mask.1)
ffffffff001689a0         561689a0        4     1                 enabled_spi_mask.1
ffffffff001689a0         561689a0        0     1                 $d.521
ffffffff001689a4         561689a4        4     4         lto.tmp:(.bss.enabled_spi_mask.2)
ffffffff001689a4         561689a4        4     1                 enabled_spi_mask.2
ffffffff001689a4         561689a4        0     1                 $d.522
ffffffff001689a8         561689a8        4     4         lto.tmp:(.bss.enabled_spi_mask.3)
ffffffff001689a8         561689a8        4     1                 enabled_spi_mask.3
ffffffff001689a8         561689a8        0     1                 $d.523
ffffffff001689ac         561689ac       10     4         lto.tmp:(.bss.enabled_ppi_mask)
ffffffff001689ac         561689ac       10     1                 enabled_ppi_mask
ffffffff001689ac         561689ac        0     1                 $d.524
ffffffff001689c0         561689c0        8     8         lto.tmp:(.bss.t_callback)
ffffffff001689c0         561689c0        8     1                 t_callback
ffffffff001689c0         561689c0        0     1                 $d.525
ffffffff001689c8         561689c8        1     4         lto.tmp:(.bss.timer_irq)
ffffffff001689c8         561689c8        1     1                 timer_irq
ffffffff001689c8         561689c8        0     1                 $d.526
ffffffff001689d0         561689d0        c     8         lto.tmp:(.bss.ns_per_cntpct)
ffffffff001689d0         561689d0        c     1                 ns_per_cntpct
ffffffff001689d0         561689d0        0     1                 $d.527
ffffffff001689e0         561689e0        c     8         lto.tmp:(.bss.cntpct_per_ns)
ffffffff001689e0         561689e0        c     1                 cntpct_per_ns
ffffffff001689e0         561689e0        0     1                 $d.528
ffffffff001689f0         561689f0        c     8         lto.tmp:(.bss.ms_per_cntpct)
ffffffff001689f0         561689f0        c     1                 ms_per_cntpct
ffffffff001689f0         561689f0        0     1                 $d.529
ffffffff00168a00         56168a00       40     8         lto.tmp:(.bss.saved_state)
ffffffff00168a00         56168a00       40     1                 saved_state
ffffffff00168a00         56168a00        0     1                 $d.530
ffffffff00168a40         56168a40       10     8         lto.tmp:(.bss.thread_list)
ffffffff00168a40         56168a40       10     1                 thread_list
ffffffff00168a40         56168a40        0     1                 $d.531
ffffffff00168a50         56168a50      100     8         lto.tmp:(.bss.preempt_timer)
ffffffff00168a50         56168a50      100     1                 preempt_timer
ffffffff00168a50         56168a50        0     1                 $d.532
ffffffff00168b50         56168b50       10     8         lto.tmp:(.bss.dead_threads)
ffffffff00168b50         56168b50       10     1                 dead_threads
ffffffff00168b50         56168b50        0     1                 $d.533
ffffffff00168b60         56168b60       20     8         lto.tmp:(.bss.reaper_wait_queue)
ffffffff00168b60         56168b60       20     1                 reaper_wait_queue
ffffffff00168b60         56168b60        0     1                 $d.534
ffffffff00168b80         56168b80      200     8         lto.tmp:(.bss.run_queue)
ffffffff00168b80         56168b80      200     1                 run_queue
ffffffff00168b80         56168b80        0     1                 $d.535
ffffffff00168d80         56168d80      c40     8         lto.tmp:(.bss._idle_threads)
ffffffff00168d80         56168d80      c40     1                 _idle_threads
ffffffff00168d80         56168d80        0     1                 $d.536
ffffffff001699c0         561699c0       10     4         lto.tmp:(.bss.cpu_priority)
ffffffff001699c0         561699c0       10     1                 cpu_priority
ffffffff001699c0         561699c0        0     1                 $d.537
ffffffff001699d0         561699d0        4     4         lto.tmp:(.bss.run_queue_bitmap)
ffffffff001699d0         561699d0        4     1                 run_queue_bitmap
ffffffff001699d0         561699d0        0     1                 $d.538
ffffffff001699d8         561699d8        8     8         lto.tmp:(.bss.timer_lock)
ffffffff001699d8         561699d8        8     1                 timer_lock
ffffffff001699d8         561699d8        0     1                 $d.539
ffffffff001699e0         561699e0       80    32         lto.tmp:(.bss.timers)
ffffffff001699e0         561699e0       80     1                 timers
ffffffff001699e0         561699e0        0     1                 $d.540
ffffffff00169a60         56169a60        c    32         lto.tmp:(.bss.mp)
ffffffff00169a60         56169a60        c     1                 mp
ffffffff00169a60         56169a60        0     1                 $d.541
ffffffff00169a70         56169a70      140     8         lto.tmp:(.bss.thread_stats)
ffffffff00169a70         56169a70      140     1                 thread_stats
ffffffff00169a70         56169a70        0     1                 $d.542
ffffffff00169bb0         56169bb0       10     8         lto.tmp:(.bss.write_port_list)
ffffffff00169bb0         56169bb0       10     1                 write_port_list
ffffffff00169bb0         56169bb0        0     1                 $d.543
ffffffff00169bc0         56169bc0       20     8         lto.tmp:(.bss.active_asid_version)
ffffffff00169bc0         56169bc0       20     1                 active_asid_version
ffffffff00169bc0         56169bc0        0     1                 $d.544
ffffffff00169be0         56169be0       20     8         lto.tmp:(.bss.active_aspace)
ffffffff00169be0         56169be0       20     1                 active_aspace
ffffffff00169be0         56169be0        0     1                 $d.545
ffffffff00169c00         56169c00        8     8         lto.tmp:(.bss.last_asid)
ffffffff00169c00         56169c00        8     1                 last_asid
ffffffff00169c00         56169c00        0     1                 $d.546
ffffffff00169c08         56169c08        1     1         lto.tmp:(.bss.old_asid_active)
ffffffff00169c08         56169c08        1     1                 old_asid_active
ffffffff00169c08         56169c08        0     1                 $d.547
ffffffff00169c10         56169c10        8     8         lto.tmp:(.bss.aux_slock)
ffffffff00169c10         56169c10        8     1                 aux_slock
ffffffff00169c10         56169c10        0     1                 $d.550
ffffffff00169c18         56169c18        1     8         lto.tmp:(.bss.boot_alloc_start)
ffffffff00169c18         56169c18        1     1                 boot_alloc_start
ffffffff00169c18         56169c18        0     1                 $d.555
ffffffff00169c20         56169c20        8     8         lto.tmp:(.bss.thread_lock)
ffffffff00169c20         56169c20        8     1                 thread_lock
ffffffff00169c20         56169c20        0     1                 $d.561
ffffffff00169c28         56169c28       68     8         lto.tmp:(.bss.theheap)
ffffffff00169c28         56169c28       68     1                 theheap
ffffffff00169c28         56169c28        0     1                 $d.562
ffffffff00169c90         56169c90        8     8         lto.tmp:(.bss.print_spin_lock)
ffffffff00169c90         56169c90        8     1                 print_spin_lock
ffffffff00169c90         56169c90        0     1                 $d.563
ffffffff00169c98         56169c98     1000     1         lto.tmp:(.bss.early_log_buffer)
ffffffff00169c98         56169c98     1000     1                 early_log_buffer
ffffffff00169c98         56169c98        0     1                 $d.565
ffffffff0016ac98         5616ac98        4     4         lto.tmp:(.bss.print_saved_state)
ffffffff0016ac98         5616ac98        4     1                 print_saved_state
ffffffff0016ac98         5616ac98        0     1                 $d.571
ffffffff0016ac9c         5616ac9c        4     4         lto.tmp:(.bss.secondary_bootstrap_thread_count)
ffffffff0016ac9c         5616ac9c        4     1                 secondary_bootstrap_thread_count
ffffffff0016ac9c         5616ac9c        0     1                 $d.573
ffffffff0016aca0         5616aca0       18     8         lto.tmp:(.bss.secondary_bootstrap_threads)
ffffffff0016aca0         5616aca0       18     1                 secondary_bootstrap_threads
ffffffff0016aca0         5616aca0        0     1                 $d.574
ffffffff0016acb8         5616acb8       10     4         lto.tmp:(.bss.src_dst_ids)
ffffffff0016acb8         5616acb8       10     1                 src_dst_ids
ffffffff0016acb8         5616acb8        0     1                 $d.576
ffffffff0016acc8         5616acc8        1     1         lto.tmp:(.bss.arm_ffa_init_is_success)
ffffffff0016acc8         5616acc8        1     1                 arm_ffa_init_is_success
ffffffff0016acc8         5616acc8        0     1                 $d.577
ffffffff0016acd0         5616acd0        8     8         lto.tmp:(.bss.ffa_rx)
ffffffff0016acd0         5616acd0        8     1                 ffa_rx
ffffffff0016acd0         5616acd0        0     1                 $d.579
ffffffff0016acd8         5616acd8        1     4         lto.tmp:(.bss.supports_ns_bit)
ffffffff0016acd8         5616acd8        1     1                 supports_ns_bit
ffffffff0016acd8         5616acd8        0     1                 $d.580
ffffffff0016acd9         5616acd9        1     1         lto.tmp:(.bss.supports_rx_release)
ffffffff0016acd9         5616acd9        1     1                 supports_rx_release
ffffffff0016acd9         5616acd9        0     1                 $d.581
ffffffff0016acdc         5616acdc        2     4         lto.tmp:(.bss.ffa_local_id)
ffffffff0016acdc         5616acdc        2     1                 ffa_local_id
ffffffff0016acdc         5616acdc        0     1                 $d.582
ffffffff0016ace0         5616ace0        8     8         lto.tmp:(.bss.ffa_tx)
ffffffff0016ace0         5616ace0        8     1                 ffa_tx
ffffffff0016ace0         5616ace0        0     1                 $d.583
ffffffff0016ace8         5616ace8        8     8         lto.tmp:(.bss.ffa_buf_size)
ffffffff0016ace8         5616ace8        8     1                 ffa_buf_size
ffffffff0016ace8         5616ace8        0     1                 $d.584
ffffffff0016acf0         5616acf0        4     8         lto.tmp:(.bss._test_context.0)
ffffffff0016acf0         5616acf0        4     1                 _test_context.0
ffffffff0016acf0         5616acf0        0     1                 $d.595
ffffffff0016acf8         5616acf8        4     8         lto.tmp:(.bss._test_context.1)
ffffffff0016acf8         5616acf8        4     1                 _test_context.1
ffffffff0016acf8         5616acf8        0     1                 $d.596
ffffffff0016ad00         5616ad00        4     8         lto.tmp:(.bss._test_context.2)
ffffffff0016ad00         5616ad00        4     1                 _test_context.2
ffffffff0016ad00         5616ad00        0     1                 $d.597
ffffffff0016ad08         5616ad08        8     8         lto.tmp:(.bss._test_context.3)
ffffffff0016ad08         5616ad08        8     1                 _test_context.3
ffffffff0016ad08         5616ad08        0     1                 $d.598
ffffffff0016ad10         5616ad10        8     8         lto.tmp:(.bss._test_context.4)
ffffffff0016ad10         5616ad10        8     1                 _test_context.4
ffffffff0016ad10         5616ad10        0     1                 $d.599
ffffffff0016ad18         5616ad18        8     8         lto.tmp:(.bss._test_context.5)
ffffffff0016ad18         5616ad18        8     1                 _test_context.5
ffffffff0016ad18         5616ad18        0     1                 $d.600
ffffffff0016ad20         5616ad20        8     8         lto.tmp:(.bss._test_context.6)
ffffffff0016ad20         5616ad20        8     1                 _test_context.6
ffffffff0016ad20         5616ad20        0     1                 $d.601
ffffffff0016ad28         5616ad28        8     8         lto.tmp:(.bss._test_context.7)
ffffffff0016ad28         5616ad28        8     1                 _test_context.7
ffffffff0016ad28         5616ad28        0     1                 $d.602
ffffffff0016ad30         5616ad30        1     8         lto.tmp:(.bss._test_context.8)
ffffffff0016ad30         5616ad30        1     1                 _test_context.8
ffffffff0016ad30         5616ad30        0     1                 $d.603
ffffffff0016ad38         5616ad38        1     8         lto.tmp:(.bss._test_context.9)
ffffffff0016ad38         5616ad38        1     1                 _test_context.9
ffffffff0016ad38         5616ad38        0     1                 $d.604
ffffffff0016ad3c         5616ad3c       40     4         lto.tmp:(.bss.echo_buf)
ffffffff0016ad3c         5616ad3c       40     1                 echo_buf
ffffffff0016ad3c         5616ad3c        0     1                 $d.616
ffffffff0016ad7c         5616ad7c        4     4         lto.tmp:(.bss.close_counter)
ffffffff0016ad7c         5616ad7c        4     1                 close_counter
ffffffff0016ad7c         5616ad7c        0     1                 $d.617
ffffffff0016ad80         5616ad80        8     8         lto.tmp:(.bss.log_lock)
ffffffff0016ad80         5616ad80        8     1                 log_lock
ffffffff0016ad80         5616ad80        0     1                 $d.622
ffffffff0016ad88         5616ad88        4     4         lto.tmp:(.bss._uctx_slot_id)
ffffffff0016ad88         5616ad88        4     1                 _uctx_slot_id
ffffffff0016ad88         5616ad88        0     1                 $d.636
ffffffff0016ad8c         5616ad8c        1     1         lto.tmp:(.bss.apps_started)
ffffffff0016ad8c         5616ad8c        1     1                 apps_started
ffffffff0016ad8c         5616ad8c        0     1                 $d.641
ffffffff0016ad90         5616ad90        4     4         lto.tmp:(.bss.als_slot_cnt)
ffffffff0016ad90         5616ad90        4     1                 als_slot_cnt
ffffffff0016ad90         5616ad90        0     1                 $d.643
ffffffff0016ad98         5616ad98        8     8         lto.tmp:(.bss.g_apploader_chandle)
ffffffff0016ad98         5616ad98        8     1                 g_apploader_chandle
ffffffff0016ad98         5616ad98        0     1                 $d.644
ffffffff0016ada0         5616ada0        4     4         lto.tmp:(.bss.rctee_next_app_id)
ffffffff0016ada0         5616ada0        4     1                 rctee_next_app_id
ffffffff0016ada0         5616ada0        0     1                 $d.646
ffffffff0016ada8         5616ada8       78     8         lto.tmp:(.bss.g_conn_req_queue)
ffffffff0016ada8         5616ada8       78     1                 g_conn_req_queue
ffffffff0016ada8         5616ada8        0     1                 $d.654
ffffffff0016ae20         5616ae20        8     8         lto.tmp:(.bss.conn_req_thread)
ffffffff0016ae20         5616ae20        8     1                 conn_req_thread
ffffffff0016ae20         5616ae20        0     1                 $d.655
ffffffff0016ae28         5616ae28        4     4         lto.tmp:(.bss._dev_cnt)
ffffffff0016ae28         5616ae28        4     1                 _dev_cnt
ffffffff0016ae28         5616ae28        0     1                 $d.657
ffffffff0016ae30         5616ae30        8     8         lto.tmp:(.bss._dev_list_lock)
ffffffff0016ae30         5616ae30        8     1                 _dev_list_lock
ffffffff0016ae30         5616ae30        0     1                 $d.658
ffffffff0016ae38         5616ae38        8     8         lto.tmp:(.bss.sm_api_version_lock)
ffffffff0016ae38         5616ae38        8     1                 sm_api_version_lock
ffffffff0016ae38         5616ae38        0     1                 $d.664
ffffffff0016ae40         5616ae40        4     4         lto.tmp:(.bss.sm_api_version_min)
ffffffff0016ae40         5616ae40        4     1                 sm_api_version_min
ffffffff0016ae40         5616ae40        0     1                 $d.666
ffffffff0016ae44         5616ae44        4     4         lto.tmp:(.bss.sm_api_version)
ffffffff0016ae44         5616ae44        4     1                 sm_api_version
ffffffff0016ae44         5616ae44        0     1                 $d.667
ffffffff0016ae48         5616ae48        4     1         lto.tmp:(.bss.irq_thread_ready)
ffffffff0016ae48         5616ae48        4     1                 irq_thread_ready
ffffffff0016ae48         5616ae48        0     1                 $d.668
ffffffff0016ae50         5616ae50       c0     8         lto.tmp:(.bss.nsirqevent)
ffffffff0016ae50         5616ae50       c0     1                 nsirqevent
ffffffff0016ae50         5616ae50        0     1                 $d.669
ffffffff0016af10         5616af10        1     4         lto.tmp:(.bss.platform_halted)
ffffffff0016af10         5616af10        1     1                 platform_halted
ffffffff0016af10         5616af10        0     1                 $d.670
ffffffff0016af18         5616af18       20     8         lto.tmp:(.bss.nsirqthreads)
ffffffff0016af18         5616af18       20     1                 nsirqthreads
ffffffff0016af18         5616af18        0     1                 $d.671
ffffffff0016af38         5616af38        8     8         lto.tmp:(.bss.boot_args)
ffffffff0016af38         5616af38        8     1                 boot_args
ffffffff0016af38         5616af38        0     1                 $d.673
ffffffff0016af40         5616af40        4     4         lto.tmp:(.bss.boot_args_refcnt)
ffffffff0016af40         5616af40        4     1                 boot_args_refcnt
ffffffff0016af40         5616af40        0     1                 $d.674
ffffffff0016af48         5616af48        8     8         lto.tmp:(.bss.lk_boot_args.0)
ffffffff0016af48         5616af48        8     1                 lk_boot_args.0
ffffffff0016af48         5616af48        0     1                 $d.675
ffffffff0016af50         5616af50        8     8         lto.tmp:(.bss.lk_boot_args.1)
ffffffff0016af50         5616af50        8     1                 lk_boot_args.1
ffffffff0016af50         5616af50        0     1                 $d.676
ffffffff0016af58         5616af58        8     8         lto.tmp:(.bss.lk_boot_args.2)
ffffffff0016af58         5616af58        8     1                 lk_boot_args.2
ffffffff0016af58         5616af58        0     1                 $d.677
ffffffff0016af60         5616af60        8     8         lto.tmp:(.bss.lk_boot_args.3)
ffffffff0016af60         5616af60        8     1                 lk_boot_args.3
ffffffff0016af60         5616af60        0     1                 $d.678
ffffffff0016af68         5616af68       20     8         lto.tmp:(.bss.nsidlethreads)
ffffffff0016af68         5616af68       20     1                 nsidlethreads
ffffffff0016af68         5616af68        0     1                 $d.679
ffffffff0016af88         5616af88        8     8         lto.tmp:(.bss.stdcallthread)
ffffffff0016af88         5616af88        8     1                 stdcallthread
ffffffff0016af88         5616af88        0     1                 $d.680
ffffffff0016af90         5616af90        8     8         lto.tmp:(.bss.sched_shared_mem)
ffffffff0016af90         5616af90        8     1                 sched_shared_mem
ffffffff0016af90         5616af90        0     1                 $d.689
ffffffff0016af98         5616af98        8     8         lto.tmp:(.bss.sched_shared_datalock)
ffffffff0016af98         5616af98        8     1                 sched_shared_datalock
ffffffff0016af98         5616af98        0     1                 $d.690
ffffffff0016afa0         5616afa0       20     8         lto.tmp:(.bss.shareinfo)
ffffffff0016afa0         5616afa0       20     1                 shareinfo
ffffffff0016afa0         5616afa0        0     1                 $d.691
ffffffff0016afc0         5616afc0        8     8         lto.tmp:(.bss.ipc_printf_handle)
ffffffff0016afc0         5616afc0        8     1                 ipc_printf_handle
ffffffff0016afc0         5616afc0        0     1                 $d.695
ffffffff0016afc8         5616afc8        8     8         lto.tmp:(.bss.unittest_handle_set)
ffffffff0016afc8         5616afc8        8     1                 unittest_handle_set
ffffffff0016afc8         5616afc8        0     1                 $d.696
ffffffff0016afd0         5616afd0        8     8         lto.tmp:(.bss.unittest_thread)
ffffffff0016afd0         5616afd0        8     1                 unittest_thread
ffffffff0016afd0         5616afd0        0     1                 $d.697
ffffffff0016afd8         5616afd8       98     8         lto.tmp:(.bss._kernel_aspace)
ffffffff0016afd8         5616afd8       98     1                 _kernel_aspace
ffffffff0016afd8         5616afd8        0     1                 $d.705
ffffffff0016b070         5616b070       10     1         lto.tmp:(.bss.generic_ta_service_handle_get_property_string.version_str)
ffffffff0016b070         5616b070       10     1                 generic_ta_service_handle_get_property_string.version_str
ffffffff0016b070         5616b070        0     1                 $d.710
ffffffff0016b080         5616b080        8     8         lto.tmp:(.bss.rng_data_avail_count)
ffffffff0016b080         5616b080        8     1                 rng_data_avail_count
ffffffff0016b080         5616b080        0     1                 $d.716
ffffffff0016b088         5616b088       80     1         lto.tmp:(.bss.rng_data)
ffffffff0016b088         5616b088       80     1                 rng_data
ffffffff0016b088         5616b088        0     1                 $d.717
ffffffff0016b108         5616b108        8     8         lto.tmp:(.bss.rng_data_avail_pos)
ffffffff0016b108         5616b108        8     1                 rng_data_avail_pos
ffffffff0016b108         5616b108        0     1                 $d.718
ffffffff0016b110         5616b110        0     1         . = ALIGN ( 8 )
ffffffff0016b110         5616b110        0     1         __bss_end = .
ffffffff0016b110         5616b110      ef0     1 . = ALIGN ( 4096 )
ffffffff0016c000         5616c000        0     1 _end = .
ffffffff0016c000         5616c000  1e94000     1 . = 0xFFFFFFFF00000000 + 0x2000000
ffffffff02000000         58000000        0     1 _end_of_ram = .
               0                0    5a4fa     1 .debug_info
               0                0      1d7     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(asm.o):(.debug_info)
               0                0        0     1                 $d.3
             1d7              1d7      451     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(exceptions.o):(.debug_info)
             1d7              1d7        0     1                 $d.3
             628              628      167     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(spinlock.o):(.debug_info)
             628              628        0     1                 $d.3
             78f              78f      149     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(start.o):(.debug_info)
             78f              78f        0     1                 $d.11
             8d8              8d8      1b2     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(cache-ops.o):(.debug_info)
             8d8              8d8        0     1                 $d.3
             a8a              a8a      172     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(usercopy.o):(.debug_info)
             a8a              a8a        0     1                 $d.9
             bfc              bfc      145     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(safecopy.o):(.debug_info)
             bfc              bfc        0     1                 $d.9
             d41              d41       57     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(abort.o):(.debug_info)
             d41              d41        0     1                 $d.3
             d98              d98       6d     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(close.o):(.debug_info)
             d98              d98        0     1                 $d.4
             e05              e05       5a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(fflush.o):(.debug_info)
             e05              e05        0     1                 $d.3
             e5f              e5f      572     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(libc_state.o):(.debug_info)
             e5f              e5f        0     1                 $d.7
            13d1             13d1      22a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(writev.o):(.debug_info)
            13d1             13d1        0     1                 $d.3
            15fb             15fb      20f     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(atoi.o):(.debug_info)
            15fb             15fb        0     1                 $d.8
            180a             180a       75     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(eabi.o):(.debug_info)
            180a             180a        0     1                 $d.4
            187f             187f       34     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(eabi_unwind_stubs.o):(.debug_info)
            187f             187f        0     1                 $d.2
            18b3             18b3      161     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(io_handle.o):(.debug_info)
            18b3             18b3        0     1                 $d.4
            1a14             1a14      a5e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(printf.o):(.debug_info)
            1a14             1a14        0     1                 $d.16
            2472             2472       d3     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(rand.o):(.debug_info)
            2472             2472        0     1                 $d.6
            2545             2545      a30     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdio.o):(.debug_info)
            2545             2545        0     1                 $d.15
            2f75             2f75       e5     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtol.o):(.debug_info)
            2f75             2f75        0     1                 $d.3
            305a             305a       e5     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtoll.o):(.debug_info)
            305a             305a        0     1                 $d.3
            313f             313f      1a4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(locale_stubs.o):(.debug_info)
            313f             313f        0     1                 $d.8
            32e3             32e3       50     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(atexit.o):(.debug_info)
            32e3             32e3        0     1                 $d.3
            3333             3333      2f1     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(pthreads.o):(.debug_info)
            3333             3333        0     1                 $d.14
            3624             3624       a7     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isalnum.o):(.debug_info)
            3624             3624        0     1                 $d.4
            36cb             36cb       a7     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isalpha.o):(.debug_info)
            36cb             36cb        0     1                 $d.4
            3772             3772       4a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isascii.o):(.debug_info)
            3772             3772        0     1                 $d.3
            37bc             37bc       a3     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isblank.o):(.debug_info)
            37bc             37bc        0     1                 $d.4
            385f             385f       a7     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(iscntrl.o):(.debug_info)
            385f             385f        0     1                 $d.4
            3906             3906       87     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isdigit.o):(.debug_info)
            3906             3906        0     1                 $d.4
            398d             398d       a7     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isgraph.o):(.debug_info)
            398d             398d        0     1                 $d.4
            3a34             3a34       a7     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(islower.o):(.debug_info)
            3a34             3a34        0     1                 $d.4
            3adb             3adb       a7     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isprint.o):(.debug_info)
            3adb             3adb        0     1                 $d.4
            3b82             3b82       a6     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(ispunct.o):(.debug_info)
            3b82             3b82        0     1                 $d.4
            3c28             3c28       a7     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isspace.o):(.debug_info)
            3c28             3c28        0     1                 $d.4
            3ccf             3ccf       a7     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isupper.o):(.debug_info)
            3ccf             3ccf        0     1                 $d.4
            3d76             3d76       a7     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isxdigit.o):(.debug_info)
            3d76             3d76        0     1                 $d.4
            3e1d             3e1d       4a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(toascii.o):(.debug_info)
            3e1d             3e1d        0     1                 $d.3
            3e67             3e67       a7     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(tolower.o):(.debug_info)
            3e67             3e67        0     1                 $d.4
            3f0e             3f0e       a7     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(toupper.o):(.debug_info)
            3f0e             3f0e        0     1                 $d.4
            3fb5             3fb5       e7     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(c_locale.o):(.debug_info)
            3fb5             3fb5        0     1                 $d.5
            409c             409c       4a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(abs.o):(.debug_info)
            409c             409c        0     1                 $d.3
            40e6             40e6       c2     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bsearch.o):(.debug_info)
            40e6             40e6        0     1                 $d.3
            41a8             41a8       73     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(div.o):(.debug_info)
            41a8             41a8        0     1                 $d.3
            421b             421b       52     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(imaxabs.o):(.debug_info)
            421b             421b        0     1                 $d.3
            426d             426d       7a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(imaxdiv.o):(.debug_info)
            426d             426d        0     1                 $d.3
            42e7             42e7       4a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(labs.o):(.debug_info)
            42e7             42e7        0     1                 $d.3
            4331             4331       72     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(ldiv.o):(.debug_info)
            4331             4331        0     1                 $d.3
            43a3             43a3       4a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(llabs.o):(.debug_info)
            43a3             43a3        0     1                 $d.3
            43ed             43ed       72     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(lldiv.o):(.debug_info)
            43ed             43ed        0     1                 $d.3
            445f             445f      7a1     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(qsort.o):(.debug_info)
            445f             445f        0     1                 $d.5
            4c00             4c00       6d     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bcmp.o):(.debug_info)
            4c00             4c00        0     1                 $d.2
            4c6d             4c6d       ed     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memccpy.o):(.debug_info)
            4c6d             4c6d        0     1                 $d.3
            4d5a             4d5a      289     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memmem.o):(.debug_info)
            4d5a             4d5a        0     1                 $d.5
            4fe3             4fe3       74     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(mempcpy.o):(.debug_info)
            4fe3             4fe3        0     1                 $d.3
            5057             5057       8d     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memrchr.o):(.debug_info)
            5057             5057        0     1                 $d.3
            50e4             50e4       b0     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stpcpy.o):(.debug_info)
            50e4             50e4        0     1                 $d.3
            5194             5194       be     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stpncpy.o):(.debug_info)
            5194             5194        0     1                 $d.3
            5252             5252      104     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcasecmp.o):(.debug_info)
            5252             5252        0     1                 $d.4
            5356             5356       ac     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcasestr.o):(.debug_info)
            5356             5356        0     1                 $d.3
            5402             5402       a8     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strchrnul.o):(.debug_info)
            5402             5402        0     1                 $d.3
            54aa             54aa       95     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcspn.o):(.debug_info)
            54aa             54aa        0     1                 $d.3
            553f             553f       83     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strerror_r.o):(.debug_info)
            553f             553f        0     1                 $d.3
            55c2             55c2      12d     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncasecmp.o):(.debug_info)
            55c2             55c2        0     1                 $d.4
            56ef             56ef       a8     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strndup.o):(.debug_info)
            56ef             56ef        0     1                 $d.3
            5797             5797       79     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strsep.o):(.debug_info)
            5797             5797        0     1                 $d.3
            5810             5810       7f     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtok_r.o):(.debug_info)
            5810             5810        0     1                 $d.3
            588f             588f       cd     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strverscmp.o):(.debug_info)
            588f             588f        0     1                 $d.3
            595c             595c       98     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(swab.o):(.debug_info)
            595c             595c        0     1                 $d.3
            59f4             59f4      23c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stderr.o):(.debug_info)
            59f4             59f4        0     1                 $d.5
            5c30             5c30      23c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdin.o):(.debug_info)
            5c30             5c30        0     1                 $d.5
            5e6c             5e6c      23c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdout.o):(.debug_info)
            5e6c             5e6c        0     1                 $d.5
            60a8             60a8      23a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_close.o):(.debug_info)
            60a8             60a8        0     1                 $d.4
            62e2             62e2      22c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_read.o):(.debug_info)
            62e2             62e2        0     1                 $d.3
            650e             650e      2d8     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_write.o):(.debug_info)
            650e             650e        0     1                 $d.3
            67e6             67e6      22c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_seek.o):(.debug_info)
            67e6             67e6        0     1                 $d.3
            6a12             6a12       bb     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__ctype_get_mb_cur_max.o):(.debug_info)
            6a12             6a12        0     1                 $d.2
            6acd             6acd       50     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(internal.o):(.debug_info)
            6acd             6acd        0     1                 $d.2
            6b1d             6b1d      13d     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(mbtowc.o):(.debug_info)
            6b1d             6b1d        0     1                 $d.3
            6c5a             6c5a      11f     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(wcrtomb.o):(.debug_info)
            6c5a             6c5a        0     1                 $d.3
            6d79             6d79       68     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bcopy.o):(.debug_info)
            6d79             6d79        0     1                 $d.3
            6de1             6de1       59     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bzero.o):(.debug_info)
            6de1             6de1        0     1                 $d.3
            6e3a             6e3a       9a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memchr.o):(.debug_info)
            6e3a             6e3a        0     1                 $d.3
            6ed4             6ed4       99     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memcmp.o):(.debug_info)
            6ed4             6ed4        0     1                 $d.3
            6f6d             6f6d       b8     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memcpy.o):(.debug_info)
            6f6d             6f6d        0     1                 $d.3
            7025             7025       b8     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memmove.o):(.debug_info)
            7025             7025        0     1                 $d.3
            70dd             70dd       ba     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memset.o):(.debug_info)
            70dd             70dd        0     1                 $d.3
            7197             7197       6c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcat.o):(.debug_info)
            7197             7197        0     1                 $d.3
            7203             7203       67     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strchr.o):(.debug_info)
            7203             7203        0     1                 $d.3
            726a             726a       8a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcmp.o):(.debug_info)
            726a             726a        0     1                 $d.3
            72f4             72f4       5f     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcoll.o):(.debug_info)
            72f4             72f4        0     1                 $d.2
            7353             7353       6c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcpy.o):(.debug_info)
            7353             7353        0     1                 $d.3
            73bf             73bf       78     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strdup.o):(.debug_info)
            73bf             73bf        0     1                 $d.3
            7437             7437       88     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strerror.o):(.debug_info)
            7437             7437        0     1                 $d.4
            74bf             74bf       b1     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strlcat.o):(.debug_info)
            74bf             74bf        0     1                 $d.3
            7570             7570       80     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strlcpy.o):(.debug_info)
            7570             7570        0     1                 $d.3
            75f0             75f0       69     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strlen.o):(.debug_info)
            75f0             75f0        0     1                 $d.3
            7659             7659       81     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncat.o):(.debug_info)
            7659             7659        0     1                 $d.3
            76da             76da       81     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncpy.o):(.debug_info)
            76da             76da        0     1                 $d.3
            775b             775b       9f     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncmp.o):(.debug_info)
            775b             775b        0     1                 $d.3
            77fa             77fa       8c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strnicmp.o):(.debug_info)
            77fa             77fa        0     1                 $d.3
            7886             7886       73     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strnlen.o):(.debug_info)
            7886             7886        0     1                 $d.3
            78f9             78f9       74     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strpbrk.o):(.debug_info)
            78f9             78f9        0     1                 $d.3
            796d             796d       70     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strrchr.o):(.debug_info)
            796d             796d        0     1                 $d.3
            79dd             79dd       84     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strspn.o):(.debug_info)
            79dd             79dd        0     1                 $d.3
            7a61             7a61       78     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strstr.o):(.debug_info)
            7a61             7a61        0     1                 $d.3
            7ad9             7ad9       7f     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtok.o):(.debug_info)
            7ad9             7ad9        0     1                 $d.4
            7b58             7b58       90     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strxfrm.o):(.debug_info)
            7b58             7b58        0     1                 $d.3
            7be8             7be8       a1     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(pure_virtual.o):(.debug_info)
            7be8             7be8        0     1                 $d.5
            7c89             7c89      152     1         out/build-imx8mp/kernel/rctee/lib/sm.mod.a(entry.o):(.debug_info)
            7c89             7c89        0     1                 $d.4
            7ddb             7ddb      11e     1         out/build-imx8mp/kernel/rctee/lib/smc.mod.a(smc.o):(.debug_info)
            7ddb             7ddb        0     1                 $d.3
            7ef9             7ef9      12f     1         out/build-imx8mp/kernel/rctee/lib/syscall.mod.a(syscall.o):(.debug_info)
            7ef9             7ef9        0     1                 $d.3
            8028             8028    524d2     1         lto.tmp:(.debug_info)
            8028             8028        0     1                 $d.727
               0                0     5bd1     1 .debug_abbrev
               0                0       21     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(asm.o):(.debug_abbrev)
               0                0        0     1                 $d.2
              21               21       21     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(exceptions.o):(.debug_abbrev)
              21               21        0     1                 $d.2
              42               42       21     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(spinlock.o):(.debug_abbrev)
              42               42        0     1                 $d.2
              63               63       1f     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(start.o):(.debug_abbrev)
              63               63        0     1                 $d.10
              82               82       21     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(cache-ops.o):(.debug_abbrev)
              82               82        0     1                 $d.2
              a3               a3       21     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(usercopy.o):(.debug_abbrev)
              a3               a3        0     1                 $d.8
              c4               c4       21     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(safecopy.o):(.debug_abbrev)
              c4               c4        0     1                 $d.8
              e5               e5       77     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(abort.o):(.debug_abbrev)
              e5               e5        0     1                 $d.2
             15c              15c       88     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(close.o):(.debug_abbrev)
             15c              15c        0     1                 $d.3
             1e4              1e4       79     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(fflush.o):(.debug_abbrev)
             1e4              1e4        0     1                 $d.2
             25d              25d      226     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(libc_state.o):(.debug_abbrev)
             25d              25d        0     1                 $d.6
             483              483      146     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(writev.o):(.debug_abbrev)
             483              483        0     1                 $d.2
             5c9              5c9      123     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(atoi.o):(.debug_abbrev)
             5c9              5c9        0     1                 $d.7
             6ec              6ec       96     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(eabi.o):(.debug_abbrev)
             6ec              6ec        0     1                 $d.3
             782              782       3d     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(eabi_unwind_stubs.o):(.debug_abbrev)
             782              782        0     1                 $d.1
             7bf              7bf       c2     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(io_handle.o):(.debug_abbrev)
             7bf              7bf        0     1                 $d.3
             881              881      302     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(printf.o):(.debug_abbrev)
             881              881        0     1                 $d.15
             b83              b83       de     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(rand.o):(.debug_abbrev)
             b83              b83        0     1                 $d.5
             c61              c61      21d     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdio.o):(.debug_abbrev)
             c61              c61        0     1                 $d.14
             e7e              e7e       b2     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtol.o):(.debug_abbrev)
             e7e              e7e        0     1                 $d.2
             f30              f30       b2     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtoll.o):(.debug_abbrev)
             f30              f30        0     1                 $d.2
             fe2              fe2      117     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(locale_stubs.o):(.debug_abbrev)
             fe2              fe2        0     1                 $d.7
            10f9             10f9       6a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(atexit.o):(.debug_abbrev)
            10f9             10f9        0     1                 $d.2
            1163             1163      143     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(pthreads.o):(.debug_abbrev)
            1163             1163        0     1                 $d.13
            12a6             12a6       d4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isalnum.o):(.debug_abbrev)
            12a6             12a6        0     1                 $d.3
            137a             137a       d4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isalpha.o):(.debug_abbrev)
            137a             137a        0     1                 $d.3
            144e             144e       5c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isascii.o):(.debug_abbrev)
            144e             144e        0     1                 $d.2
            14aa             14aa       d4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isblank.o):(.debug_abbrev)
            14aa             14aa        0     1                 $d.3
            157e             157e       d4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(iscntrl.o):(.debug_abbrev)
            157e             157e        0     1                 $d.3
            1652             1652       88     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isdigit.o):(.debug_abbrev)
            1652             1652        0     1                 $d.3
            16da             16da       d4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isgraph.o):(.debug_abbrev)
            16da             16da        0     1                 $d.3
            17ae             17ae       d4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(islower.o):(.debug_abbrev)
            17ae             17ae        0     1                 $d.3
            1882             1882       d4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isprint.o):(.debug_abbrev)
            1882             1882        0     1                 $d.3
            1956             1956       c5     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(ispunct.o):(.debug_abbrev)
            1956             1956        0     1                 $d.3
            1a1b             1a1b       d4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isspace.o):(.debug_abbrev)
            1a1b             1a1b        0     1                 $d.3
            1aef             1aef       d4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isupper.o):(.debug_abbrev)
            1aef             1aef        0     1                 $d.3
            1bc3             1bc3       d4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isxdigit.o):(.debug_abbrev)
            1bc3             1bc3        0     1                 $d.3
            1c97             1c97       5c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(toascii.o):(.debug_abbrev)
            1c97             1c97        0     1                 $d.2
            1cf3             1cf3       d4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(tolower.o):(.debug_abbrev)
            1cf3             1cf3        0     1                 $d.3
            1dc7             1dc7       d4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(toupper.o):(.debug_abbrev)
            1dc7             1dc7        0     1                 $d.3
            1e9b             1e9b       9e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(c_locale.o):(.debug_abbrev)
            1e9b             1e9b        0     1                 $d.4
            1f39             1f39       5c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(abs.o):(.debug_abbrev)
            1f39             1f39        0     1                 $d.2
            1f95             1f95       a9     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bsearch.o):(.debug_abbrev)
            1f95             1f95        0     1                 $d.2
            203e             203e       92     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(div.o):(.debug_abbrev)
            203e             203e        0     1                 $d.2
            20d0             20d0       69     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(imaxabs.o):(.debug_abbrev)
            20d0             20d0        0     1                 $d.2
            2139             2139       83     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(imaxdiv.o):(.debug_abbrev)
            2139             2139        0     1                 $d.2
            21bc             21bc       5c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(labs.o):(.debug_abbrev)
            21bc             21bc        0     1                 $d.2
            2218             2218       83     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(ldiv.o):(.debug_abbrev)
            2218             2218        0     1                 $d.2
            229b             229b       5c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(llabs.o):(.debug_abbrev)
            229b             229b        0     1                 $d.2
            22f7             22f7       83     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(lldiv.o):(.debug_abbrev)
            22f7             22f7        0     1                 $d.2
            237a             237a      1bf     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(qsort.o):(.debug_abbrev)
            237a             237a        0     1                 $d.4
            2539             2539       72     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bcmp.o):(.debug_abbrev)
            2539             2539        0     1                 $d.1
            25ab             25ab       ad     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memccpy.o):(.debug_abbrev)
            25ab             25ab        0     1                 $d.2
            2658             2658      188     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memmem.o):(.debug_abbrev)
            2658             2658        0     1                 $d.4
            27e0             27e0       77     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(mempcpy.o):(.debug_abbrev)
            27e0             27e0        0     1                 $d.2
            2857             2857       9f     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memrchr.o):(.debug_abbrev)
            2857             2857        0     1                 $d.2
            28f6             28f6       97     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stpcpy.o):(.debug_abbrev)
            28f6             28f6        0     1                 $d.2
            298d             298d       a4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stpncpy.o):(.debug_abbrev)
            298d             298d        0     1                 $d.2
            2a31             2a31       e7     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcasecmp.o):(.debug_abbrev)
            2a31             2a31        0     1                 $d.3
            2b18             2b18       b2     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcasestr.o):(.debug_abbrev)
            2b18             2b18        0     1                 $d.2
            2bca             2bca       88     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strchrnul.o):(.debug_abbrev)
            2bca             2bca        0     1                 $d.2
            2c52             2c52       ab     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcspn.o):(.debug_abbrev)
            2c52             2c52        0     1                 $d.2
            2cfd             2cfd       7c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strerror_r.o):(.debug_abbrev)
            2cfd             2cfd        0     1                 $d.2
            2d79             2d79       f4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncasecmp.o):(.debug_abbrev)
            2d79             2d79        0     1                 $d.3
            2e6d             2e6d       b2     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strndup.o):(.debug_abbrev)
            2e6d             2e6d        0     1                 $d.2
            2f1f             2f1f       76     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strsep.o):(.debug_abbrev)
            2f1f             2f1f        0     1                 $d.2
            2f95             2f95       6e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtok_r.o):(.debug_abbrev)
            2f95             2f95        0     1                 $d.2
            3003             3003       b2     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strverscmp.o):(.debug_abbrev)
            3003             3003        0     1                 $d.2
            30b5             30b5       b3     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(swab.o):(.debug_abbrev)
            30b5             30b5        0     1                 $d.2
            3168             3168       cb     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stderr.o):(.debug_abbrev)
            3168             3168        0     1                 $d.4
            3233             3233       cb     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdin.o):(.debug_abbrev)
            3233             3233        0     1                 $d.4
            32fe             32fe       cb     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdout.o):(.debug_abbrev)
            32fe             32fe        0     1                 $d.4
            33c9             33c9       f3     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_close.o):(.debug_abbrev)
            33c9             33c9        0     1                 $d.3
            34bc             34bc       d4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_read.o):(.debug_abbrev)
            34bc             34bc        0     1                 $d.2
            3590             3590      13f     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_write.o):(.debug_abbrev)
            3590             3590        0     1                 $d.2
            36cf             36cf       d4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_seek.o):(.debug_abbrev)
            36cf             36cf        0     1                 $d.2
            37a3             37a3       ae     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__ctype_get_mb_cur_max.o):(.debug_abbrev)
            37a3             37a3        0     1                 $d.1
            3851             3851       67     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(internal.o):(.debug_abbrev)
            3851             3851        0     1                 $d.1
            38b8             38b8       ed     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(mbtowc.o):(.debug_abbrev)
            38b8             38b8        0     1                 $d.2
            39a5             39a5       e0     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(wcrtomb.o):(.debug_abbrev)
            39a5             39a5        0     1                 $d.2
            3a85             3a85       87     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bcopy.o):(.debug_abbrev)
            3a85             3a85        0     1                 $d.2
            3b0c             3b0c       7b     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bzero.o):(.debug_abbrev)
            3b0c             3b0c        0     1                 $d.2
            3b87             3b87       ae     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memchr.o):(.debug_abbrev)
            3b87             3b87        0     1                 $d.2
            3c35             3c35       a9     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memcmp.o):(.debug_abbrev)
            3c35             3c35        0     1                 $d.2
            3cde             3cde       9f     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memcpy.o):(.debug_abbrev)
            3cde             3cde        0     1                 $d.2
            3d7d             3d7d       9f     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memmove.o):(.debug_abbrev)
            3d7d             3d7d        0     1                 $d.2
            3e1c             3e1c       a2     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memset.o):(.debug_abbrev)
            3e1c             3e1c        0     1                 $d.2
            3ebe             3ebe       79     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcat.o):(.debug_abbrev)
            3ebe             3ebe        0     1                 $d.2
            3f37             3f37       79     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strchr.o):(.debug_abbrev)
            3f37             3f37        0     1                 $d.2
            3fb0             3fb0       79     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcmp.o):(.debug_abbrev)
            3fb0             3fb0        0     1                 $d.2
            4029             4029       67     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcoll.o):(.debug_abbrev)
            4029             4029        0     1                 $d.1
            4090             4090       79     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcpy.o):(.debug_abbrev)
            4090             4090        0     1                 $d.2
            4109             4109       88     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strdup.o):(.debug_abbrev)
            4109             4109        0     1                 $d.2
            4191             4191       92     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strerror.o):(.debug_abbrev)
            4191             4191        0     1                 $d.3
            4223             4223       b2     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strlcat.o):(.debug_abbrev)
            4223             4223        0     1                 $d.2
            42d5             42d5       83     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strlcpy.o):(.debug_abbrev)
            42d5             42d5        0     1                 $d.2
            4358             4358       86     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strlen.o):(.debug_abbrev)
            4358             4358        0     1                 $d.2
            43de             43de       86     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncat.o):(.debug_abbrev)
            43de             43de        0     1                 $d.2
            4464             4464       86     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncpy.o):(.debug_abbrev)
            4464             4464        0     1                 $d.2
            44ea             44ea       86     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncmp.o):(.debug_abbrev)
            44ea             44ea        0     1                 $d.2
            4570             4570       83     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strnicmp.o):(.debug_abbrev)
            4570             4570        0     1                 $d.2
            45f3             45f3       95     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strnlen.o):(.debug_abbrev)
            45f3             45f3        0     1                 $d.2
            4688             4688       79     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strpbrk.o):(.debug_abbrev)
            4688             4688        0     1                 $d.2
            4701             4701       88     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strrchr.o):(.debug_abbrev)
            4701             4701        0     1                 $d.2
            4789             4789       86     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strspn.o):(.debug_abbrev)
            4789             4789        0     1                 $d.2
            480f             480f       76     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strstr.o):(.debug_abbrev)
            480f             480f        0     1                 $d.2
            4885             4885       85     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtok.o):(.debug_abbrev)
            4885             4885        0     1                 $d.3
            490a             490a       8c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strxfrm.o):(.debug_abbrev)
            490a             490a        0     1                 $d.2
            4996             4996       ca     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(pure_virtual.o):(.debug_abbrev)
            4996             4996        0     1                 $d.4
            4a60             4a60       21     1         out/build-imx8mp/kernel/rctee/lib/sm.mod.a(entry.o):(.debug_abbrev)
            4a60             4a60        0     1                 $d.3
            4a81             4a81       21     1         out/build-imx8mp/kernel/rctee/lib/smc.mod.a(smc.o):(.debug_abbrev)
            4a81             4a81        0     1                 $d.2
            4aa2             4aa2       21     1         out/build-imx8mp/kernel/rctee/lib/syscall.mod.a(syscall.o):(.debug_abbrev)
            4aa2             4aa2        0     1                 $d.2
            4ac3             4ac3     110e     1         lto.tmp:(.debug_abbrev)
            4ac3             4ac3        0     1                 $d.726
               0                0      1f0     1 .debug_aranges
               0                0       30     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(asm.o):(.debug_aranges)
               0                0        0     1                 $d.1
              30               30       30     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(exceptions.o):(.debug_aranges)
              30               30        0     1                 $d.1
              60               60       30     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(spinlock.o):(.debug_aranges)
              60               60        0     1                 $d.1
              90               90       40     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(start.o):(.debug_aranges)
              90               90        0     1                 $d.8
              d0               d0       30     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(cache-ops.o):(.debug_aranges)
              d0               d0        0     1                 $d.1
             100              100       30     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(usercopy.o):(.debug_aranges)
             100              100        0     1                 $d.7
             130              130       30     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(safecopy.o):(.debug_aranges)
             130              130        0     1                 $d.7
             160              160       30     1         out/build-imx8mp/kernel/rctee/lib/sm.mod.a(entry.o):(.debug_aranges)
             160              160        0     1                 $d.2
             190              190       30     1         out/build-imx8mp/kernel/rctee/lib/smc.mod.a(smc.o):(.debug_aranges)
             190              190        0     1                 $d.1
             1c0              1c0       30     1         out/build-imx8mp/kernel/rctee/lib/syscall.mod.a(syscall.o):(.debug_aranges)
             1c0              1c0        0     1                 $d.1
               0                0    4a8c0     1 .debug_line
               0                0       8d     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(asm.o):(.debug_line)
               0                0        0     1                 $d.4
              8d               8d      271     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(exceptions.o):(.debug_line)
              8d               8d        0     1                 $d.4
             2fe              2fe       55     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(spinlock.o):(.debug_line)
             2fe              2fe        0     1                 $d.4
             353              353      175     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(start.o):(.debug_line)
             353              353        0     1                 $d.12
             4c8              4c8       6c     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(cache-ops.o):(.debug_line)
             4c8              4c8        0     1                 $d.4
             534              534       66     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(usercopy.o):(.debug_line)
             534              534        0     1                 $d.10
             59a              59a       59     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(safecopy.o):(.debug_line)
             59a              59a        0     1                 $d.10
             5f3              5f3      14e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(abort.o):(.debug_line)
             5f3              5f3        0     1                 $d.10
             741              741       f2     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(close.o):(.debug_line)
             741              741        0     1                 $d.11
             833              833      103     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(fflush.o):(.debug_line)
             833              833        0     1                 $d.10
             936              936      574     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(libc_state.o):(.debug_line)
             936              936        0     1                 $d.15
             eaa              eaa      31e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(writev.o):(.debug_line)
             eaa              eaa        0     1                 $d.10
            11c8             11c8      647     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(atoi.o):(.debug_line)
            11c8             11c8        0     1                 $d.16
            180f             180f       8e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(eabi.o):(.debug_line)
            180f             180f        0     1                 $d.11
            189d             189d       ef     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(eabi_unwind_stubs.o):(.debug_line)
            189d             189d        0     1                 $d.9
            198c             198c      26e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(io_handle.o):(.debug_line)
            198c             198c        0     1                 $d.12
            1bfa             1bfa      8b3     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(printf.o):(.debug_line)
            1bfa             1bfa        0     1                 $d.24
            24ad             24ad      22d     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(rand.o):(.debug_line)
            24ad             24ad        0     1                 $d.14
            26da             26da      3ba     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdio.o):(.debug_line)
            26da             26da        0     1                 $d.23
            2a94             2a94      28e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtol.o):(.debug_line)
            2a94             2a94        0     1                 $d.10
            2d22             2d22      329     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtoll.o):(.debug_line)
            2d22             2d22        0     1                 $d.10
            304b             304b      263     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(locale_stubs.o):(.debug_line)
            304b             304b        0     1                 $d.16
            32ae             32ae       8e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(atexit.o):(.debug_line)
            32ae             32ae        0     1                 $d.10
            333c             333c      2ea     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(pthreads.o):(.debug_line)
            333c             333c        0     1                 $d.22
            3626             3626      122     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isalnum.o):(.debug_line)
            3626             3626        0     1                 $d.12
            3748             3748      11e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isalpha.o):(.debug_line)
            3748             3748        0     1                 $d.12
            3866             3866       f4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isascii.o):(.debug_line)
            3866             3866        0     1                 $d.10
            395a             395a      116     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isblank.o):(.debug_line)
            395a             395a        0     1                 $d.12
            3a70             3a70      116     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(iscntrl.o):(.debug_line)
            3a70             3a70        0     1                 $d.12
            3b86             3b86      114     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isdigit.o):(.debug_line)
            3b86             3b86        0     1                 $d.12
            3c9a             3c9a      118     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isgraph.o):(.debug_line)
            3c9a             3c9a        0     1                 $d.12
            3db2             3db2      118     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(islower.o):(.debug_line)
            3db2             3db2        0     1                 $d.12
            3eca             3eca      118     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isprint.o):(.debug_line)
            3eca             3eca        0     1                 $d.12
            3fe2             3fe2      12d     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(ispunct.o):(.debug_line)
            3fe2             3fe2        0     1                 $d.12
            410f             410f      11e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isspace.o):(.debug_line)
            410f             410f        0     1                 $d.12
            422d             422d      118     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isupper.o):(.debug_line)
            422d             422d        0     1                 $d.12
            4345             4345      122     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isxdigit.o):(.debug_line)
            4345             4345        0     1                 $d.12
            4467             4467       f4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(toascii.o):(.debug_line)
            4467             4467        0     1                 $d.10
            455b             455b      115     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(tolower.o):(.debug_line)
            455b             455b        0     1                 $d.12
            4670             4670      115     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(toupper.o):(.debug_line)
            4670             4670        0     1                 $d.12
            4785             4785      1c8     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(c_locale.o):(.debug_line)
            4785             4785        0     1                 $d.11
            494d             494d      105     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(abs.o):(.debug_line)
            494d             494d        0     1                 $d.10
            4a52             4a52      133     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bsearch.o):(.debug_line)
            4a52             4a52        0     1                 $d.10
            4b85             4b85      105     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(div.o):(.debug_line)
            4b85             4b85        0     1                 $d.10
            4c8a             4c8a      11e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(imaxabs.o):(.debug_line)
            4c8a             4c8a        0     1                 $d.10
            4da8             4da8      11e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(imaxdiv.o):(.debug_line)
            4da8             4da8        0     1                 $d.10
            4ec6             4ec6      105     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(labs.o):(.debug_line)
            4ec6             4ec6        0     1                 $d.10
            4fcb             4fcb      105     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(ldiv.o):(.debug_line)
            4fcb             4fcb        0     1                 $d.10
            50d0             50d0      105     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(llabs.o):(.debug_line)
            50d0             50d0        0     1                 $d.10
            51d5             51d5      105     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(lldiv.o):(.debug_line)
            51d5             51d5        0     1                 $d.10
            52da             52da      6bc     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(qsort.o):(.debug_line)
            52da             52da        0     1                 $d.13
            5996             5996      11a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bcmp.o):(.debug_line)
            5996             5996        0     1                 $d.9
            5ab0             5ab0      2d8     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memccpy.o):(.debug_line)
            5ab0             5ab0        0     1                 $d.11
            5d88             5d88      3df     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memmem.o):(.debug_line)
            5d88             5d88        0     1                 $d.13
            6167             6167      122     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(mempcpy.o):(.debug_line)
            6167             6167        0     1                 $d.10
            6289             6289      11b     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memrchr.o):(.debug_line)
            6289             6289        0     1                 $d.10
            63a4             63a4      267     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stpcpy.o):(.debug_line)
            63a4             63a4        0     1                 $d.10
            660b             660b      2b9     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stpncpy.o):(.debug_line)
            660b             660b        0     1                 $d.10
            68c4             68c4      187     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcasecmp.o):(.debug_line)
            68c4             68c4        0     1                 $d.12
            6a4b             6a4b      13a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcasestr.o):(.debug_line)
            6a4b             6a4b        0     1                 $d.10
            6b85             6b85      247     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strchrnul.o):(.debug_line)
            6b85             6b85        0     1                 $d.10
            6dcc             6dcc      14e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcspn.o):(.debug_line)
            6dcc             6dcc        0     1                 $d.10
            6f1a             6f1a      175     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strerror_r.o):(.debug_line)
            6f1a             6f1a        0     1                 $d.10
            708f             708f      1ae     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncasecmp.o):(.debug_line)
            708f             708f        0     1                 $d.12
            723d             723d      140     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strndup.o):(.debug_line)
            723d             723d        0     1                 $d.10
            737d             737d      13e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strsep.o):(.debug_line)
            737d             737d        0     1                 $d.10
            74bb             74bb      136     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtok_r.o):(.debug_line)
            74bb             74bb        0     1                 $d.10
            75f1             75f1      1d3     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strverscmp.o):(.debug_line)
            75f1             75f1        0     1                 $d.11
            77c4             77c4      134     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(swab.o):(.debug_line)
            77c4             77c4        0     1                 $d.10
            78f8             78f8      167     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stderr.o):(.debug_line)
            78f8             78f8        0     1                 $d.11
            7a5f             7a5f      167     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdin.o):(.debug_line)
            7a5f             7a5f        0     1                 $d.11
            7bc6             7bc6      167     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdout.o):(.debug_line)
            7bc6             7bc6        0     1                 $d.11
            7d2d             7d2d      197     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_close.o):(.debug_line)
            7d2d             7d2d        0     1                 $d.12
            7ec4             7ec4      19d     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_read.o):(.debug_line)
            7ec4             7ec4        0     1                 $d.10
            8061             8061      24e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_write.o):(.debug_line)
            8061             8061        0     1                 $d.10
            82af             82af      17e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_seek.o):(.debug_line)
            82af             82af        0     1                 $d.10
            842d             842d      1ba     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__ctype_get_mb_cur_max.o):(.debug_line)
            842d             842d        0     1                 $d.9
            85e7             85e7      1e1     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(internal.o):(.debug_line)
            85e7             85e7        0     1                 $d.8
            87c8             87c8      2f0     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(mbtowc.o):(.debug_line)
            87c8             87c8        0     1                 $d.10
            8ab8             8ab8      2d0     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(wcrtomb.o):(.debug_line)
            8ab8             8ab8        0     1                 $d.10
            8d88             8d88      1cc     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bcopy.o):(.debug_line)
            8d88             8d88        0     1                 $d.10
            8f54             8f54      1cc     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bzero.o):(.debug_line)
            8f54             8f54        0     1                 $d.10
            9120             9120      1e9     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memchr.o):(.debug_line)
            9120             9120        0     1                 $d.10
            9309             9309      207     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memcmp.o):(.debug_line)
            9309             9309        0     1                 $d.10
            9510             9510      28e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memcpy.o):(.debug_line)
            9510             9510        0     1                 $d.10
            979e             979e      350     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memmove.o):(.debug_line)
            979e             979e        0     1                 $d.10
            9aee             9aee      296     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memset.o):(.debug_line)
            9aee             9aee        0     1                 $d.10
            9d84             9d84      1e5     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcat.o):(.debug_line)
            9d84             9d84        0     1                 $d.10
            9f69             9f69      1e6     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strchr.o):(.debug_line)
            9f69             9f69        0     1                 $d.10
            a14f             a14f      1da     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcmp.o):(.debug_line)
            a14f             a14f        0     1                 $d.10
            a329             a329      103     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcoll.o):(.debug_line)
            a329             a329        0     1                 $d.9
            a42c             a42c      1d5     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcpy.o):(.debug_line)
            a42c             a42c        0     1                 $d.10
            a601             a601      13f     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strdup.o):(.debug_line)
            a601             a601        0     1                 $d.10
            a740             a740      1d4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strerror.o):(.debug_line)
            a740             a740        0     1                 $d.11
            a914             a914      221     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strlcat.o):(.debug_line)
            a914             a914        0     1                 $d.10
            ab35             ab35      1fc     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strlcpy.o):(.debug_line)
            ab35             ab35        0     1                 $d.10
            ad31             ad31      1d3     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strlen.o):(.debug_line)
            ad31             ad31        0     1                 $d.10
            af04             af04      1f3     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncat.o):(.debug_line)
            af04             af04        0     1                 $d.10
            b0f7             b0f7      1d8     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncpy.o):(.debug_line)
            b0f7             b0f7        0     1                 $d.10
            b2cf             b2cf      1e6     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncmp.o):(.debug_line)
            b2cf             b2cf        0     1                 $d.10
            b4b5             b4b5      20f     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strnicmp.o):(.debug_line)
            b4b5             b4b5        0     1                 $d.10
            b6c4             b6c4      1ec     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strnlen.o):(.debug_line)
            b6c4             b6c4        0     1                 $d.10
            b8b0             b8b0      1f4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strpbrk.o):(.debug_line)
            b8b0             b8b0        0     1                 $d.10
            baa4             baa4      1e1     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strrchr.o):(.debug_line)
            baa4             baa4        0     1                 $d.10
            bc85             bc85      1f7     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strspn.o):(.debug_line)
            bc85             bc85        0     1                 $d.10
            be7c             be7c      1fd     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strstr.o):(.debug_line)
            be7c             be7c        0     1                 $d.10
            c079             c079      1ff     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtok.o):(.debug_line)
            c079             c079        0     1                 $d.11
            c278             c278      1e4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strxfrm.o):(.debug_line)
            c278             c278        0     1                 $d.10
            c45c             c45c      284     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(pure_virtual.o):(.debug_line)
            c45c             c45c        0     1                 $d.13
            c6e0             c6e0       57     1         out/build-imx8mp/kernel/rctee/lib/sm.mod.a(entry.o):(.debug_line)
            c6e0             c6e0        0     1                 $d.5
            c737             c737       4d     1         out/build-imx8mp/kernel/rctee/lib/smc.mod.a(smc.o):(.debug_line)
            c737             c737        0     1                 $d.4
            c784             c784       5a     1         out/build-imx8mp/kernel/rctee/lib/syscall.mod.a(syscall.o):(.debug_line)
            c784             c784        0     1                 $d.4
            c7de             c7de       31     1         out/build-imx8mp/user_product/ta/apploader/apploader.ko:(.debug_line)
            c7de             c7de        0     1                 $d.3
            c80f             c80f       31     1         out/build-imx8mp/user_product/ta/hwcrypto/hwcrypto.ko:(.debug_line)
            c80f             c80f        0     1                 $d.3
            c840             c840       31     1         out/build-imx8mp/user_product/ta/receiver/receiver.ko:(.debug_line)
            c840             c840        0     1                 $d.3
            c871             c871       31     1         out/build-imx8mp/user_product/ta/manifest-test/manifest-test.ko:(.debug_line)
            c871             c871        0     1                 $d.3
            c8a2             c8a2       31     1         out/build-imx8mp/user_product/ta/tongsuo_crypto_test/tongsuo_crypto_test.ko:(.debug_line)
            c8a2             c8a2        0     1                 $d.3
            c8d3             c8d3    3dfed     1         lto.tmp:(.debug_line)
            c8d3             c8d3        0     1                 $d.849
               0                0     3e0b     1 .debug_line_str
               0                0     3e0b     1         <internal>:(.debug_line_str)
               0                0     bdbe     1 .debug_rnglists
               0                0       23     1         out/build-imx8mp/kernel/lk/arch/arm64.mod.a(start.o):(.debug_rnglists)
               0                0        0     1                 $d.9
              23               23       37     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(libc_state.o):(.debug_rnglists)
              23               23        0     1                 $d.8
              5a               5a       6c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(atoi.o):(.debug_rnglists)
              5a               5a        0     1                 $d.9
              c6               c6       17     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(io_handle.o):(.debug_rnglists)
              c6               c6        0     1                 $d.5
              dd               dd      161     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(printf.o):(.debug_rnglists)
              dd               dd        0     1                 $d.17
             23e              23e       1a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(rand.o):(.debug_rnglists)
             23e              23e        0     1                 $d.7
             258              258       8d     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdio.o):(.debug_rnglists)
             258              258        0     1                 $d.16
             2e5              2e5       20     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(locale_stubs.o):(.debug_rnglists)
             2e5              2e5        0     1                 $d.9
             305              305       35     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(pthreads.o):(.debug_rnglists)
             305              305        0     1                 $d.15
             33a              33a       17     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isalnum.o):(.debug_rnglists)
             33a              33a        0     1                 $d.5
             351              351       17     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isalpha.o):(.debug_rnglists)
             351              351        0     1                 $d.5
             368              368       17     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isblank.o):(.debug_rnglists)
             368              368        0     1                 $d.5
             37f              37f       17     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(iscntrl.o):(.debug_rnglists)
             37f              37f        0     1                 $d.5
             396              396       17     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isdigit.o):(.debug_rnglists)
             396              396        0     1                 $d.5
             3ad              3ad       17     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isgraph.o):(.debug_rnglists)
             3ad              3ad        0     1                 $d.5
             3c4              3c4       17     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(islower.o):(.debug_rnglists)
             3c4              3c4        0     1                 $d.5
             3db              3db       17     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isprint.o):(.debug_rnglists)
             3db              3db        0     1                 $d.5
             3f2              3f2       17     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(ispunct.o):(.debug_rnglists)
             3f2              3f2        0     1                 $d.5
             409              409       17     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isspace.o):(.debug_rnglists)
             409              409        0     1                 $d.5
             420              420       17     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isupper.o):(.debug_rnglists)
             420              420        0     1                 $d.5
             437              437       17     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isxdigit.o):(.debug_rnglists)
             437              437        0     1                 $d.5
             44e              44e       17     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(tolower.o):(.debug_rnglists)
             44e              44e        0     1                 $d.5
             465              465       17     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(toupper.o):(.debug_rnglists)
             465              465        0     1                 $d.5
             47c              47c       82     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(qsort.o):(.debug_rnglists)
             47c              47c        0     1                 $d.6
             4fe              4fe       1a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memccpy.o):(.debug_rnglists)
             4fe              4fe        0     1                 $d.4
             518              518       2d     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memmem.o):(.debug_rnglists)
             518              518        0     1                 $d.6
             545              545       19     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcasecmp.o):(.debug_rnglists)
             545              545        0     1                 $d.5
             55e              55e       19     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncasecmp.o):(.debug_rnglists)
             55e              55e        0     1                 $d.5
             577              577       17     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strverscmp.o):(.debug_rnglists)
             577              577        0     1                 $d.4
             58e              58e       17     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_close.o):(.debug_rnglists)
             58e              58e        0     1                 $d.5
             5a5              5a5       17     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(pure_virtual.o):(.debug_rnglists)
             5a5              5a5        0     1                 $d.6
             5bc              5bc     b802     1         lto.tmp:(.debug_rnglists)
             5bc              5bc        0     1                 $d.728
               0                0    fa7b5     1 .debug_macro
               0                0      94d     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(abort.o):(.debug_macro)
               0                0        0     1                 $d.4
             94d              94d      8c0     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(close.o):(.debug_macro)
             94d              94d        0     1                 $d.5
            120d             120d      886     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(fflush.o):(.debug_macro)
            120d             120d        0     1                 $d.4
            1a93             1a93     194b     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(libc_state.o):(.debug_macro)
            1a93             1a93        0     1                 $d.9
            33de             33de     1395     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(writev.o):(.debug_macro)
            33de             33de        0     1                 $d.4
            4773             4773     143e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(atoi.o):(.debug_macro)
            4773             4773        0     1                 $d.10
            5bb1             5bb1      78c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(eabi.o):(.debug_macro)
            5bb1             5bb1        0     1                 $d.5
            633d             633d      918     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(eabi_unwind_stubs.o):(.debug_macro)
            633d             633d        0     1                 $d.3
            6c55             6c55     1212     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(io_handle.o):(.debug_macro)
            6c55             6c55        0     1                 $d.6
            7e67             7e67     12cc     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(printf.o):(.debug_macro)
            7e67             7e67        0     1                 $d.18
            9133             9133     10be     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(rand.o):(.debug_macro)
            9133             9133        0     1                 $d.8
            a1f1             a1f1     12c2     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdio.o):(.debug_macro)
            a1f1             a1f1        0     1                 $d.17
            b4b3             b4b3      c98     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtol.o):(.debug_macro)
            b4b3             b4b3        0     1                 $d.4
            c14b             c14b     12e2     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtoll.o):(.debug_macro)
            c14b             c14b        0     1                 $d.4
            d42d             d42d      bc6     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(locale_stubs.o):(.debug_macro)
            d42d             d42d        0     1                 $d.10
            dff3             dff3      789     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(atexit.o):(.debug_macro)
            dff3             dff3        0     1                 $d.4
            e77c             e77c     108c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(pthreads.o):(.debug_macro)
            e77c             e77c        0     1                 $d.16
            f808             f808      80a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isalnum.o):(.debug_macro)
            f808             f808        0     1                 $d.6
           10012            10012      80d     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isalpha.o):(.debug_macro)
           10012            10012        0     1                 $d.6
           1081f            1081f      808     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isascii.o):(.debug_macro)
           1081f            1081f        0     1                 $d.4
           11027            11027      809     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isblank.o):(.debug_macro)
           11027            11027        0     1                 $d.6
           11830            11830      80a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(iscntrl.o):(.debug_macro)
           11830            11830        0     1                 $d.6
           1203a            1203a      80d     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isdigit.o):(.debug_macro)
           1203a            1203a        0     1                 $d.6
           12847            12847      80d     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isgraph.o):(.debug_macro)
           12847            12847        0     1                 $d.6
           13054            13054      80d     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(islower.o):(.debug_macro)
           13054            13054        0     1                 $d.6
           13861            13861      80d     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isprint.o):(.debug_macro)
           13861            13861        0     1                 $d.6
           1406e            1406e      80a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(ispunct.o):(.debug_macro)
           1406e            1406e        0     1                 $d.6
           14878            14878      80d     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isspace.o):(.debug_macro)
           14878            14878        0     1                 $d.6
           15085            15085      80d     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isupper.o):(.debug_macro)
           15085            15085        0     1                 $d.6
           15892            15892      80a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isxdigit.o):(.debug_macro)
           15892            15892        0     1                 $d.6
           1609c            1609c      805     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(toascii.o):(.debug_macro)
           1609c            1609c        0     1                 $d.4
           168a1            168a1      80a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(tolower.o):(.debug_macro)
           168a1            168a1        0     1                 $d.6
           170ab            170ab      80a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(toupper.o):(.debug_macro)
           170ab            170ab        0     1                 $d.6
           178b5            178b5      d0e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(c_locale.o):(.debug_macro)
           178b5            178b5        0     1                 $d.6
           185c3            185c3      820     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(abs.o):(.debug_macro)
           185c3            185c3        0     1                 $d.4
           18de3            18de3      829     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bsearch.o):(.debug_macro)
           18de3            18de3        0     1                 $d.4
           1960c            1960c      824     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(div.o):(.debug_macro)
           1960c            1960c        0     1                 $d.4
           19e30            19e30      c23     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(imaxabs.o):(.debug_macro)
           19e30            19e30        0     1                 $d.4
           1aa53            1aa53      c27     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(imaxdiv.o):(.debug_macro)
           1aa53            1aa53        0     1                 $d.4
           1b67a            1b67a      820     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(labs.o):(.debug_macro)
           1b67a            1b67a        0     1                 $d.4
           1be9a            1be9a      824     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(ldiv.o):(.debug_macro)
           1be9a            1be9a        0     1                 $d.4
           1c6be            1c6be      820     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(llabs.o):(.debug_macro)
           1c6be            1c6be        0     1                 $d.4
           1cede            1cede      824     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(lldiv.o):(.debug_macro)
           1cede            1cede        0     1                 $d.4
           1d702            1d702      a90     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(qsort.o):(.debug_macro)
           1d702            1d702        0     1                 $d.7
           1e192            1e192      828     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bcmp.o):(.debug_macro)
           1e192            1e192        0     1                 $d.3
           1e9ba            1e9ba      b68     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memccpy.o):(.debug_macro)
           1e9ba            1e9ba        0     1                 $d.5
           1f522            1f522      9db     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memmem.o):(.debug_macro)
           1f522            1f522        0     1                 $d.7
           1fefd            1fefd      82c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(mempcpy.o):(.debug_macro)
           1fefd            1fefd        0     1                 $d.4
           20729            20729      7f7     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memrchr.o):(.debug_macro)
           20729            20729        0     1                 $d.4
           20f20            20f20      b61     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stpcpy.o):(.debug_macro)
           20f20            20f20        0     1                 $d.4
           21a81            21a81      b63     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stpncpy.o):(.debug_macro)
           21a81            21a81        0     1                 $d.4
           225e4            225e4      841     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcasecmp.o):(.debug_macro)
           225e4            225e4        0     1                 $d.6
           22e25            22e25      82e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcasestr.o):(.debug_macro)
           22e25            22e25        0     1                 $d.4
           23653            23653      b63     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strchrnul.o):(.debug_macro)
           23653            23653        0     1                 $d.4
           241b6            241b6      7fc     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcspn.o):(.debug_macro)
           241b6            241b6        0     1                 $d.4
           249b2            249b2      a39     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strerror_r.o):(.debug_macro)
           249b2            249b2        0     1                 $d.4
           253eb            253eb      844     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncasecmp.o):(.debug_macro)
           253eb            253eb        0     1                 $d.6
           25c2f            25c2f      866     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strndup.o):(.debug_macro)
           25c2f            25c2f        0     1                 $d.4
           26495            26495      82b     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strsep.o):(.debug_macro)
           26495            26495        0     1                 $d.4
           26cc0            26cc0      7f3     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtok_r.o):(.debug_macro)
           26cc0            26cc0        0     1                 $d.4
           274b3            274b3      888     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strverscmp.o):(.debug_macro)
           274b3            274b3        0     1                 $d.5
           27d3b            27d3b      d3d     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(swab.o):(.debug_macro)
           27d3b            27d3b        0     1                 $d.4
           28a78            28a78      a42     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stderr.o):(.debug_macro)
           28a78            28a78        0     1                 $d.6
           294ba            294ba      a42     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdin.o):(.debug_macro)
           294ba            294ba        0     1                 $d.6
           29efc            29efc      a42     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdout.o):(.debug_macro)
           29efc            29efc        0     1                 $d.6
           2a93e            2a93e      a3f     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_close.o):(.debug_macro)
           2a93e            2a93e        0     1                 $d.6
           2b37d            2b37d      a7b     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_read.o):(.debug_macro)
           2b37d            2b37d        0     1                 $d.4
           2bdf8            2bdf8      a8a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_write.o):(.debug_macro)
           2bdf8            2bdf8        0     1                 $d.4
           2c882            2c882      a3f     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_seek.o):(.debug_macro)
           2c882            2c882        0     1                 $d.4
           2d2c1            2d2c1      b81     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__ctype_get_mb_cur_max.o):(.debug_macro)
           2d2c1            2d2c1        0     1                 $d.3
           2de42            2de42      d35     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(internal.o):(.debug_macro)
           2de42            2de42        0     1                 $d.3
           2eb77            2eb77     1004     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(mbtowc.o):(.debug_macro)
           2eb77            2eb77        0     1                 $d.4
           2fb7b            2fb7b     1000     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(wcrtomb.o):(.debug_macro)
           2fb7b            2fb7b        0     1                 $d.4
           30b7b            30b7b     1012     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bcopy.o):(.debug_macro)
           30b7b            30b7b        0     1                 $d.4
           31b8d            31b8d     1011     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bzero.o):(.debug_macro)
           31b8d            31b8d        0     1                 $d.4
           32b9e            32b9e     1017     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memchr.o):(.debug_macro)
           32b9e            32b9e        0     1                 $d.4
           33bb5            33bb5     10f1     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memcmp.o):(.debug_macro)
           33bb5            33bb5        0     1                 $d.4
           34ca6            34ca6     1022     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memcpy.o):(.debug_macro)
           34ca6            34ca6        0     1                 $d.4
           35cc8            35cc8     1022     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memmove.o):(.debug_macro)
           35cc8            35cc8        0     1                 $d.4
           36cea            36cea     101c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memset.o):(.debug_macro)
           36cea            36cea        0     1                 $d.4
           37d06            37d06     1011     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcat.o):(.debug_macro)
           37d06            37d06        0     1                 $d.4
           38d17            38d17     1011     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strchr.o):(.debug_macro)
           38d17            38d17        0     1                 $d.4
           39d28            39d28     1015     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcmp.o):(.debug_macro)
           39d28            39d28        0     1                 $d.4
           3ad3d            3ad3d      7f3     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcoll.o):(.debug_macro)
           3ad3d            3ad3d        0     1                 $d.3
           3b530            3b530     1011     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcpy.o):(.debug_macro)
           3b530            3b530        0     1                 $d.4
           3c541            3c541      864     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strdup.o):(.debug_macro)
           3c541            3c541        0     1                 $d.4
           3cda5            3cda5     1011     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strerror.o):(.debug_macro)
           3cda5            3cda5        0     1                 $d.5
           3ddb6            3ddb6     1016     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strlcat.o):(.debug_macro)
           3ddb6            3ddb6        0     1                 $d.4
           3edcc            3edcc     1014     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strlcpy.o):(.debug_macro)
           3edcc            3edcc        0     1                 $d.4
           3fde0            3fde0     1012     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strlen.o):(.debug_macro)
           3fde0            3fde0        0     1                 $d.4
           40df2            40df2     1014     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncat.o):(.debug_macro)
           40df2            40df2        0     1                 $d.4
           41e06            41e06     1014     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncpy.o):(.debug_macro)
           41e06            41e06        0     1                 $d.4
           42e1a            42e1a     1018     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncmp.o):(.debug_macro)
           42e1a            42e1a        0     1                 $d.4
           43e32            43e32     106a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strnicmp.o):(.debug_macro)
           43e32            43e32        0     1                 $d.4
           44e9c            44e9c     1013     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strnlen.o):(.debug_macro)
           44e9c            44e9c        0     1                 $d.4
           45eaf            45eaf     1012     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strpbrk.o):(.debug_macro)
           45eaf            45eaf        0     1                 $d.4
           46ec1            46ec1     1012     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strrchr.o):(.debug_macro)
           46ec1            46ec1        0     1                 $d.4
           47ed3            47ed3     1015     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strspn.o):(.debug_macro)
           47ed3            47ed3        0     1                 $d.4
           48ee8            48ee8     1013     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strstr.o):(.debug_macro)
           48ee8            48ee8        0     1                 $d.4
           49efb            49efb     1013     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtok.o):(.debug_macro)
           49efb            49efb        0     1                 $d.5
           4af0e            4af0e     1015     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strxfrm.o):(.debug_macro)
           4af0e            4af0e        0     1                 $d.4
           4bf23            4bf23     1400     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(pure_virtual.o):(.debug_macro)
           4bf23            4bf23        0     1                 $d.7
           4d323            4d323    ad492     1         lto.tmp:(.debug_macro)
           4d323            4d323        0     1                 $d.729
               0                0    537fc     1 .debug_str_offsets
               0                0      974     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(abort.o):(.debug_str_offsets)
               0                0        0     1                 $d.5
             974              974      928     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(close.o):(.debug_str_offsets)
             974              974        0     1                 $d.6
            129c             129c      8f4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(fflush.o):(.debug_str_offsets)
            129c             129c        0     1                 $d.5
            1b90             1b90     1840     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(libc_state.o):(.debug_str_offsets)
            1b90             1b90        0     1                 $d.10
            33d0             33d0     1290     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(writev.o):(.debug_str_offsets)
            33d0             33d0        0     1                 $d.5
            4660             4660     132c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(atoi.o):(.debug_str_offsets)
            4660             4660        0     1                 $d.11
            598c             598c      818     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(eabi.o):(.debug_str_offsets)
            598c             598c        0     1                 $d.6
            61a4             61a4      978     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(eabi_unwind_stubs.o):(.debug_str_offsets)
            61a4             61a4        0     1                 $d.4
            6b1c             6b1c     1150     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(io_handle.o):(.debug_str_offsets)
            6b1c             6b1c        0     1                 $d.7
            7c6c             7c6c     1258     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(printf.o):(.debug_str_offsets)
            7c6c             7c6c        0     1                 $d.19
            8ec4             8ec4     1030     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(rand.o):(.debug_str_offsets)
            8ec4             8ec4        0     1                 $d.9
            9ef4             9ef4     122c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdio.o):(.debug_str_offsets)
            9ef4             9ef4        0     1                 $d.18
            b120             b120      cd4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtol.o):(.debug_str_offsets)
            b120             b120        0     1                 $d.5
            bdf4             bdf4     1204     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtoll.o):(.debug_str_offsets)
            bdf4             bdf4        0     1                 $d.5
            cff8             cff8      bb0     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(locale_stubs.o):(.debug_str_offsets)
            cff8             cff8        0     1                 $d.11
            dba8             dba8      80c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(atexit.o):(.debug_str_offsets)
            dba8             dba8        0     1                 $d.5
            e3b4             e3b4     1014     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(pthreads.o):(.debug_str_offsets)
            e3b4             e3b4        0     1                 $d.17
            f3c8             f3c8      888     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isalnum.o):(.debug_str_offsets)
            f3c8             f3c8        0     1                 $d.7
            fc50             fc50      888     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isalpha.o):(.debug_str_offsets)
            fc50             fc50        0     1                 $d.7
           104d8            104d8      874     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isascii.o):(.debug_str_offsets)
           104d8            104d8        0     1                 $d.5
           10d4c            10d4c      884     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isblank.o):(.debug_str_offsets)
           10d4c            10d4c        0     1                 $d.7
           115d0            115d0      888     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(iscntrl.o):(.debug_str_offsets)
           115d0            115d0        0     1                 $d.7
           11e58            11e58      888     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isdigit.o):(.debug_str_offsets)
           11e58            11e58        0     1                 $d.7
           126e0            126e0      888     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isgraph.o):(.debug_str_offsets)
           126e0            126e0        0     1                 $d.7
           12f68            12f68      888     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(islower.o):(.debug_str_offsets)
           12f68            12f68        0     1                 $d.7
           137f0            137f0      888     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isprint.o):(.debug_str_offsets)
           137f0            137f0        0     1                 $d.7
           14078            14078      888     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(ispunct.o):(.debug_str_offsets)
           14078            14078        0     1                 $d.7
           14900            14900      888     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isspace.o):(.debug_str_offsets)
           14900            14900        0     1                 $d.7
           15188            15188      888     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isupper.o):(.debug_str_offsets)
           15188            15188        0     1                 $d.7
           15a10            15a10      888     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isxdigit.o):(.debug_str_offsets)
           15a10            15a10        0     1                 $d.7
           16298            16298      874     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(toascii.o):(.debug_str_offsets)
           16298            16298        0     1                 $d.5
           16b0c            16b0c      888     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(tolower.o):(.debug_str_offsets)
           16b0c            16b0c        0     1                 $d.7
           17394            17394      888     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(toupper.o):(.debug_str_offsets)
           17394            17394        0     1                 $d.7
           17c1c            17c1c      ce8     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(c_locale.o):(.debug_str_offsets)
           17c1c            17c1c        0     1                 $d.7
           18904            18904      88c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(abs.o):(.debug_str_offsets)
           18904            18904        0     1                 $d.5
           19190            19190      8b0     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bsearch.o):(.debug_str_offsets)
           19190            19190        0     1                 $d.5
           19a40            19a40      89c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(div.o):(.debug_str_offsets)
           19a40            19a40        0     1                 $d.5
           1a2dc            1a2dc      c18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(imaxabs.o):(.debug_str_offsets)
           1a2dc            1a2dc        0     1                 $d.5
           1aef4            1aef4      c28     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(imaxdiv.o):(.debug_str_offsets)
           1aef4            1aef4        0     1                 $d.5
           1bb1c            1bb1c      88c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(labs.o):(.debug_str_offsets)
           1bb1c            1bb1c        0     1                 $d.5
           1c3a8            1c3a8      89c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(ldiv.o):(.debug_str_offsets)
           1c3a8            1c3a8        0     1                 $d.5
           1cc44            1cc44      88c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(llabs.o):(.debug_str_offsets)
           1cc44            1cc44        0     1                 $d.5
           1d4d0            1d4d0      89c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(lldiv.o):(.debug_str_offsets)
           1d4d0            1d4d0        0     1                 $d.5
           1dd6c            1dd6c      af4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(qsort.o):(.debug_str_offsets)
           1dd6c            1dd6c        0     1                 $d.8
           1e860            1e860      874     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bcmp.o):(.debug_str_offsets)
           1e860            1e860        0     1                 $d.4
           1f0d4            1f0d4      ba8     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memccpy.o):(.debug_str_offsets)
           1f0d4            1f0d4        0     1                 $d.6
           1fc7c            1fc7c      a44     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memmem.o):(.debug_str_offsets)
           1fc7c            1fc7c        0     1                 $d.8
           206c0            206c0      878     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(mempcpy.o):(.debug_str_offsets)
           206c0            206c0        0     1                 $d.5
           20f38            20f38      874     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memrchr.o):(.debug_str_offsets)
           20f38            20f38        0     1                 $d.5
           217ac            217ac      b8c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stpcpy.o):(.debug_str_offsets)
           217ac            217ac        0     1                 $d.5
           22338            22338      b94     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stpncpy.o):(.debug_str_offsets)
           22338            22338        0     1                 $d.5
           22ecc            22ecc      8a4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcasecmp.o):(.debug_str_offsets)
           22ecc            22ecc        0     1                 $d.7
           23770            23770      880     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcasestr.o):(.debug_str_offsets)
           23770            23770        0     1                 $d.5
           23ff0            23ff0      b94     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strchrnul.o):(.debug_str_offsets)
           23ff0            23ff0        0     1                 $d.5
           24b84            24b84      87c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcspn.o):(.debug_str_offsets)
           24b84            24b84        0     1                 $d.5
           25400            25400      aa4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strerror_r.o):(.debug_str_offsets)
           25400            25400        0     1                 $d.5
           25ea4            25ea4      8b0     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncasecmp.o):(.debug_str_offsets)
           25ea4            25ea4        0     1                 $d.7
           26754            26754      8b4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strndup.o):(.debug_str_offsets)
           26754            26754        0     1                 $d.5
           27008            27008      874     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strsep.o):(.debug_str_offsets)
           27008            27008        0     1                 $d.5
           2787c            2787c      864     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtok_r.o):(.debug_str_offsets)
           2787c            2787c        0     1                 $d.5
           280e0            280e0      8c8     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strverscmp.o):(.debug_str_offsets)
           280e0            280e0        0     1                 $d.6
           289a8            289a8      cb8     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(swab.o):(.debug_str_offsets)
           289a8            289a8        0     1                 $d.5
           29660            29660      adc     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stderr.o):(.debug_str_offsets)
           29660            29660        0     1                 $d.7
           2a13c            2a13c      adc     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdin.o):(.debug_str_offsets)
           2a13c            2a13c        0     1                 $d.7
           2ac18            2ac18      adc     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdout.o):(.debug_str_offsets)
           2ac18            2ac18        0     1                 $d.7
           2b6f4            2b6f4      adc     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_close.o):(.debug_str_offsets)
           2b6f4            2b6f4        0     1                 $d.7
           2c1d0            2c1d0      aec     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_read.o):(.debug_str_offsets)
           2c1d0            2c1d0        0     1                 $d.5
           2ccbc            2ccbc      b18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_write.o):(.debug_str_offsets)
           2ccbc            2ccbc        0     1                 $d.5
           2d7d4            2d7d4      adc     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_seek.o):(.debug_str_offsets)
           2d7d4            2d7d4        0     1                 $d.5
           2e2b0            2e2b0      b80     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__ctype_get_mb_cur_max.o):(.debug_str_offsets)
           2e2b0            2e2b0        0     1                 $d.4
           2ee30            2ee30      ce4     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(internal.o):(.debug_str_offsets)
           2ee30            2ee30        0     1                 $d.4
           2fb14            2fb14      f88     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(mbtowc.o):(.debug_str_offsets)
           2fb14            2fb14        0     1                 $d.5
           30a9c            30a9c      f78     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(wcrtomb.o):(.debug_str_offsets)
           30a9c            30a9c        0     1                 $d.5
           31a14            31a14      f4c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bcopy.o):(.debug_str_offsets)
           31a14            31a14        0     1                 $d.5
           32960            32960      f48     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bzero.o):(.debug_str_offsets)
           32960            32960        0     1                 $d.5
           338a8            338a8      f60     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memchr.o):(.debug_str_offsets)
           338a8            338a8        0     1                 $d.5
           34808            34808     1028     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memcmp.o):(.debug_str_offsets)
           34808            34808        0     1                 $d.5
           35830            35830      f74     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memcpy.o):(.debug_str_offsets)
           35830            35830        0     1                 $d.5
           367a4            367a4      f74     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memmove.o):(.debug_str_offsets)
           367a4            367a4        0     1                 $d.5
           37718            37718      f74     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memset.o):(.debug_str_offsets)
           37718            37718        0     1                 $d.5
           3868c            3868c      f48     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcat.o):(.debug_str_offsets)
           3868c            3868c        0     1                 $d.5
           395d4            395d4      f48     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strchr.o):(.debug_str_offsets)
           395d4            395d4        0     1                 $d.5
           3a51c            3a51c      f58     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcmp.o):(.debug_str_offsets)
           3a51c            3a51c        0     1                 $d.5
           3b474            3b474      864     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcoll.o):(.debug_str_offsets)
           3b474            3b474        0     1                 $d.4
           3bcd8            3bcd8      f48     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcpy.o):(.debug_str_offsets)
           3bcd8            3bcd8        0     1                 $d.5
           3cc20            3cc20      8ac     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strdup.o):(.debug_str_offsets)
           3cc20            3cc20        0     1                 $d.5
           3d4cc            3d4cc      f48     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strerror.o):(.debug_str_offsets)
           3d4cc            3d4cc        0     1                 $d.6
           3e414            3e414      f5c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strlcat.o):(.debug_str_offsets)
           3e414            3e414        0     1                 $d.5
           3f370            3f370      f54     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strlcpy.o):(.debug_str_offsets)
           3f370            3f370        0     1                 $d.5
           402c4            402c4      f4c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strlen.o):(.debug_str_offsets)
           402c4            402c4        0     1                 $d.5
           41210            41210      f54     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncat.o):(.debug_str_offsets)
           41210            41210        0     1                 $d.5
           42164            42164      f54     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncpy.o):(.debug_str_offsets)
           42164            42164        0     1                 $d.5
           430b8            430b8      f64     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncmp.o):(.debug_str_offsets)
           430b8            430b8        0     1                 $d.5
           4401c            4401c      f8c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strnicmp.o):(.debug_str_offsets)
           4401c            4401c        0     1                 $d.5
           44fa8            44fa8      f50     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strnlen.o):(.debug_str_offsets)
           44fa8            44fa8        0     1                 $d.5
           45ef8            45ef8      f4c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strpbrk.o):(.debug_str_offsets)
           45ef8            45ef8        0     1                 $d.5
           46e44            46e44      f4c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strrchr.o):(.debug_str_offsets)
           46e44            46e44        0     1                 $d.5
           47d90            47d90      f58     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strspn.o):(.debug_str_offsets)
           47d90            47d90        0     1                 $d.5
           48ce8            48ce8      f50     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strstr.o):(.debug_str_offsets)
           48ce8            48ce8        0     1                 $d.5
           49c38            49c38      f50     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtok.o):(.debug_str_offsets)
           49c38            49c38        0     1                 $d.6
           4ab88            4ab88      f58     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strxfrm.o):(.debug_str_offsets)
           4ab88            4ab88        0     1                 $d.5
           4bae0            4bae0     129c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(pure_virtual.o):(.debug_str_offsets)
           4bae0            4bae0        0     1                 $d.8
           4cd7c            4cd7c     6a80     1         lto.tmp:(.debug_str_offsets)
           4cd7c            4cd7c        0     1                 $d.730
               0                0    3211e     1 .debug_str
               0                0    3211e     1         <internal>:(.debug_str)
               0                0     ec20     1 .debug_addr
               0                0       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(abort.o):(.debug_addr)
               0                0        0     1                 $d.7
              18               18       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(close.o):(.debug_addr)
              18               18        0     1                 $d.8
              30               30       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(fflush.o):(.debug_addr)
              30               30        0     1                 $d.7
              40               40       70     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(libc_state.o):(.debug_addr)
              40               40        0     1                 $d.12
              b0               b0       40     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(writev.o):(.debug_addr)
              b0               b0        0     1                 $d.7
              f0               f0       58     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(atoi.o):(.debug_addr)
              f0               f0        0     1                 $d.13
             148              148       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(eabi.o):(.debug_addr)
             148              148        0     1                 $d.8
             160              160       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(eabi_unwind_stubs.o):(.debug_addr)
             160              160        0     1                 $d.6
             170              170       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(io_handle.o):(.debug_addr)
             170              170        0     1                 $d.9
             188              188      190     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(printf.o):(.debug_addr)
             188              188        0     1                 $d.21
             318              318       30     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(rand.o):(.debug_addr)
             318              318        0     1                 $d.11
             348              348      298     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdio.o):(.debug_addr)
             348              348        0     1                 $d.20
             5e0              5e0       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtol.o):(.debug_addr)
             5e0              5e0        0     1                 $d.7
             5f8              5f8       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtoll.o):(.debug_addr)
             5f8              5f8        0     1                 $d.7
             610              610       48     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(locale_stubs.o):(.debug_addr)
             610              610        0     1                 $d.13
             658              658       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(atexit.o):(.debug_addr)
             658              658        0     1                 $d.7
             668              668       68     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(pthreads.o):(.debug_addr)
             668              668        0     1                 $d.19
             6d0              6d0       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isalnum.o):(.debug_addr)
             6d0              6d0        0     1                 $d.9
             6e8              6e8       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isalpha.o):(.debug_addr)
             6e8              6e8        0     1                 $d.9
             700              700       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isascii.o):(.debug_addr)
             700              700        0     1                 $d.7
             710              710       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isblank.o):(.debug_addr)
             710              710        0     1                 $d.9
             728              728       20     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(iscntrl.o):(.debug_addr)
             728              728        0     1                 $d.9
             748              748       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isdigit.o):(.debug_addr)
             748              748        0     1                 $d.9
             760              760       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isgraph.o):(.debug_addr)
             760              760        0     1                 $d.9
             778              778       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(islower.o):(.debug_addr)
             778              778        0     1                 $d.9
             790              790       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isprint.o):(.debug_addr)
             790              790        0     1                 $d.9
             7a8              7a8       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(ispunct.o):(.debug_addr)
             7a8              7a8        0     1                 $d.9
             7c0              7c0       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isspace.o):(.debug_addr)
             7c0              7c0        0     1                 $d.9
             7d8              7d8       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isupper.o):(.debug_addr)
             7d8              7d8        0     1                 $d.9
             7f0              7f0       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isxdigit.o):(.debug_addr)
             7f0              7f0        0     1                 $d.9
             808              808       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(toascii.o):(.debug_addr)
             808              808        0     1                 $d.7
             818              818       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(tolower.o):(.debug_addr)
             818              818        0     1                 $d.9
             830              830       20     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(toupper.o):(.debug_addr)
             830              830        0     1                 $d.9
             850              850       28     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(c_locale.o):(.debug_addr)
             850              850        0     1                 $d.9
             878              878       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(abs.o):(.debug_addr)
             878              878        0     1                 $d.7
             888              888       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bsearch.o):(.debug_addr)
             888              888        0     1                 $d.7
             8a0              8a0       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(div.o):(.debug_addr)
             8a0              8a0        0     1                 $d.7
             8b0              8b0       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(imaxabs.o):(.debug_addr)
             8b0              8b0        0     1                 $d.7
             8c0              8c0       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(imaxdiv.o):(.debug_addr)
             8c0              8c0        0     1                 $d.7
             8d0              8d0       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(labs.o):(.debug_addr)
             8d0              8d0        0     1                 $d.7
             8e0              8e0       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(ldiv.o):(.debug_addr)
             8e0              8e0        0     1                 $d.7
             8f0              8f0       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(llabs.o):(.debug_addr)
             8f0              8f0        0     1                 $d.7
             900              900       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(lldiv.o):(.debug_addr)
             900              900        0     1                 $d.7
             910              910      118     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(qsort.o):(.debug_addr)
             910              910        0     1                 $d.10
             a28              a28       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bcmp.o):(.debug_addr)
             a28              a28        0     1                 $d.6
             a38              a38       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memccpy.o):(.debug_addr)
             a38              a38        0     1                 $d.8
             a50              a50       38     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memmem.o):(.debug_addr)
             a50              a50        0     1                 $d.10
             a88              a88       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(mempcpy.o):(.debug_addr)
             a88              a88        0     1                 $d.7
             a98              a98       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memrchr.o):(.debug_addr)
             a98              a98        0     1                 $d.7
             aa8              aa8       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stpcpy.o):(.debug_addr)
             aa8              aa8        0     1                 $d.7
             ab8              ab8       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stpncpy.o):(.debug_addr)
             ab8              ab8        0     1                 $d.7
             ad0              ad0       20     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcasecmp.o):(.debug_addr)
             ad0              ad0        0     1                 $d.9
             af0              af0       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcasestr.o):(.debug_addr)
             af0              af0        0     1                 $d.7
             b08              b08       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strchrnul.o):(.debug_addr)
             b08              b08        0     1                 $d.7
             b18              b18       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcspn.o):(.debug_addr)
             b18              b18        0     1                 $d.7
             b28              b28       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strerror_r.o):(.debug_addr)
             b28              b28        0     1                 $d.7
             b38              b38       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncasecmp.o):(.debug_addr)
             b38              b38        0     1                 $d.9
             b50              b50       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strndup.o):(.debug_addr)
             b50              b50        0     1                 $d.7
             b68              b68       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strsep.o):(.debug_addr)
             b68              b68        0     1                 $d.7
             b78              b78       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtok_r.o):(.debug_addr)
             b78              b78        0     1                 $d.7
             b88              b88       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strverscmp.o):(.debug_addr)
             b88              b88        0     1                 $d.8
             b98              b98       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(swab.o):(.debug_addr)
             b98              b98        0     1                 $d.7
             ba8              ba8       28     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stderr.o):(.debug_addr)
             ba8              ba8        0     1                 $d.9
             bd0              bd0       28     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdin.o):(.debug_addr)
             bd0              bd0        0     1                 $d.9
             bf8              bf8       28     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdout.o):(.debug_addr)
             bf8              bf8        0     1                 $d.9
             c20              c20       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_close.o):(.debug_addr)
             c20              c20        0     1                 $d.9
             c38              c38       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_read.o):(.debug_addr)
             c38              c38        0     1                 $d.7
             c48              c48       20     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_write.o):(.debug_addr)
             c48              c48        0     1                 $d.7
             c68              c68       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_seek.o):(.debug_addr)
             c68              c68        0     1                 $d.7
             c78              c78       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__ctype_get_mb_cur_max.o):(.debug_addr)
             c78              c78        0     1                 $d.6
             c88              c88       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(internal.o):(.debug_addr)
             c88              c88        0     1                 $d.6
             c98              c98       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(mbtowc.o):(.debug_addr)
             c98              c98        0     1                 $d.7
             cb0              cb0       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(wcrtomb.o):(.debug_addr)
             cb0              cb0        0     1                 $d.7
             cc0              cc0       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bcopy.o):(.debug_addr)
             cc0              cc0        0     1                 $d.7
             cd0              cd0       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bzero.o):(.debug_addr)
             cd0              cd0        0     1                 $d.7
             ce0              ce0       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memchr.o):(.debug_addr)
             ce0              ce0        0     1                 $d.7
             cf0              cf0       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memcmp.o):(.debug_addr)
             cf0              cf0        0     1                 $d.7
             d00              d00       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memcpy.o):(.debug_addr)
             d00              d00        0     1                 $d.7
             d10              d10       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memmove.o):(.debug_addr)
             d10              d10        0     1                 $d.7
             d20              d20       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memset.o):(.debug_addr)
             d20              d20        0     1                 $d.7
             d30              d30       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcat.o):(.debug_addr)
             d30              d30        0     1                 $d.7
             d40              d40       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strchr.o):(.debug_addr)
             d40              d40        0     1                 $d.7
             d50              d50       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcmp.o):(.debug_addr)
             d50              d50        0     1                 $d.7
             d60              d60       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcoll.o):(.debug_addr)
             d60              d60        0     1                 $d.6
             d70              d70       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcpy.o):(.debug_addr)
             d70              d70        0     1                 $d.7
             d80              d80       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strdup.o):(.debug_addr)
             d80              d80        0     1                 $d.7
             d90              d90       20     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strerror.o):(.debug_addr)
             d90              d90        0     1                 $d.8
             db0              db0       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strlcat.o):(.debug_addr)
             db0              db0        0     1                 $d.7
             dc8              dc8       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strlcpy.o):(.debug_addr)
             dc8              dc8        0     1                 $d.7
             dd8              dd8       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strlen.o):(.debug_addr)
             dd8              dd8        0     1                 $d.7
             de8              de8       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncat.o):(.debug_addr)
             de8              de8        0     1                 $d.7
             df8              df8       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncpy.o):(.debug_addr)
             df8              df8        0     1                 $d.7
             e08              e08       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncmp.o):(.debug_addr)
             e08              e08        0     1                 $d.7
             e18              e18       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strnicmp.o):(.debug_addr)
             e18              e18        0     1                 $d.7
             e28              e28       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strnlen.o):(.debug_addr)
             e28              e28        0     1                 $d.7
             e38              e38       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strpbrk.o):(.debug_addr)
             e38              e38        0     1                 $d.7
             e48              e48       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strrchr.o):(.debug_addr)
             e48              e48        0     1                 $d.7
             e58              e58       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strspn.o):(.debug_addr)
             e58              e58        0     1                 $d.7
             e68              e68       10     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strstr.o):(.debug_addr)
             e68              e68        0     1                 $d.7
             e78              e78       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtok.o):(.debug_addr)
             e78              e78        0     1                 $d.8
             e90              e90       18     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strxfrm.o):(.debug_addr)
             e90              e90        0     1                 $d.7
             ea8              ea8       20     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(pure_virtual.o):(.debug_addr)
             ea8              ea8        0     1                 $d.10
             ec8              ec8     dd58     1         lto.tmp:(.debug_addr)
             ec8              ec8        0     1                 $d.732
               0                0    30f86     1 .debug_loclists
               0                0       1e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(close.o):(.debug_loclists)
               0                0        0     1                 $d.2
              1e               1e       1e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(fflush.o):(.debug_loclists)
              1e               1e        0     1                 $d.1
              3c               3c       b6     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(libc_state.o):(.debug_loclists)
              3c               3c        0     1                 $d.5
              f2               f2       bb     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(writev.o):(.debug_loclists)
              f2               f2        0     1                 $d.1
             1ad              1ad      361     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(atoi.o):(.debug_loclists)
             1ad              1ad        0     1                 $d.6
             50e              50e       2f     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(eabi.o):(.debug_loclists)
             50e              50e        0     1                 $d.2
             53d              53d       34     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(io_handle.o):(.debug_loclists)
             53d              53d        0     1                 $d.2
             571              571      996     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(printf.o):(.debug_loclists)
             571              571        0     1                 $d.14
             f07              f07       40     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(rand.o):(.debug_loclists)
             f07              f07        0     1                 $d.4
             f47              f47      5df     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stdio.o):(.debug_loclists)
             f47              f47        0     1                 $d.13
            1526             1526      203     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtol.o):(.debug_loclists)
            1526             1526        0     1                 $d.1
            1729             1729      203     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtoll.o):(.debug_loclists)
            1729             1729        0     1                 $d.1
            192c             192c       77     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(locale_stubs.o):(.debug_loclists)
            192c             192c        0     1                 $d.6
            19a3             19a3       1e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(atexit.o):(.debug_loclists)
            19a3             19a3        0     1                 $d.1
            19c1             19c1       e8     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(pthreads.o):(.debug_loclists)
            19c1             19c1        0     1                 $d.12
            1aa9             1aa9       34     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isalnum.o):(.debug_loclists)
            1aa9             1aa9        0     1                 $d.2
            1add             1add       34     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isalpha.o):(.debug_loclists)
            1add             1add        0     1                 $d.2
            1b11             1b11       1e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isascii.o):(.debug_loclists)
            1b11             1b11        0     1                 $d.1
            1b2f             1b2f       34     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isblank.o):(.debug_loclists)
            1b2f             1b2f        0     1                 $d.2
            1b63             1b63       34     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(iscntrl.o):(.debug_loclists)
            1b63             1b63        0     1                 $d.2
            1b97             1b97       34     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isdigit.o):(.debug_loclists)
            1b97             1b97        0     1                 $d.2
            1bcb             1bcb       34     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isgraph.o):(.debug_loclists)
            1bcb             1bcb        0     1                 $d.2
            1bff             1bff       34     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(islower.o):(.debug_loclists)
            1bff             1bff        0     1                 $d.2
            1c33             1c33       34     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isprint.o):(.debug_loclists)
            1c33             1c33        0     1                 $d.2
            1c67             1c67       58     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(ispunct.o):(.debug_loclists)
            1c67             1c67        0     1                 $d.2
            1cbf             1cbf       34     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isspace.o):(.debug_loclists)
            1cbf             1cbf        0     1                 $d.2
            1cf3             1cf3       34     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isupper.o):(.debug_loclists)
            1cf3             1cf3        0     1                 $d.2
            1d27             1d27       34     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(isxdigit.o):(.debug_loclists)
            1d27             1d27        0     1                 $d.2
            1d5b             1d5b       1e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(toascii.o):(.debug_loclists)
            1d5b             1d5b        0     1                 $d.1
            1d79             1d79       34     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(tolower.o):(.debug_loclists)
            1d79             1d79        0     1                 $d.2
            1dad             1dad       34     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(toupper.o):(.debug_loclists)
            1dad             1dad        0     1                 $d.2
            1de1             1de1       1e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(abs.o):(.debug_loclists)
            1de1             1de1        0     1                 $d.1
            1dff             1dff       75     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bsearch.o):(.debug_loclists)
            1dff             1dff        0     1                 $d.1
            1e74             1e74       1e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(div.o):(.debug_loclists)
            1e74             1e74        0     1                 $d.1
            1e92             1e92       1e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(imaxabs.o):(.debug_loclists)
            1e92             1e92        0     1                 $d.1
            1eb0             1eb0       30     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(imaxdiv.o):(.debug_loclists)
            1eb0             1eb0        0     1                 $d.1
            1ee0             1ee0       1e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(labs.o):(.debug_loclists)
            1ee0             1ee0        0     1                 $d.1
            1efe             1efe       30     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(ldiv.o):(.debug_loclists)
            1efe             1efe        0     1                 $d.1
            1f2e             1f2e       1e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(llabs.o):(.debug_loclists)
            1f2e             1f2e        0     1                 $d.1
            1f4c             1f4c       30     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(lldiv.o):(.debug_loclists)
            1f4c             1f4c        0     1                 $d.1
            1f7c             1f7c      635     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(qsort.o):(.debug_loclists)
            1f7c             1f7c        0     1                 $d.3
            25b1             25b1      13d     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memccpy.o):(.debug_loclists)
            25b1             25b1        0     1                 $d.1
            26ee             26ee      49c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memmem.o):(.debug_loclists)
            26ee             26ee        0     1                 $d.3
            2b8a             2b8a       4c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(mempcpy.o):(.debug_loclists)
            2b8a             2b8a        0     1                 $d.1
            2bd6             2bd6       57     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memrchr.o):(.debug_loclists)
            2bd6             2bd6        0     1                 $d.1
            2c2d             2c2d       b9     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stpcpy.o):(.debug_loclists)
            2c2d             2c2d        0     1                 $d.1
            2ce6             2ce6       c7     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(stpncpy.o):(.debug_loclists)
            2ce6             2ce6        0     1                 $d.1
            2dad             2dad       d5     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcasecmp.o):(.debug_loclists)
            2dad             2dad        0     1                 $d.2
            2e82             2e82       41     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcasestr.o):(.debug_loclists)
            2e82             2e82        0     1                 $d.1
            2ec3             2ec3       7e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strchrnul.o):(.debug_loclists)
            2ec3             2ec3        0     1                 $d.1
            2f41             2f41       4a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcspn.o):(.debug_loclists)
            2f41             2f41        0     1                 $d.1
            2f8b             2f8b       71     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strerror_r.o):(.debug_loclists)
            2f8b             2f8b        0     1                 $d.1
            2ffc             2ffc      147     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncasecmp.o):(.debug_loclists)
            2ffc             2ffc        0     1                 $d.2
            3143             3143       49     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strndup.o):(.debug_loclists)
            3143             3143        0     1                 $d.1
            318c             318c       41     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strsep.o):(.debug_loclists)
            318c             318c        0     1                 $d.1
            31cd             31cd       4b     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtok_r.o):(.debug_loclists)
            31cd             31cd        0     1                 $d.1
            3218             3218      12c     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strverscmp.o):(.debug_loclists)
            3218             3218        0     1                 $d.1
            3344             3344       40     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(swab.o):(.debug_loclists)
            3344             3344        0     1                 $d.1
            3384             3384       20     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_close.o):(.debug_loclists)
            3384             3384        0     1                 $d.2
            33a4             33a4       1b     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_read.o):(.debug_loclists)
            33a4             33a4        0     1                 $d.1
            33bf             33bf       d8     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_write.o):(.debug_loclists)
            33bf             33bf        0     1                 $d.1
            3497             3497       1e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(__stdio_seek.o):(.debug_loclists)
            3497             3497        0     1                 $d.1
            34b5             34b5       c5     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(mbtowc.o):(.debug_loclists)
            34b5             34b5        0     1                 $d.1
            357a             357a       a0     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(wcrtomb.o):(.debug_loclists)
            357a             357a        0     1                 $d.1
            361a             361a       2f     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bcopy.o):(.debug_loclists)
            361a             361a        0     1                 $d.1
            3649             3649       1b     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(bzero.o):(.debug_loclists)
            3649             3649        0     1                 $d.1
            3664             3664       45     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memchr.o):(.debug_loclists)
            3664             3664        0     1                 $d.1
            36a9             36a9       57     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memcmp.o):(.debug_loclists)
            36a9             36a9        0     1                 $d.1
            3700             3700      16f     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memcpy.o):(.debug_loclists)
            3700             3700        0     1                 $d.1
            386f             386f      269     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memmove.o):(.debug_loclists)
            386f             386f        0     1                 $d.1
            3ad8             3ad8       c9     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(memset.o):(.debug_loclists)
            3ad8             3ad8        0     1                 $d.1
            3ba1             3ba1       27     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcat.o):(.debug_loclists)
            3ba1             3ba1        0     1                 $d.1
            3bc8             3bc8       16     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strchr.o):(.debug_loclists)
            3bc8             3bc8        0     1                 $d.1
            3bde             3bde       55     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcmp.o):(.debug_loclists)
            3bde             3bde        0     1                 $d.1
            3c33             3c33       20     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strcpy.o):(.debug_loclists)
            3c33             3c33        0     1                 $d.1
            3c53             3c53       37     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strdup.o):(.debug_loclists)
            3c53             3c53        0     1                 $d.1
            3c8a             3c8a       1e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strerror.o):(.debug_loclists)
            3c8a             3c8a        0     1                 $d.2
            3ca8             3ca8       87     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strlcat.o):(.debug_loclists)
            3ca8             3ca8        0     1                 $d.1
            3d2f             3d2f       6f     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strlcpy.o):(.debug_loclists)
            3d2f             3d2f        0     1                 $d.1
            3d9e             3d9e       2e     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strlen.o):(.debug_loclists)
            3d9e             3d9e        0     1                 $d.1
            3dcc             3dcc       38     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncat.o):(.debug_loclists)
            3dcc             3dcc        0     1                 $d.1
            3e04             3e04       31     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncpy.o):(.debug_loclists)
            3e04             3e04        0     1                 $d.1
            3e35             3e35       bc     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strncmp.o):(.debug_loclists)
            3e35             3e35        0     1                 $d.1
            3ef1             3ef1       96     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strnicmp.o):(.debug_loclists)
            3ef1             3ef1        0     1                 $d.1
            3f87             3f87       2d     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strnlen.o):(.debug_loclists)
            3f87             3f87        0     1                 $d.1
            3fb4             3fb4       52     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strpbrk.o):(.debug_loclists)
            3fb4             3fb4        0     1                 $d.1
            4006             4006       20     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strrchr.o):(.debug_loclists)
            4006             4006        0     1                 $d.1
            4026             4026       64     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strspn.o):(.debug_loclists)
            4026             4026        0     1                 $d.1
            408a             408a       5a     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strstr.o):(.debug_loclists)
            408a             408a        0     1                 $d.1
            40e4             40e4       41     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strtok.o):(.debug_loclists)
            40e4             40e4        0     1                 $d.2
            4125             4125       65     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(strxfrm.o):(.debug_loclists)
            4125             4125        0     1                 $d.1
            418a             418a       20     1         out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a(pure_virtual.o):(.debug_loclists)
            418a             418a        0     1                 $d.3
            41aa             41aa    2cddc     1         lto.tmp:(.debug_loclists)
            41aa             41aa        0     1                 $d.725
               0                0    16920     8 .symtab
               0                0    16920     8         <internal>:(.symtab)
               0                0      199     1 .shstrtab
               0                0      199     1         <internal>:(.shstrtab)
               0                0     9479     1 .strtab
               0                0     9479     1         <internal>:(.strtab)
