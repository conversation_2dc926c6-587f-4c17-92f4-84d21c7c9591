#ifndef __out_build_imx8mp_kernel_hardware_nxp_platform_imx_module_config_h_H
#define __out_build_imx8mp_kernel_hardware_nxp_platform_imx_module_config_h_H
#define WITH_CAAM_SUPPORT 1
#define MODULE_COMPILEFLAGS "_FVISIBILITY HIDDEN__FLTO FULL__FVIRTUAL_FUNCTION_ELIMINATION__FVISIBILITY_INLINES_HIDDEN___FSANITIZE_BLACKLIST KERNEL_RCTEE_LIB_UBSAN_EXEMPTLIST__FSANITIZE CFI__DCFI_ENABLED"
#define MODULE_CFLAGS "_WNO_STRICT_PROTOTYPES"
#define MODULE_CPPFLAGS ""
#define MODULE_ASMFLAGS ""
#define MODULE_LDFLAGS ""
#define MODULE_OPTFLAGS ""
#define MODULE_INCLUDES "KERNEL_RCTEE_SMC_INCLUDE_KERNEL_HARDWARE_NXP_PLATFORM_IMX_LIB_"
#define MODULE_SRCDEPS "OUT_BUILD_IMX8MP_TOOLCHAIN_CONFIG"
#define MODULE_DEPS "DEV_INTERRUPT_ARM_GIC_DEV_TIMER_ARM_GENERIC_"
#define MODULE_SRCS "KERNEL_HARDWARE_NXP_PLATFORM_IMX_DEBUG_C_KERNEL_HARDWARE_NXP_PLATFORM_IMX_PLATFORM_C_KERNEL_HARDWARE_NXP_PLATFORM_IMX_SMC_SERVICE_ACCESS_POLICY_C_KERNEL_HARDWARE_NXP_PLATFORM_IMX_APPLOADER_MMIO_APPS_C_KERNEL_HARDWARE_NXP_PLATFORM_IMX_DRIVERS_IMX_CAAM_C_KERNEL_HARDWARE_NXP_PLATFORM_IMX_DRIVERS_IMX_CSU_C_KERNEL_HARDWARE_NXP_PLATFORM_IMX_DRIVERS_IMX_LCDIF_C_KERNEL_HARDWARE_NXP_PLATFORM_IMX_DRIVERS_IMX_UART_C_KERNEL_HARDWARE_NXP_PLATFORM_IMX_DRIVERS_IMX_SNVS_C_KERNEL_HARDWARE_NXP_PLATFORM_IMX_DRIVERS_IMX_VPU_C_KERNEL_HARDWARE_NXP_PLATFORM_IMX_DRIVERS_IMX_VPU_ENC_C"
#endif
