out/build-imx8mp/kernel/hardware/nxp/platform/imx/drivers/imx_uart.o: \
  kernel/rctee/lib/ubsan/exemptlist \
  /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt \
  kernel/hardware/nxp/platform/imx/drivers/imx_uart.c \
  out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/hardware/nxp/platform/imx/module_config.h \
  kernel/lk/include/debug.h opensource_libs/musl/include/stddef.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  opensource_libs/musl/include/stdio.h \
  opensource_libs/musl/include/features.h \
  kernel/lk/include/shared/lk/compiler.h kernel/lk/include/panic.h \
  kernel/lk/include/platform/debug.h \
  opensource_libs/musl/include/sys/types.h kernel/lk/include/lk/types.h \
  opensource_libs/musl/include/inttypes.h \
  opensource_libs/musl/include/stdint.h \
  opensource_libs/musl/arch/aarch64/bits/stdint.h \
  opensource_libs/musl/include/limits.h \
  opensource_libs/musl/arch/aarch64/bits/limits.h \
  opensource_libs/musl/include/stdbool.h \
  opensource_libs/musl/include/endian.h \
  opensource_libs/musl/include/sys/select.h \
  opensource_libs/musl/include/stdarg.h kernel/lk/include/dev/uart.h \
  kernel/hardware/nxp/platform/imx/soc/imx8mp/include/imx-regs.h \
  kernel/lk/include/shared/lk/reg.h

kernel/rctee/lib/ubsan/exemptlist:

/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt:

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/hardware/nxp/platform/imx/module_config.h:

kernel/lk/include/debug.h:

opensource_libs/musl/include/stddef.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

opensource_libs/musl/include/stdio.h:

opensource_libs/musl/include/features.h:

kernel/lk/include/shared/lk/compiler.h:

kernel/lk/include/panic.h:

kernel/lk/include/platform/debug.h:

opensource_libs/musl/include/sys/types.h:

kernel/lk/include/lk/types.h:

opensource_libs/musl/include/inttypes.h:

opensource_libs/musl/include/stdint.h:

opensource_libs/musl/arch/aarch64/bits/stdint.h:

opensource_libs/musl/include/limits.h:

opensource_libs/musl/arch/aarch64/bits/limits.h:

opensource_libs/musl/include/stdbool.h:

opensource_libs/musl/include/endian.h:

opensource_libs/musl/include/sys/select.h:

opensource_libs/musl/include/stdarg.h:

kernel/lk/include/dev/uart.h:

kernel/hardware/nxp/platform/imx/soc/imx8mp/include/imx-regs.h:

kernel/lk/include/shared/lk/reg.h:
