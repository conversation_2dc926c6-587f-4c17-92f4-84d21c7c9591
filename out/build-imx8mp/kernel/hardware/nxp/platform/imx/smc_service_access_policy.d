out/build-imx8mp/kernel/hardware/nxp/platform/imx/smc_service_access_policy.o: \
  kernel/rctee/lib/ubsan/exemptlist \
  /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt \
  kernel/hardware/nxp/platform/imx/smc_service_access_policy.c \
  out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/hardware/nxp/platform/imx/module_config.h \
  kernel/lk/include/uapi/uapi/err.h \
  kernel/rctee/services/smc/include/services/smc/acl.h \
  user/base/interface/smc/include/interface/smc/smc.h \
  opensource_libs/musl/include/stdint.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  opensource_libs/musl/arch/aarch64/bits/stdint.h \
  kernel/rctee/include/uapi/uapi/rctee_uuid.h \
  opensource_libs/musl/include/stdbool.h \
  opensource_libs/musl/include/string.h \
  opensource_libs/musl/include/features.h \
  opensource_libs/musl/include/strings.h

kernel/rctee/lib/ubsan/exemptlist:

/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt:

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/hardware/nxp/platform/imx/module_config.h:

kernel/lk/include/uapi/uapi/err.h:

kernel/rctee/services/smc/include/services/smc/acl.h:

user/base/interface/smc/include/interface/smc/smc.h:

opensource_libs/musl/include/stdint.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

opensource_libs/musl/arch/aarch64/bits/stdint.h:

kernel/rctee/include/uapi/uapi/rctee_uuid.h:

opensource_libs/musl/include/stdbool.h:

opensource_libs/musl/include/string.h:

opensource_libs/musl/include/features.h:

opensource_libs/musl/include/strings.h:
