out/build-imx8mp/kernel/hardware/nxp/platform/imx/platform.o: \
  kernel/rctee/lib/ubsan/exemptlist \
  /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt \
  kernel/hardware/nxp/platform/imx/platform.c out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/hardware/nxp/platform/imx/module_config.h \
  kernel/lk/include/debug.h opensource_libs/musl/include/stddef.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  opensource_libs/musl/include/stdio.h \
  opensource_libs/musl/include/features.h \
  kernel/lk/include/shared/lk/compiler.h kernel/lk/include/panic.h \
  kernel/lk/include/platform/debug.h \
  opensource_libs/musl/include/sys/types.h kernel/lk/include/lk/types.h \
  opensource_libs/musl/include/inttypes.h \
  opensource_libs/musl/include/stdint.h \
  opensource_libs/musl/arch/aarch64/bits/stdint.h \
  opensource_libs/musl/include/limits.h \
  opensource_libs/musl/arch/aarch64/bits/limits.h \
  opensource_libs/musl/include/stdbool.h \
  opensource_libs/musl/include/endian.h \
  opensource_libs/musl/include/sys/select.h \
  opensource_libs/musl/include/stdarg.h \
  kernel/lk/dev/interrupt/arm_gic/include/dev/interrupt/arm_gic.h \
  kernel/lk/dev/timer/arm_generic/include/dev/timer/arm_generic.h \
  kernel/hardware/nxp/platform/imx/soc/imx8mp/include/imx-regs.h \
  kernel/lk/include/kernel/vm.h kernel/lk/include/shared/lk/list.h \
  kernel/lk/include/shared/lk/macros.h \
  opensource_libs/musl/include/stdlib.h \
  opensource_libs/musl/include/alloca.h kernel/lk/include/arch.h \
  kernel/lk/arch/arm64/include/arch/defines.h \
  kernel/lk/include/arch/mmu.h \
  kernel/lk/arch/arm64/include/arch/aspace.h \
  kernel/lk/arch/arm64/include/arch/arm64/mmu.h \
  kernel/lk/lib/libc/include_common/assert.h \
  kernel/lk/arch/arm64/include/arch/arm64.h \
  kernel/lk/include/kernel/asid.h \
  kernel/lk/arch/arm64/include/arch/tbi.h \
  kernel/lk/include/kernel/vm_obj.h \
  kernel/rctee/include/shared/lk/reflist.h \
  kernel/lk/lib/binary_search_tree/include/lib/binary_search_tree.h \
  kernel/lk/include/lk/init.h opensource_libs/musl/include/string.h \
  opensource_libs/musl/include/strings.h

kernel/rctee/lib/ubsan/exemptlist:

/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt:

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/hardware/nxp/platform/imx/module_config.h:

kernel/lk/include/debug.h:

opensource_libs/musl/include/stddef.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

opensource_libs/musl/include/stdio.h:

opensource_libs/musl/include/features.h:

kernel/lk/include/shared/lk/compiler.h:

kernel/lk/include/panic.h:

kernel/lk/include/platform/debug.h:

opensource_libs/musl/include/sys/types.h:

kernel/lk/include/lk/types.h:

opensource_libs/musl/include/inttypes.h:

opensource_libs/musl/include/stdint.h:

opensource_libs/musl/arch/aarch64/bits/stdint.h:

opensource_libs/musl/include/limits.h:

opensource_libs/musl/arch/aarch64/bits/limits.h:

opensource_libs/musl/include/stdbool.h:

opensource_libs/musl/include/endian.h:

opensource_libs/musl/include/sys/select.h:

opensource_libs/musl/include/stdarg.h:

kernel/lk/dev/interrupt/arm_gic/include/dev/interrupt/arm_gic.h:

kernel/lk/dev/timer/arm_generic/include/dev/timer/arm_generic.h:

kernel/hardware/nxp/platform/imx/soc/imx8mp/include/imx-regs.h:

kernel/lk/include/kernel/vm.h:

kernel/lk/include/shared/lk/list.h:

kernel/lk/include/shared/lk/macros.h:

opensource_libs/musl/include/stdlib.h:

opensource_libs/musl/include/alloca.h:

kernel/lk/include/arch.h:

kernel/lk/arch/arm64/include/arch/defines.h:

kernel/lk/include/arch/mmu.h:

kernel/lk/arch/arm64/include/arch/aspace.h:

kernel/lk/arch/arm64/include/arch/arm64/mmu.h:

kernel/lk/lib/libc/include_common/assert.h:

kernel/lk/arch/arm64/include/arch/arm64.h:

kernel/lk/include/kernel/asid.h:

kernel/lk/arch/arm64/include/arch/tbi.h:

kernel/lk/include/kernel/vm_obj.h:

kernel/rctee/include/shared/lk/reflist.h:

kernel/lk/lib/binary_search_tree/include/lib/binary_search_tree.h:

kernel/lk/include/lk/init.h:

opensource_libs/musl/include/string.h:

opensource_libs/musl/include/strings.h:
