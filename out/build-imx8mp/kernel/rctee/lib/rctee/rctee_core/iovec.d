out/build-imx8mp/kernel/rctee/lib/rctee/rctee_core/iovec.o: \
  kernel/rctee/lib/ubsan/exemptlist \
  /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt \
  kernel/rctee/lib/rctee/rctee_core/iovec.c out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/rctee/lib/rctee/module_config.h \
  kernel/lk/include/uapi/uapi/err.h \
  kernel/rctee/lib/rctee/include/lib/rctee/uio.h \
  kernel/lk/include/kernel/usercopy.h \
  kernel/lk/include/shared/lk/compiler.h \
  opensource_libs/musl/include/stddef.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  opensource_libs/musl/include/sys/types.h \
  opensource_libs/musl/include/features.h kernel/lk/include/lk/types.h \
  opensource_libs/musl/include/inttypes.h \
  opensource_libs/musl/include/stdint.h \
  opensource_libs/musl/arch/aarch64/bits/stdint.h \
  opensource_libs/musl/include/limits.h \
  opensource_libs/musl/arch/aarch64/bits/limits.h \
  opensource_libs/musl/include/stdbool.h \
  opensource_libs/musl/include/endian.h \
  opensource_libs/musl/include/sys/select.h \
  kernel/lk/include/arch/usercopy.h \
  opensource_libs/musl/include/string.h \
  opensource_libs/musl/include/strings.h

kernel/rctee/lib/ubsan/exemptlist:

/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt:

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/rctee/lib/rctee/module_config.h:

kernel/lk/include/uapi/uapi/err.h:

kernel/rctee/lib/rctee/include/lib/rctee/uio.h:

kernel/lk/include/kernel/usercopy.h:

kernel/lk/include/shared/lk/compiler.h:

opensource_libs/musl/include/stddef.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

opensource_libs/musl/include/sys/types.h:

opensource_libs/musl/include/features.h:

kernel/lk/include/lk/types.h:

opensource_libs/musl/include/inttypes.h:

opensource_libs/musl/include/stdint.h:

opensource_libs/musl/arch/aarch64/bits/stdint.h:

opensource_libs/musl/include/limits.h:

opensource_libs/musl/arch/aarch64/bits/limits.h:

opensource_libs/musl/include/stdbool.h:

opensource_libs/musl/include/endian.h:

opensource_libs/musl/include/sys/select.h:

kernel/lk/include/arch/usercopy.h:

opensource_libs/musl/include/string.h:

opensource_libs/musl/include/strings.h:
