out/build-imx8mp/kernel/rctee/lib/rctee/rctee_core/ipc.o: \
  kernel/rctee/lib/ubsan/exemptlist \
  /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt \
  kernel/rctee/lib/rctee/rctee_core/ipc.c out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/rctee/lib/rctee/module_config.h \
  kernel/lk/lib/libc/include_common/assert.h \
  kernel/lk/include/shared/lk/compiler.h \
  opensource_libs/musl/include/stddef.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  kernel/lk/include/panic.h kernel/lk/include/uapi/uapi/err.h \
  kernel/lk/include/kernel/usercopy.h \
  opensource_libs/musl/include/sys/types.h \
  opensource_libs/musl/include/features.h kernel/lk/include/lk/types.h \
  opensource_libs/musl/include/inttypes.h \
  opensource_libs/musl/include/stdint.h \
  opensource_libs/musl/arch/aarch64/bits/stdint.h \
  opensource_libs/musl/include/limits.h \
  opensource_libs/musl/arch/aarch64/bits/limits.h \
  opensource_libs/musl/include/stdbool.h \
  opensource_libs/musl/include/endian.h \
  opensource_libs/musl/include/sys/select.h \
  kernel/lk/include/arch/usercopy.h kernel/lk/include/shared/lk/list.h \
  kernel/lk/include/shared/lk/macros.h kernel/lk/include/platform.h \
  opensource_libs/musl/include/stdlib.h \
  opensource_libs/musl/include/alloca.h \
  opensource_libs/musl/include/string.h \
  opensource_libs/musl/include/strings.h \
  kernel/lk/include/shared/lk/trace.h \
  opensource_libs/musl/include/stdio.h kernel/lk/include/kernel/event.h \
  kernel/lk/include/kernel/thread.h \
  kernel/lk/arch/arm64/include/arch/defines.h \
  kernel/lk/include/arch/ops.h \
  kernel/lk/arch/arm64/include/arch/arch_ops.h \
  kernel/lk/include/shared/lk/reg.h \
  kernel/lk/arch/arm64/include/arch/arm64.h \
  kernel/lk/include/arch/thread.h \
  kernel/lk/arch/arm64/include/arch/arch_thread.h \
  kernel/lk/include/kernel/wait.h kernel/lk/include/kernel/spinlock.h \
  kernel/lk/arch/arm64/include/arch/spinlock.h \
  /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/include/stdatomic.h \
  kernel/lk/include/sys/cdefs.h \
  /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/include/bits/stdatomic.h \
  kernel/lk/include/debug.h kernel/lk/include/platform/debug.h \
  opensource_libs/musl/include/stdarg.h kernel/lk/include/kernel/mutex.h \
  kernel/lk/include/lk/init.h \
  kernel/rctee/lib/syscall/include/lib/syscall.h \
  kernel/rctee/lib/rctee/include/lib/rctee/uuid.h \
  kernel/rctee/include/uapi/uapi/rctee_uuid.h \
  kernel/rctee/lib/rctee/include/lib/rctee/event.h \
  kernel/rctee/include/shared/lk/reflist.h \
  kernel/rctee/lib/rctee/include/lib/rctee/handle.h \
  kernel/rctee/lib/rctee/include/lib/rctee/refcount.h \
  kernel/rctee/include/uapi/uapi/rctee_uevent.h \
  kernel/rctee/lib/rctee/include/lib/rctee/ipc.h \
  kernel/lk/include/bits.h \
  kernel/rctee/lib/rctee/include/lib/rctee/ipc_msg.h \
  kernel/rctee/include/uapi/uapi/rctee_ipc.h \
  kernel/rctee/lib/rctee/include/lib/rctee/uctx.h \
  kernel/rctee/lib/rctee/include/lib/rctee/sys_fd.h \
  kernel/rctee/lib/rctee/include/lib/rctee/uio.h \
  kernel/rctee/lib/rctee/include/lib/rctee/rctee_app.h \
  kernel/lk/include/kernel/physmem.h kernel/lk/include/kernel/vm_obj.h \
  kernel/lk/include/kernel/vm.h kernel/lk/include/arch.h \
  kernel/lk/include/arch/mmu.h \
  kernel/lk/arch/arm64/include/arch/aspace.h \
  kernel/lk/arch/arm64/include/arch/arm64/mmu.h \
  kernel/lk/include/kernel/asid.h \
  kernel/lk/arch/arm64/include/arch/tbi.h \
  kernel/lk/lib/binary_search_tree/include/lib/binary_search_tree.h \
  kernel/rctee/lib/libc-ext/include/trusty/uuid.h

kernel/rctee/lib/ubsan/exemptlist:

/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt:

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/rctee/lib/rctee/module_config.h:

kernel/lk/lib/libc/include_common/assert.h:

kernel/lk/include/shared/lk/compiler.h:

opensource_libs/musl/include/stddef.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

kernel/lk/include/panic.h:

kernel/lk/include/uapi/uapi/err.h:

kernel/lk/include/kernel/usercopy.h:

opensource_libs/musl/include/sys/types.h:

opensource_libs/musl/include/features.h:

kernel/lk/include/lk/types.h:

opensource_libs/musl/include/inttypes.h:

opensource_libs/musl/include/stdint.h:

opensource_libs/musl/arch/aarch64/bits/stdint.h:

opensource_libs/musl/include/limits.h:

opensource_libs/musl/arch/aarch64/bits/limits.h:

opensource_libs/musl/include/stdbool.h:

opensource_libs/musl/include/endian.h:

opensource_libs/musl/include/sys/select.h:

kernel/lk/include/arch/usercopy.h:

kernel/lk/include/shared/lk/list.h:

kernel/lk/include/shared/lk/macros.h:

kernel/lk/include/platform.h:

opensource_libs/musl/include/stdlib.h:

opensource_libs/musl/include/alloca.h:

opensource_libs/musl/include/string.h:

opensource_libs/musl/include/strings.h:

kernel/lk/include/shared/lk/trace.h:

opensource_libs/musl/include/stdio.h:

kernel/lk/include/kernel/event.h:

kernel/lk/include/kernel/thread.h:

kernel/lk/arch/arm64/include/arch/defines.h:

kernel/lk/include/arch/ops.h:

kernel/lk/arch/arm64/include/arch/arch_ops.h:

kernel/lk/include/shared/lk/reg.h:

kernel/lk/arch/arm64/include/arch/arm64.h:

kernel/lk/include/arch/thread.h:

kernel/lk/arch/arm64/include/arch/arch_thread.h:

kernel/lk/include/kernel/wait.h:

kernel/lk/include/kernel/spinlock.h:

kernel/lk/arch/arm64/include/arch/spinlock.h:

/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/include/stdatomic.h:

kernel/lk/include/sys/cdefs.h:

/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/include/bits/stdatomic.h:

kernel/lk/include/debug.h:

kernel/lk/include/platform/debug.h:

opensource_libs/musl/include/stdarg.h:

kernel/lk/include/kernel/mutex.h:

kernel/lk/include/lk/init.h:

kernel/rctee/lib/syscall/include/lib/syscall.h:

kernel/rctee/lib/rctee/include/lib/rctee/uuid.h:

kernel/rctee/include/uapi/uapi/rctee_uuid.h:

kernel/rctee/lib/rctee/include/lib/rctee/event.h:

kernel/rctee/include/shared/lk/reflist.h:

kernel/rctee/lib/rctee/include/lib/rctee/handle.h:

kernel/rctee/lib/rctee/include/lib/rctee/refcount.h:

kernel/rctee/include/uapi/uapi/rctee_uevent.h:

kernel/rctee/lib/rctee/include/lib/rctee/ipc.h:

kernel/lk/include/bits.h:

kernel/rctee/lib/rctee/include/lib/rctee/ipc_msg.h:

kernel/rctee/include/uapi/uapi/rctee_ipc.h:

kernel/rctee/lib/rctee/include/lib/rctee/uctx.h:

kernel/rctee/lib/rctee/include/lib/rctee/sys_fd.h:

kernel/rctee/lib/rctee/include/lib/rctee/uio.h:

kernel/rctee/lib/rctee/include/lib/rctee/rctee_app.h:

kernel/lk/include/kernel/physmem.h:

kernel/lk/include/kernel/vm_obj.h:

kernel/lk/include/kernel/vm.h:

kernel/lk/include/arch.h:

kernel/lk/include/arch/mmu.h:

kernel/lk/arch/arm64/include/arch/aspace.h:

kernel/lk/arch/arm64/include/arch/arm64/mmu.h:

kernel/lk/include/kernel/asid.h:

kernel/lk/arch/arm64/include/arch/tbi.h:

kernel/lk/lib/binary_search_tree/include/lib/binary_search_tree.h:

kernel/rctee/lib/libc-ext/include/trusty/uuid.h:
