#ifndef __out_build_imx8mp_kernel_rctee_lib_rctee_module_config_h_H
#define __out_build_imx8mp_kernel_rctee_lib_rctee_module_config_h_H
#define MODULE_COMPILEFLAGS ""
#define MODU<PERSON>_CFLAGS ""
#define MOD<PERSON>LE_CPPFLAGS ""
#define MODULE_ASMFLAGS ""
#define MODULE_LDFLAGS ""
#define MODULE_OPTFLAGS ""
#define MODULE_INCLUDES ""
#define MODULE_SRCDEPS ""
#define MODULE_DEPS "_KERNEL_RCTEE_LIB_EXTMEM_KERNEL_RCTEE_LIB_SM__KERNEL_RCTEE_LIB_SYSCALL_KERNEL_RCTEE_LIB_APP_MANIFEST_KERNEL_RCTEE_LIB_BACKTRACE_KERNEL_RCTEE_LIB_LIBC_EXT_KERNEL_RCTEE_LIB_RAND_KERNEL_RCTEE_LIB_VERSION_"
#define M<PERSON><PERSON><PERSON>_SRCS "KERNEL_RCTEE_LIB_RCTEE_RCTEE_CORE_EVENT_C_KERNEL_RCTEE_LIB_RCTEE_RCTEE_CORE_HANDLE_C_KERNEL_RCTEE_LIB_RCTEE_RCTEE_CORE_HANDLE_SET_C_KERNEL_RCTEE_LIB_RCTEE_RCTEE_CORE_IOVEC_C_KERNEL_RCTEE_LIB_RCTEE_RCTEE_CORE_IPC_C_KERNEL_RCTEE_LIB_RCTEE_RCTEE_CORE_IPC_MSG_C_KERNEL_RCTEE_LIB_RCTEE_RCTEE_CORE_MEMREF_C_KERNEL_RCTEE_LIB_RCTEE_RCTEE_CORE_SYSCALL_C_KERNEL_RCTEE_LIB_RCTEE_RCTEE_CORE_UCTX_C_KERNEL_RCTEE_LIB_RCTEE_RCTEE_CORE_UIRQ_C_KERNEL_RCTEE_LIB_RCTEE_RCTEE_CORE_UTIL_C_KERNEL_RCTEE_LIB_RCTEE_RCTEE_CORE_UUID_C_KERNEL_RCTEE_LIB_RCTEE_RCTEE_CORE_RCTEE_APP_C__KERNEL_RCTEE_LIB_RCTEE_RCTEE_VITIO_VQUEUE_C_KERNEL_RCTEE_LIB_RCTEE_RCTEE_VITIO_SMCALL_C_KERNEL_RCTEE_LIB_RCTEE_RCTEE_VITIO_RCTEE_VIRTIO_C_KERNEL_RCTEE_LIB_RCTEE_RCTEE_VITIO_RCIPC_VIRTIO_DEV_C_KERNEL_RCTEE_LIB_RCTEE_RCTEE_VITIO_RCIPC_DEV_QL_C_KERNEL_RCTEE_LIB_RCTEE_RCIPC_CONFIG_C"
#endif
