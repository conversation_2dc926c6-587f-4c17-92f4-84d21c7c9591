out/build-imx8mp/kernel/rctee/lib/rctee/rcipc_config.o: \
  kernel/rctee/lib/ubsan/exemptlist \
  /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt \
  kernel/rctee/lib/rctee/rcipc_config.c out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/rctee/lib/rctee/module_config.h \
  kernel/lk/lib/libc/include_common/assert.h \
  kernel/lk/include/shared/lk/compiler.h \
  opensource_libs/musl/include/stddef.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  kernel/lk/include/panic.h kernel/lk/include/uapi/uapi/err.h \
  kernel/lk/include/shared/lk/trace.h \
  opensource_libs/musl/include/stdio.h \
  opensource_libs/musl/include/features.h \
  kernel/rctee/lib/rctee/include/lib/rctee/rcipc_virtio_dev.h \
  kernel/lk/arch/arm64/include/arch/defines.h \
  kernel/rctee/lib/rctee/include/lib/rctee/uuid.h \
  kernel/rctee/include/uapi/uapi/rctee_uuid.h \
  opensource_libs/musl/include/stdint.h \
  opensource_libs/musl/arch/aarch64/bits/stdint.h \
  opensource_libs/headers/include/remoteproc/remoteproc.h \
  kernel/rctee/include/linux/types.h \
  opensource_libs/musl/include/sys/types.h kernel/lk/include/lk/types.h \
  opensource_libs/musl/include/inttypes.h \
  opensource_libs/musl/include/limits.h \
  opensource_libs/musl/arch/aarch64/bits/limits.h \
  opensource_libs/musl/include/stdbool.h \
  opensource_libs/musl/include/endian.h \
  opensource_libs/musl/include/sys/select.h kernel/lk/include/lk/init.h \
  kernel/rctee/lib/rctee/include/rctee_virtio.h \
  kernel/rctee/lib/sm/include/lib/sm.h \
  kernel/rctee/lib/extmem/include/lib/extmem/extmem.h \
  kernel/lk/include/kernel/vm_obj.h \
  kernel/rctee/include/shared/lk/reflist.h \
  kernel/lk/include/shared/lk/list.h \
  kernel/lk/include/shared/lk/macros.h \
  kernel/lk/lib/binary_search_tree/include/lib/binary_search_tree.h \
  kernel/rctee/lib/sm/include/lib/sm/smcall.h \
  user/base/interface/smc/include/interface/smc/smc_def.h

kernel/rctee/lib/ubsan/exemptlist:

/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt:

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/rctee/lib/rctee/module_config.h:

kernel/lk/lib/libc/include_common/assert.h:

kernel/lk/include/shared/lk/compiler.h:

opensource_libs/musl/include/stddef.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

kernel/lk/include/panic.h:

kernel/lk/include/uapi/uapi/err.h:

kernel/lk/include/shared/lk/trace.h:

opensource_libs/musl/include/stdio.h:

opensource_libs/musl/include/features.h:

kernel/rctee/lib/rctee/include/lib/rctee/rcipc_virtio_dev.h:

kernel/lk/arch/arm64/include/arch/defines.h:

kernel/rctee/lib/rctee/include/lib/rctee/uuid.h:

kernel/rctee/include/uapi/uapi/rctee_uuid.h:

opensource_libs/musl/include/stdint.h:

opensource_libs/musl/arch/aarch64/bits/stdint.h:

opensource_libs/headers/include/remoteproc/remoteproc.h:

kernel/rctee/include/linux/types.h:

opensource_libs/musl/include/sys/types.h:

kernel/lk/include/lk/types.h:

opensource_libs/musl/include/inttypes.h:

opensource_libs/musl/include/limits.h:

opensource_libs/musl/arch/aarch64/bits/limits.h:

opensource_libs/musl/include/stdbool.h:

opensource_libs/musl/include/endian.h:

opensource_libs/musl/include/sys/select.h:

kernel/lk/include/lk/init.h:

kernel/rctee/lib/rctee/include/rctee_virtio.h:

kernel/rctee/lib/sm/include/lib/sm.h:

kernel/rctee/lib/extmem/include/lib/extmem/extmem.h:

kernel/lk/include/kernel/vm_obj.h:

kernel/rctee/include/shared/lk/reflist.h:

kernel/lk/include/shared/lk/list.h:

kernel/lk/include/shared/lk/macros.h:

kernel/lk/lib/binary_search_tree/include/lib/binary_search_tree.h:

kernel/rctee/lib/sm/include/lib/sm/smcall.h:

user/base/interface/smc/include/interface/smc/smc_def.h:
