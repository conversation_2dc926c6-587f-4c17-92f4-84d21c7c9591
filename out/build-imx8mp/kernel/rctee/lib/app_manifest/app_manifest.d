out/build-imx8mp/kernel/rctee/lib/app_manifest/app_manifest.o: \
  kernel/rctee/lib/ubsan/exemptlist \
  /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt \
  kernel/rctee/lib/app_manifest/app_manifest.c out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/rctee/lib/app_manifest/module_config.h \
  kernel/lk/lib/libc/include_common/assert.h \
  kernel/lk/include/shared/lk/compiler.h \
  opensource_libs/musl/include/stddef.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  kernel/lk/include/panic.h opensource_libs/musl/include/inttypes.h \
  opensource_libs/musl/include/features.h \
  opensource_libs/musl/include/stdint.h \
  opensource_libs/musl/arch/aarch64/bits/stdint.h \
  kernel/rctee/lib/app_manifest/include/lib/app_manifest/app_manifest.h \
  opensource_libs/musl/include/stdbool.h \
  kernel/rctee/include/uapi/uapi/rctee_uuid.h \
  kernel/lk/include/shared/lk/macros.h \
  opensource_libs/musl/include/string.h \
  opensource_libs/musl/include/strings.h \
  kernel/rctee/include/trusty_log.h kernel/lk/include/debug.h \
  opensource_libs/musl/include/stdio.h \
  kernel/lk/include/platform/debug.h \
  opensource_libs/musl/include/sys/types.h kernel/lk/include/lk/types.h \
  opensource_libs/musl/include/limits.h \
  opensource_libs/musl/arch/aarch64/bits/limits.h \
  opensource_libs/musl/include/endian.h \
  opensource_libs/musl/include/sys/select.h \
  opensource_libs/musl/include/stdarg.h \
  kernel/lk/include/uapi/uapi/err.h

kernel/rctee/lib/ubsan/exemptlist:

/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt:

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/rctee/lib/app_manifest/module_config.h:

kernel/lk/lib/libc/include_common/assert.h:

kernel/lk/include/shared/lk/compiler.h:

opensource_libs/musl/include/stddef.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

kernel/lk/include/panic.h:

opensource_libs/musl/include/inttypes.h:

opensource_libs/musl/include/features.h:

opensource_libs/musl/include/stdint.h:

opensource_libs/musl/arch/aarch64/bits/stdint.h:

kernel/rctee/lib/app_manifest/include/lib/app_manifest/app_manifest.h:

opensource_libs/musl/include/stdbool.h:

kernel/rctee/include/uapi/uapi/rctee_uuid.h:

kernel/lk/include/shared/lk/macros.h:

opensource_libs/musl/include/string.h:

opensource_libs/musl/include/strings.h:

kernel/rctee/include/trusty_log.h:

kernel/lk/include/debug.h:

opensource_libs/musl/include/stdio.h:

kernel/lk/include/platform/debug.h:

opensource_libs/musl/include/sys/types.h:

kernel/lk/include/lk/types.h:

opensource_libs/musl/include/limits.h:

opensource_libs/musl/arch/aarch64/bits/limits.h:

opensource_libs/musl/include/endian.h:

opensource_libs/musl/include/sys/select.h:

opensource_libs/musl/include/stdarg.h:

kernel/lk/include/uapi/uapi/err.h:
