out/build-imx8mp/kernel/rctee/lib/libc-ext/uuid.o: \
  kernel/rctee/lib/ubsan/exemptlist \
  /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt \
  kernel/rctee/lib/libc-ext/uuid.c out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/rctee/lib/libc-ext/module_config.h \
  opensource_libs/musl/include/inttypes.h \
  opensource_libs/musl/include/features.h \
  opensource_libs/musl/include/stdint.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  opensource_libs/musl/arch/aarch64/bits/stdint.h \
  opensource_libs/musl/include/stdbool.h \
  opensource_libs/musl/include/stdio.h \
  opensource_libs/musl/include/string.h \
  opensource_libs/musl/include/strings.h \
  kernel/rctee/lib/libc-ext/include/trusty/uuid.h \
  kernel/rctee/include/uapi/uapi/rctee_uuid.h

kernel/rctee/lib/ubsan/exemptlist:

/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt:

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/rctee/lib/libc-ext/module_config.h:

opensource_libs/musl/include/inttypes.h:

opensource_libs/musl/include/features.h:

opensource_libs/musl/include/stdint.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

opensource_libs/musl/arch/aarch64/bits/stdint.h:

opensource_libs/musl/include/stdbool.h:

opensource_libs/musl/include/stdio.h:

opensource_libs/musl/include/string.h:

opensource_libs/musl/include/strings.h:

kernel/rctee/lib/libc-ext/include/trusty/uuid.h:

kernel/rctee/include/uapi/uapi/rctee_uuid.h:
