out/build-imx8mp/kernel/rctee/lib/libc-ext/scnprintf.o: \
  kernel/rctee/lib/ubsan/exemptlist \
  /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt \
  kernel/rctee/lib/libc-ext/scnprintf.c out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/rctee/lib/libc-ext/module_config.h \
  kernel/rctee/lib/libc-ext/include/trusty/string.h \
  kernel/lk/include/shared/lk/compiler.h \
  opensource_libs/musl/include/stddef.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  opensource_libs/musl/include/stdarg.h \
  opensource_libs/musl/include/stdio.h \
  opensource_libs/musl/include/features.h \
  opensource_libs/musl/include/stdlib.h \
  opensource_libs/musl/include/alloca.h

kernel/rctee/lib/ubsan/exemptlist:

/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt:

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/rctee/lib/libc-ext/module_config.h:

kernel/rctee/lib/libc-ext/include/trusty/string.h:

kernel/lk/include/shared/lk/compiler.h:

opensource_libs/musl/include/stddef.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

opensource_libs/musl/include/stdarg.h:

opensource_libs/musl/include/stdio.h:

opensource_libs/musl/include/features.h:

opensource_libs/musl/include/stdlib.h:

opensource_libs/musl/include/alloca.h:
