#ifndef __out_build_imx8mp_kernel_rctee_lib_sm_module_config_h_H
#define __out_build_imx8mp_kernel_rctee_lib_sm_module_config_h_H
#define MODULE_COMPILEFLAGS ""
#define MOD<PERSON>LE_CFLAGS ""
#define MODULE_CPPFLAGS ""
#define MODULE_ASMFLAGS ""
#define MODULE_LDFLAGS ""
#define MODULE_OPTFLAGS ""
#define MODULE_INCLUDES ""
#define MODULE_SRCDEPS ""
#define MODULE_DEPS "_KERNEL_RCTEE_LIB_ARM_FFA_KERNEL_RCTEE_LIB_EXTMEM_KERNEL_RCTEE_LIB_VERSION_KERNEL_RCTEE_LIB_SMC_"
#define MODULE_SRCS "_KERNEL_RCTEE_LIB_SM_SM_C_KERNEL_RCTEE_LIB_SM_SMCALL_C_KERNEL_RCTEE_LIB_SM_NS_MEM_C_KERNEL_RCTEE_LIB_SM_SHARED_MEM_C_KERNEL_RCTEE_LIB_SM_TRUSTY_SCHED_SHARE_C__KERNEL_RCTEE_LIB_SM_ARCH_ARM64_ENTRY_S_"
#endif
