out/build-imx8mp/kernel/rctee/lib/version/version.o: \
  kernel/rctee/lib/ubsan/exemptlist \
  /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt \
  kernel/rctee/lib/version/version.c out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/rctee/lib/version/module_config.h \
  kernel/rctee/lib/version/include/version.h

kernel/rctee/lib/ubsan/exemptlist:

/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt:

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/rctee/lib/version/module_config.h:

kernel/rctee/lib/version/include/version.h:
