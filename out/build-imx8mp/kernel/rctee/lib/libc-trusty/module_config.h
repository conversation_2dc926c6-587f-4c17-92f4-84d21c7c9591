#ifndef __out_build_imx8mp_kernel_rctee_lib_libc_trusty_module_config_h_H
#define __out_build_imx8mp_kernel_rctee_lib_libc_trusty_module_config_h_H
#define KERNEL_LIBC_RANDSEED 0X1A3F3D41U
#define MODULE_COMPILEFLAGS "__U_ALL_SOURCE__D_XOPEN_SOURCE 700__WNO_PARENTHESES__WNO_SIGN_COMPARE__WNO_INCOMPATIBLE_POINTER_TYPES_DISCARDS_QUALIFIERS__WNO_STRING_PLUS_INT__WNO_MISSING_BRACES__WNO_IMPLICIT_FALLTHROUGH__WNO_UNUSED_BUT_SET_VARIABLE___WNO_IGNORED_ATTRIBUTES___WNO_TAUTOLOGICAL_CONSTANT_COMPARE"
#define MODULE_CFLAGS "__WNO_STRICT_PROTOTYPES"
#define MODULE_CPPFLAGS ""
#define MODULE_ASMFLAGS ""
#define MODULE_LDFLAGS ""
#define MODULE_OPTFLAGS ""
#define MODULE_INCLUDES "_OPENSOURCE_LIBS_MUSL_SRC_INTERNAL_OPENSOURCE_LIBS_MUSL_SRC_INCLUDE_"
#define MODULE_SRCDEPS ""
#define MODULE_DEPS "LIB_IO_LIB_HEAP"
#define MODULE_SRCS "KERNEL_RCTEE_LIB_LIBC_TRUSTY_ABORT_C_KERNEL_RCTEE_LIB_LIBC_TRUSTY_CLOSE_C_KERNEL_RCTEE_LIB_LIBC_TRUSTY_FFLUSH_C_KERNEL_RCTEE_LIB_LIBC_TRUSTY_LIBC_STATE_C_KERNEL_RCTEE_LIB_LIBC_TRUSTY_WRITEV_C_KERNEL_LK_LIB_LIBC_ATOI_C_KERNEL_LK_LIB_LIBC_EABI_C_KERNEL_LK_LIB_LIBC_EABI_UNWIND_STUBS_C_KERNEL_LK_LIB_LIBC_IO_HANDLE_C_KERNEL_LK_LIB_LIBC_PRINTF_C_KERNEL_LK_LIB_LIBC_RAND_C_KERNEL_LK_LIB_LIBC_STDIO_C_KERNEL_LK_LIB_LIBC_STRTOL_C_KERNEL_LK_LIB_LIBC_STRTOLL_C__USER_BASE_LIB_LIBC_RCTEE_LOCALE_STUBS_C_KERNEL_LK_LIB_LIBC_ATEXIT_C_KERNEL_LK_LIB_LIBC_PURE_VIRTUAL_CPP_USER_BASE_LIB_LIBC_RCTEE_PTHREADS_C_OPENSOURCE_LIBS_MUSL_SRC_CTYPE_ISALNUM_C_OPENSOURCE_LIBS_MUSL_SRC_CTYPE_ISALPHA_C_OPENSOURCE_LIBS_MUSL_SRC_CTYPE_ISASCII_C_OPENSOURCE_LIBS_MUSL_SRC_CTYPE_ISBLANK_C_OPENSOURCE_LIBS_MUSL_SRC_CTYPE_ISCNTRL_C_OPENSOURCE_LIBS_MUSL_SRC_CTYPE_ISDIGIT_C_OPENSOURCE_LIBS_MUSL_SRC_CTYPE_ISGRAPH_C_OPENSOURCE_LIBS_MUSL_SRC_CTYPE_ISLOWER_C_OPENSOURCE_LIBS_MUSL_SRC_CTYPE_ISPRINT_C_OPENSOURCE_LIBS_MUSL_SRC_CTYPE_ISPUNCT_C_OPENSOURCE_LIBS_MUSL_SRC_CTYPE_ISSPACE_C_OPENSOURCE_LIBS_MUSL_SRC_CTYPE_ISUPPER_C_OPENSOURCE_LIBS_MUSL_SRC_CTYPE_ISXDIGIT_C_OPENSOURCE_LIBS_MUSL_SRC_CTYPE_TOASCII_C_OPENSOURCE_LIBS_MUSL_SRC_CTYPE_TOLOWER_C_OPENSOURCE_LIBS_MUSL_SRC_CTYPE_TOUPPER_C_OPENSOURCE_LIBS_MUSL_SRC_LOCALE_C_LOCALE_C_OPENSOURCE_LIBS_MUSL_SRC_STDLIB_ABS_C_OPENSOURCE_LIBS_MUSL_SRC_STDLIB_BSEARCH_C_OPENSOURCE_LIBS_MUSL_SRC_STDLIB_DIV_C_OPENSOURCE_LIBS_MUSL_SRC_STDLIB_IMAXABS_C_OPENSOURCE_LIBS_MUSL_SRC_STDLIB_IMAXDIV_C_OPENSOURCE_LIBS_MUSL_SRC_STDLIB_LABS_C_OPENSOURCE_LIBS_MUSL_SRC_STDLIB_LDIV_C_OPENSOURCE_LIBS_MUSL_SRC_STDLIB_LLABS_C_OPENSOURCE_LIBS_MUSL_SRC_STDLIB_LLDIV_C_OPENSOURCE_LIBS_MUSL_SRC_STDLIB_QSORT_C_OPENSOURCE_LIBS_MUSL_SRC_STRING_BCMP_C_OPENSOURCE_LIBS_MUSL_SRC_STRING_MEMCCPY_C_OPENSOURCE_LIBS_MUSL_SRC_STRING_MEMMEM_C_OPENSOURCE_LIBS_MUSL_SRC_STRING_MEMPCPY_C_OPENSOURCE_LIBS_MUSL_SRC_STRING_MEMRCHR_C_OPENSOURCE_LIBS_MUSL_SRC_STRING_STPCPY_C_OPENSOURCE_LIBS_MUSL_SRC_STRING_STPNCPY_C_OPENSOURCE_LIBS_MUSL_SRC_STRING_STRCASECMP_C_OPENSOURCE_LIBS_MUSL_SRC_STRING_STRCASESTR_C_OPENSOURCE_LIBS_MUSL_SRC_STRING_STRCHRNUL_C_OPENSOURCE_LIBS_MUSL_SRC_STRING_STRCSPN_C_OPENSOURCE_LIBS_MUSL_SRC_STRING_STRERROR_R_C_OPENSOURCE_LIBS_MUSL_SRC_STRING_STRNCASECMP_C_OPENSOURCE_LIBS_MUSL_SRC_STRING_STRNDUP_C_OPENSOURCE_LIBS_MUSL_SRC_STRING_STRSEP_C_OPENSOURCE_LIBS_MUSL_SRC_STRING_STRTOK_R_C_OPENSOURCE_LIBS_MUSL_SRC_STRING_STRVERSCMP_C_OPENSOURCE_LIBS_MUSL_SRC_STRING_SWAB_C__OPENSOURCE_LIBS_MUSL_SRC_STDIO_STDERR_C_OPENSOURCE_LIBS_MUSL_SRC_STDIO_STDIN_C_OPENSOURCE_LIBS_MUSL_SRC_STDIO_STDOUT_C_OPENSOURCE_LIBS_MUSL_SRC_STDIO___STDIO_CLOSE_C_OPENSOURCE_LIBS_MUSL_SRC_STDIO___STDIO_READ_C_OPENSOURCE_LIBS_MUSL_SRC_STDIO___STDIO_WRITE_C_OPENSOURCE_LIBS_MUSL_SRC_STDIO___STDIO_SEEK_C__OPENSOURCE_LIBS_MUSL_SRC_CTYPE___CTYPE_GET_MB_CUR_MAX_C_OPENSOURCE_LIBS_MUSL_SRC_MULTIBYTE_INTERNAL_C_OPENSOURCE_LIBS_MUSL_SRC_MULTIBYTE_MBTOWC_C_OPENSOURCE_LIBS_MUSL_SRC_MULTIBYTE_WCRTOMB_C__KERNEL_LK_LIB_LIBC_STRING_BCOPY_C_KERNEL_LK_LIB_LIBC_STRING_BZERO_C_KERNEL_LK_LIB_LIBC_STRING_MEMCHR_C_KERNEL_LK_LIB_LIBC_STRING_MEMCMP_C_KERNEL_LK_LIB_LIBC_STRING_MEMCPY_C_KERNEL_LK_LIB_LIBC_STRING_MEMMOVE_C_KERNEL_LK_LIB_LIBC_STRING_MEMSET_C_KERNEL_LK_LIB_LIBC_STRING_STRCAT_C_KERNEL_LK_LIB_LIBC_STRING_STRCHR_C_KERNEL_LK_LIB_LIBC_STRING_STRCMP_C_KERNEL_LK_LIB_LIBC_STRING_STRCOLL_C_KERNEL_LK_LIB_LIBC_STRING_STRCPY_C_KERNEL_LK_LIB_LIBC_STRING_STRDUP_C_KERNEL_LK_LIB_LIBC_STRING_STRERROR_C_KERNEL_LK_LIB_LIBC_STRING_STRLCAT_C_KERNEL_LK_LIB_LIBC_STRING_STRLCPY_C_KERNEL_LK_LIB_LIBC_STRING_STRLEN_C_KERNEL_LK_LIB_LIBC_STRING_STRNCAT_C_KERNEL_LK_LIB_LIBC_STRING_STRNCPY_C_KERNEL_LK_LIB_LIBC_STRING_STRNCMP_C_KERNEL_LK_LIB_LIBC_STRING_STRNICMP_C_KERNEL_LK_LIB_LIBC_STRING_STRNLEN_C_KERNEL_LK_LIB_LIBC_STRING_STRPBRK_C_KERNEL_LK_LIB_LIBC_STRING_STRRCHR_C_KERNEL_LK_LIB_LIBC_STRING_STRSPN_C_KERNEL_LK_LIB_LIBC_STRING_STRSTR_C_KERNEL_LK_LIB_LIBC_STRING_STRTOK_C_KERNEL_LK_LIB_LIBC_STRING_STRXFRM_C"
#endif
