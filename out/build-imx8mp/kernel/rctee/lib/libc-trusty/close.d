out/build-imx8mp/kernel/rctee/lib/libc-trusty/close.o: \
  kernel/rctee/lib/libc-trusty/close.c out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/rctee/lib/libc-trusty/module_config.h \
  kernel/lk/include/panic.h kernel/lk/include/shared/lk/compiler.h \
  opensource_libs/musl/include/stddef.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/rctee/lib/libc-trusty/module_config.h:

kernel/lk/include/panic.h:

kernel/lk/include/shared/lk/compiler.h:

opensource_libs/musl/include/stddef.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:
