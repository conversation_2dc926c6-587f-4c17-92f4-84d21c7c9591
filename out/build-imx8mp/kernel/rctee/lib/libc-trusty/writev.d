out/build-imx8mp/kernel/rctee/lib/libc-trusty/writev.o: \
  kernel/rctee/lib/libc-trusty/writev.c out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/rctee/lib/libc-trusty/module_config.h \
  kernel/lk/include/debug.h opensource_libs/musl/include/stddef.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  opensource_libs/musl/src/include/stdio.h \
  opensource_libs/musl/src/include/../../include/stdio.h \
  opensource_libs/musl/src/include/features.h \
  opensource_libs/musl/src/include/../../include/features.h \
  kernel/lk/include/shared/lk/compiler.h kernel/lk/include/panic.h \
  kernel/lk/include/platform/debug.h \
  opensource_libs/musl/include/sys/types.h kernel/lk/include/lk/types.h \
  opensource_libs/musl/include/inttypes.h \
  opensource_libs/musl/include/stdint.h \
  opensource_libs/musl/arch/aarch64/bits/stdint.h \
  opensource_libs/musl/include/limits.h \
  opensource_libs/musl/arch/aarch64/bits/limits.h \
  opensource_libs/musl/include/stdbool.h \
  opensource_libs/musl/include/stdarg.h \
  kernel/lk/include/uapi/uapi/err.h \
  opensource_libs/musl/include/sys/uio.h \
  kernel/rctee/lib/libc-trusty/include/trusty/io_handle.h \
  kernel/lk/lib/io/include/lib/io.h kernel/lk/include/shared/lk/list.h \
  kernel/lk/include/shared/lk/macros.h \
  kernel/rctee/lib/libc-trusty/include/trusty/uio.h

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/rctee/lib/libc-trusty/module_config.h:

kernel/lk/include/debug.h:

opensource_libs/musl/include/stddef.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

opensource_libs/musl/src/include/stdio.h:

opensource_libs/musl/src/include/../../include/stdio.h:

opensource_libs/musl/src/include/features.h:

opensource_libs/musl/src/include/../../include/features.h:

kernel/lk/include/shared/lk/compiler.h:

kernel/lk/include/panic.h:

kernel/lk/include/platform/debug.h:

opensource_libs/musl/include/sys/types.h:

kernel/lk/include/lk/types.h:

opensource_libs/musl/include/inttypes.h:

opensource_libs/musl/include/stdint.h:

opensource_libs/musl/arch/aarch64/bits/stdint.h:

opensource_libs/musl/include/limits.h:

opensource_libs/musl/arch/aarch64/bits/limits.h:

opensource_libs/musl/include/stdbool.h:

opensource_libs/musl/include/stdarg.h:

kernel/lk/include/uapi/uapi/err.h:

opensource_libs/musl/include/sys/uio.h:

kernel/rctee/lib/libc-trusty/include/trusty/io_handle.h:

kernel/lk/lib/io/include/lib/io.h:

kernel/lk/include/shared/lk/list.h:

kernel/lk/include/shared/lk/macros.h:

kernel/rctee/lib/libc-trusty/include/trusty/uio.h:
