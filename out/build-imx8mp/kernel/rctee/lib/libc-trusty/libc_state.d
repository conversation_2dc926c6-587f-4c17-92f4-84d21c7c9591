out/build-imx8mp/kernel/rctee/lib/libc-trusty/libc_state.o: \
  kernel/rctee/lib/libc-trusty/libc_state.c out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/rctee/lib/libc-trusty/module_config.h \
  kernel/lk/include/uapi/uapi/err.h \
  opensource_libs/musl/src/include/errno.h \
  opensource_libs/musl/src/include/../../include/errno.h \
  opensource_libs/musl/src/include/features.h \
  opensource_libs/musl/src/include/../../include/features.h \
  opensource_libs/musl/arch/generic/bits/errno.h \
  kernel/lk/include/kernel/thread.h \
  kernel/lk/lib/libc/include_common/assert.h \
  kernel/lk/include/shared/lk/compiler.h \
  opensource_libs/musl/include/stddef.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  kernel/lk/include/panic.h opensource_libs/musl/include/sys/types.h \
  kernel/lk/include/lk/types.h opensource_libs/musl/include/inttypes.h \
  opensource_libs/musl/include/stdint.h \
  opensource_libs/musl/arch/aarch64/bits/stdint.h \
  opensource_libs/musl/include/limits.h \
  opensource_libs/musl/arch/aarch64/bits/limits.h \
  opensource_libs/musl/include/stdbool.h \
  kernel/lk/include/shared/lk/list.h \
  kernel/lk/include/shared/lk/macros.h \
  kernel/lk/arch/arm64/include/arch/defines.h \
  kernel/lk/include/arch/ops.h \
  kernel/lk/arch/arm64/include/arch/arch_ops.h \
  kernel/lk/include/shared/lk/reg.h \
  kernel/lk/arch/arm64/include/arch/arm64.h \
  kernel/lk/include/arch/thread.h \
  kernel/lk/arch/arm64/include/arch/arch_thread.h \
  kernel/lk/include/kernel/wait.h kernel/lk/include/kernel/spinlock.h \
  kernel/lk/arch/arm64/include/arch/spinlock.h \
  /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/include/stdatomic.h \
  kernel/lk/include/sys/cdefs.h \
  /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/include/bits/stdatomic.h \
  kernel/lk/include/debug.h opensource_libs/musl/src/include/stdio.h \
  opensource_libs/musl/src/include/../../include/stdio.h \
  kernel/lk/include/platform/debug.h \
  opensource_libs/musl/include/stdarg.h \
  opensource_libs/musl/src/include/stdlib.h \
  opensource_libs/musl/src/include/../../include/stdlib.h \
  kernel/rctee/lib/libc-trusty/include/trusty/libc_state.h \
  opensource_libs/musl/include/locale.h \
  opensource_libs/musl/src/internal/locale_impl.h \
  opensource_libs/musl/src/internal/libc.h

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/rctee/lib/libc-trusty/module_config.h:

kernel/lk/include/uapi/uapi/err.h:

opensource_libs/musl/src/include/errno.h:

opensource_libs/musl/src/include/../../include/errno.h:

opensource_libs/musl/src/include/features.h:

opensource_libs/musl/src/include/../../include/features.h:

opensource_libs/musl/arch/generic/bits/errno.h:

kernel/lk/include/kernel/thread.h:

kernel/lk/lib/libc/include_common/assert.h:

kernel/lk/include/shared/lk/compiler.h:

opensource_libs/musl/include/stddef.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

kernel/lk/include/panic.h:

opensource_libs/musl/include/sys/types.h:

kernel/lk/include/lk/types.h:

opensource_libs/musl/include/inttypes.h:

opensource_libs/musl/include/stdint.h:

opensource_libs/musl/arch/aarch64/bits/stdint.h:

opensource_libs/musl/include/limits.h:

opensource_libs/musl/arch/aarch64/bits/limits.h:

opensource_libs/musl/include/stdbool.h:

kernel/lk/include/shared/lk/list.h:

kernel/lk/include/shared/lk/macros.h:

kernel/lk/arch/arm64/include/arch/defines.h:

kernel/lk/include/arch/ops.h:

kernel/lk/arch/arm64/include/arch/arch_ops.h:

kernel/lk/include/shared/lk/reg.h:

kernel/lk/arch/arm64/include/arch/arm64.h:

kernel/lk/include/arch/thread.h:

kernel/lk/arch/arm64/include/arch/arch_thread.h:

kernel/lk/include/kernel/wait.h:

kernel/lk/include/kernel/spinlock.h:

kernel/lk/arch/arm64/include/arch/spinlock.h:

/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/include/stdatomic.h:

kernel/lk/include/sys/cdefs.h:

/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/include/bits/stdatomic.h:

kernel/lk/include/debug.h:

opensource_libs/musl/src/include/stdio.h:

opensource_libs/musl/src/include/../../include/stdio.h:

kernel/lk/include/platform/debug.h:

opensource_libs/musl/include/stdarg.h:

opensource_libs/musl/src/include/stdlib.h:

opensource_libs/musl/src/include/../../include/stdlib.h:

kernel/rctee/lib/libc-trusty/include/trusty/libc_state.h:

opensource_libs/musl/include/locale.h:

opensource_libs/musl/src/internal/locale_impl.h:

opensource_libs/musl/src/internal/libc.h:
