out/build-imx8mp/kernel/lk/dev/class/netif_api.o: \
  kernel/rctee/lib/ubsan/exemptlist \
  /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt \
  kernel/lk/dev/class/netif_api.c out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/lk/dev/module_config.h \
  kernel/lk/include/uapi/uapi/err.h kernel/lk/include/dev/class/netif.h \
  kernel/lk/include/shared/lk/list.h \
  kernel/lk/include/shared/lk/compiler.h \
  opensource_libs/musl/include/stddef.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  kernel/lk/include/shared/lk/macros.h \
  opensource_libs/musl/include/stdint.h \
  opensource_libs/musl/arch/aarch64/bits/stdint.h \
  opensource_libs/musl/include/stdbool.h kernel/lk/include/dev/driver.h \
  opensource_libs/musl/include/sys/types.h \
  opensource_libs/musl/include/features.h kernel/lk/include/lk/types.h \
  opensource_libs/musl/include/inttypes.h \
  opensource_libs/musl/include/limits.h \
  opensource_libs/musl/arch/aarch64/bits/limits.h \
  opensource_libs/musl/include/endian.h \
  opensource_libs/musl/include/sys/select.h

kernel/rctee/lib/ubsan/exemptlist:

/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt:

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/lk/dev/module_config.h:

kernel/lk/include/uapi/uapi/err.h:

kernel/lk/include/dev/class/netif.h:

kernel/lk/include/shared/lk/list.h:

kernel/lk/include/shared/lk/compiler.h:

opensource_libs/musl/include/stddef.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

kernel/lk/include/shared/lk/macros.h:

opensource_libs/musl/include/stdint.h:

opensource_libs/musl/arch/aarch64/bits/stdint.h:

opensource_libs/musl/include/stdbool.h:

kernel/lk/include/dev/driver.h:

opensource_libs/musl/include/sys/types.h:

opensource_libs/musl/include/features.h:

kernel/lk/include/lk/types.h:

opensource_libs/musl/include/inttypes.h:

opensource_libs/musl/include/limits.h:

opensource_libs/musl/arch/aarch64/bits/limits.h:

opensource_libs/musl/include/endian.h:

opensource_libs/musl/include/sys/select.h:
