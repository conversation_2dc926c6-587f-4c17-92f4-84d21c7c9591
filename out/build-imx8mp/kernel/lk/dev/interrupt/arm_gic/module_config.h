#ifndef __out_build_imx8mp_kernel_lk_dev_interrupt_arm_gic_module_config_h_H
#define __out_build_imx8mp_kernel_lk_dev_interrupt_arm_gic_module_config_h_H
#define GIC_VERSION 3
#define MOD<PERSON>LE_COMPILEFLAGS ""
#define MODULE_CFLAGS ""
#define MODULE_CPPFLAGS ""
#define MODULE_ASMFLAGS ""
#define MODULE_LDFLAGS ""
#define MODULE_OPTFLAGS ""
#define MODULE_INCLUDES ""
#define MODULE_SRCDEPS ""
#define MODULE_DEPS ""
#define MODU<PERSON>_SRCS "_KERNEL_LK_DEV_INTERRUPT_ARM_GIC_ARM_GIC_C_KERNEL_LK_DEV_INTERRUPT_ARM_GIC_GIC_V3_C"
#endif
