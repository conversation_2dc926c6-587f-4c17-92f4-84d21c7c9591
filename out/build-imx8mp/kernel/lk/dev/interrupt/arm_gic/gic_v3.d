out/build-imx8mp/kernel/lk/dev/interrupt/arm_gic/gic_v3.o: \
  kernel/rctee/lib/ubsan/exemptlist \
  /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt \
  kernel/lk/dev/interrupt/arm_gic/gic_v3.c out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/lk/dev/interrupt/arm_gic/module_config.h \
  kernel/lk/include/arch/ops.h opensource_libs/musl/include/sys/types.h \
  opensource_libs/musl/include/features.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  kernel/lk/include/lk/types.h opensource_libs/musl/include/inttypes.h \
  opensource_libs/musl/include/stdint.h \
  opensource_libs/musl/arch/aarch64/bits/stdint.h \
  opensource_libs/musl/include/limits.h \
  opensource_libs/musl/arch/aarch64/bits/limits.h \
  opensource_libs/musl/include/stddef.h \
  opensource_libs/musl/include/stdbool.h \
  opensource_libs/musl/include/endian.h \
  opensource_libs/musl/include/sys/select.h \
  kernel/lk/include/shared/lk/compiler.h \
  kernel/lk/arch/arm64/include/arch/arch_ops.h \
  kernel/lk/lib/libc/include_common/assert.h kernel/lk/include/panic.h \
  kernel/lk/include/shared/lk/reg.h \
  kernel/lk/arch/arm64/include/arch/arm64.h kernel/lk/include/bits.h \
  kernel/lk/include/shared/lk/macros.h \
  kernel/lk/dev/interrupt/arm_gic/include/dev/interrupt/arm_gic.h \
  kernel/lk/dev/interrupt/arm_gic/arm_gic_common.h \
  kernel/hardware/nxp/platform/imx/common/include/platform/gic.h \
  kernel/hardware/nxp/platform/imx/soc/imx8mp/include/imx-regs.h \
  kernel/lk/dev/interrupt/arm_gic/gic_v3.h

kernel/rctee/lib/ubsan/exemptlist:

/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt:

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/lk/dev/interrupt/arm_gic/module_config.h:

kernel/lk/include/arch/ops.h:

opensource_libs/musl/include/sys/types.h:

opensource_libs/musl/include/features.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

kernel/lk/include/lk/types.h:

opensource_libs/musl/include/inttypes.h:

opensource_libs/musl/include/stdint.h:

opensource_libs/musl/arch/aarch64/bits/stdint.h:

opensource_libs/musl/include/limits.h:

opensource_libs/musl/arch/aarch64/bits/limits.h:

opensource_libs/musl/include/stddef.h:

opensource_libs/musl/include/stdbool.h:

opensource_libs/musl/include/endian.h:

opensource_libs/musl/include/sys/select.h:

kernel/lk/include/shared/lk/compiler.h:

kernel/lk/arch/arm64/include/arch/arch_ops.h:

kernel/lk/lib/libc/include_common/assert.h:

kernel/lk/include/panic.h:

kernel/lk/include/shared/lk/reg.h:

kernel/lk/arch/arm64/include/arch/arm64.h:

kernel/lk/include/bits.h:

kernel/lk/include/shared/lk/macros.h:

kernel/lk/dev/interrupt/arm_gic/include/dev/interrupt/arm_gic.h:

kernel/lk/dev/interrupt/arm_gic/arm_gic_common.h:

kernel/hardware/nxp/platform/imx/common/include/platform/gic.h:

kernel/hardware/nxp/platform/imx/soc/imx8mp/include/imx-regs.h:

kernel/lk/dev/interrupt/arm_gic/gic_v3.h:
