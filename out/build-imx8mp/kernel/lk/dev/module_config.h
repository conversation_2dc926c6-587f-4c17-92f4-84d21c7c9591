#ifndef __out_build_imx8mp_kernel_lk_dev_module_config_h_H
#define __out_build_imx8mp_kernel_lk_dev_module_config_h_H
#define MODULE_COMPILEFLAGS ""
#define MODULE_CFLAGS ""
#define MOD<PERSON>LE_CPPFLAGS ""
#define MODULE_ASMFLAGS ""
#define MODULE_LDFLAGS ""
#define MODULE_OPTFLAGS ""
#define MODULE_INCLUDES ""
#define MODULE_SRCDEPS ""
#define MODULE_DEPS ""
#define MODULE_SRCS "_KERNEL_LK_DEV_DEV_C_KERNEL_LK_DEV_DRIVER_C_KERNEL_LK_DEV_CLASS_BLOCK_API_C_KERNEL_LK_DEV_CLASS_I2C_API_C_KERNEL_LK_DEV_CLASS_SPI_API_C_KERNEL_LK_DEV_CLASS_UART_API_C_KERNEL_LK_DEV_CLASS_FB_API_C_KERNEL_LK_DEV_CLASS_NETIF_API_C_"
#endif
