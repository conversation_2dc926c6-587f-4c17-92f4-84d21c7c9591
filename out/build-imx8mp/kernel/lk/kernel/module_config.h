#ifndef __out_build_imx8mp_kernel_lk_kernel_module_config_h_H
#define __out_build_imx8mp_kernel_lk_kernel_module_config_h_H
#define MODULE_COMPILEFLAGS ""
#define MODULE_CFLAGS ""
#define M<PERSON><PERSON>LE_CPPFLAGS ""
#define MOD<PERSON>LE_ASMFLAGS ""
#define MODULE_LDFLAGS ""
#define MODULE_OPTFLAGS ""
#define MODULE_INCLUDES ""
#define MODULE_SRCDEPS ""
#define MODULE_DEPS "LIB_DEBUG_LIB_HEAP_KERNEL_RCTEE_LIB_RAND_KERNEL_RCTEE_LIB_LIBC_TRUSTY_KERNEL_VM"
#define MODULE_SRCS "KERNEL_LK_KERNEL_DEBUG_C_KERNEL_LK_KERNEL_EVENT_C_KERNEL_LK_KERNEL_INIT_C_KERNEL_LK_KERNEL_MUTEX_C_KERNEL_LK_KERNEL_THREAD_C_KERNEL_LK_KERNEL_TIMER_C_KERNEL_LK_KERNEL_SEMAPHORE_C_KERNEL_LK_KERNEL_MP_C_KERNEL_LK_KERNEL_PORT_C"
#endif
