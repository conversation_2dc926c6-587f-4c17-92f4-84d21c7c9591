out/build-imx8mp/kernel/lk/arch/arm64/exceptions.o: \
  kernel/rctee/lib/ubsan/exemptlist \
  /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt \
  kernel/lk/arch/arm64/exceptions.S out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/lk/arch/arm64/module_config.h \
  kernel/lk/include/shared/lk/asm.h \
  kernel/lk/arch/arm64/include/arch/asm.h \
  kernel/lk/arch/arm64/include/arch/asm_macros.h

kernel/rctee/lib/ubsan/exemptlist:

/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt:

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/lk/arch/arm64/module_config.h:

kernel/lk/include/shared/lk/asm.h:

kernel/lk/arch/arm64/include/arch/asm.h:

kernel/lk/arch/arm64/include/arch/asm_macros.h:
