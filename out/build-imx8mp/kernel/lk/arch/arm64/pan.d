out/build-imx8mp/kernel/lk/arch/arm64/pan.o: \
  kernel/rctee/lib/ubsan/exemptlist \
  /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt \
  kernel/lk/arch/arm64/pan.c out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/lk/arch/arm64/module_config.h \
  kernel/lk/arch/arm64/include/arch/arm64/sregs.h \
  kernel/lk/arch/arm64/include/arch/arm64.h \
  opensource_libs/musl/include/stdbool.h \
  opensource_libs/musl/include/sys/types.h \
  opensource_libs/musl/include/features.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  kernel/lk/include/lk/types.h opensource_libs/musl/include/inttypes.h \
  opensource_libs/musl/include/stdint.h \
  opensource_libs/musl/arch/aarch64/bits/stdint.h \
  opensource_libs/musl/include/limits.h \
  opensource_libs/musl/arch/aarch64/bits/limits.h \
  opensource_libs/musl/include/stddef.h \
  opensource_libs/musl/include/endian.h \
  opensource_libs/musl/include/sys/select.h \
  kernel/lk/include/shared/lk/compiler.h \
  kernel/lk/arch/arm64/include/arch/pan.h kernel/lk/include/lk/init.h \
  opensource_libs/musl/include/stdio.h

kernel/rctee/lib/ubsan/exemptlist:

/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt:

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/lk/arch/arm64/module_config.h:

kernel/lk/arch/arm64/include/arch/arm64/sregs.h:

kernel/lk/arch/arm64/include/arch/arm64.h:

opensource_libs/musl/include/stdbool.h:

opensource_libs/musl/include/sys/types.h:

opensource_libs/musl/include/features.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

kernel/lk/include/lk/types.h:

opensource_libs/musl/include/inttypes.h:

opensource_libs/musl/include/stdint.h:

opensource_libs/musl/arch/aarch64/bits/stdint.h:

opensource_libs/musl/include/limits.h:

opensource_libs/musl/arch/aarch64/bits/limits.h:

opensource_libs/musl/include/stddef.h:

opensource_libs/musl/include/endian.h:

opensource_libs/musl/include/sys/select.h:

kernel/lk/include/shared/lk/compiler.h:

kernel/lk/arch/arm64/include/arch/pan.h:

kernel/lk/include/lk/init.h:

opensource_libs/musl/include/stdio.h:
