#ifndef __out_build_imx8mp_kernel_lk_arch_arm64_module_config_h_H
#define __out_build_imx8mp_kernel_lk_arch_arm64_module_config_h_H
#define MODULE_COMPILEFLAGS ""
#define MOD<PERSON>LE_CFLAGS ""
#define MOD<PERSON>LE_CPPFLAGS ""
#define MOD<PERSON>LE_ASMFLAGS ""
#define MODULE_LDFLAGS ""
#define MODULE_OPTFLAGS ""
#define MODULE_INCLUDES ""
#define MODULE_SRCDEPS ""
#define MODULE_DEPS "_KERNEL_RCTEE_LIB_RCTEE_"
#define MODULE_SRCS "_KERNEL_LK_ARCH_ARM64_ARCH_C_KERNEL_LK_ARCH_ARM64_ASM_S_KERNEL_LK_ARCH_ARM64_EXCEPTIONS_S_KERNEL_LK_ARCH_ARM64_EXCEPTIONS_C_C_KERNEL_LK_ARCH_ARM64_FPU_C_KERNEL_LK_ARCH_ARM64_MEMTAG_C_KERNEL_LK_ARCH_ARM64_THREAD_C_KERNEL_LK_ARCH_ARM64_SPINLOCK_S_KERNEL_LK_ARCH_ARM64_START_S_KERNEL_LK_ARCH_ARM64_CACHE_OPS_S_KERNEL_LK_ARCH_ARM64_USERCOPY_S_KERNEL_LK_ARCH_ARM64_SAFECOPY_S_KERNEL_LK_ARCH_ARM64_PAN_C_KERNEL_LK_ARCH_ARM64_BTI_C_KERNEL_LK_ARCH_ARM64_PAC_C_KERNEL_LK_ARCH_ARM64_SVE_C__KERNEL_LK_ARCH_ARM64_MP_C_KERNEL_LK_ARCH_ARM64_EARLY_MMU_C_KERNEL_LK_ARCH_ARM64_MMU_C"
#endif
