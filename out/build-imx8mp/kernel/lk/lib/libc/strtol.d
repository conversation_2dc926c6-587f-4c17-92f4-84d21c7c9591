out/build-imx8mp/kernel/lk/lib/libc/strtol.o: kernel/lk/lib/libc/strtol.c \
  out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/rctee/lib/libc-trusty/module_config.h \
  opensource_libs/musl/include/ctype.h \
  opensource_libs/musl/src/include/features.h \
  opensource_libs/musl/src/include/../../include/features.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  opensource_libs/musl/src/include/errno.h \
  opensource_libs/musl/src/include/../../include/errno.h \
  opensource_libs/musl/arch/generic/bits/errno.h \
  opensource_libs/musl/include/limits.h \
  opensource_libs/musl/arch/aarch64/bits/limits.h \
  opensource_libs/musl/src/include/stdlib.h \
  opensource_libs/musl/src/include/../../include/stdlib.h

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/rctee/lib/libc-trusty/module_config.h:

opensource_libs/musl/include/ctype.h:

opensource_libs/musl/src/include/features.h:

opensource_libs/musl/src/include/../../include/features.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

opensource_libs/musl/src/include/errno.h:

opensource_libs/musl/src/include/../../include/errno.h:

opensource_libs/musl/arch/generic/bits/errno.h:

opensource_libs/musl/include/limits.h:

opensource_libs/musl/arch/aarch64/bits/limits.h:

opensource_libs/musl/src/include/stdlib.h:

opensource_libs/musl/src/include/../../include/stdlib.h:
