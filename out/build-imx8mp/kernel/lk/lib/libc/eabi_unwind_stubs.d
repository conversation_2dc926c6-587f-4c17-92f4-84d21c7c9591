out/build-imx8mp/kernel/lk/lib/libc/eabi_unwind_stubs.o: \
  kernel/lk/lib/libc/eabi_unwind_stubs.c out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/rctee/lib/libc-trusty/module_config.h \
  /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/include/unwind.h \
  opensource_libs/musl/include/stdint.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  opensource_libs/musl/arch/aarch64/bits/stdint.h

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/rctee/lib/libc-trusty/module_config.h:

/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/include/unwind.h:

opensource_libs/musl/include/stdint.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

opensource_libs/musl/arch/aarch64/bits/stdint.h:
