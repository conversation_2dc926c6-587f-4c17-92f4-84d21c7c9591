out/build-imx8mp/kernel/lk/lib/libc/string/memcpy.o: \
  kernel/lk/lib/libc/string/memcpy.c out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/rctee/lib/libc-trusty/module_config.h \
  opensource_libs/musl/src/include/string.h \
  opensource_libs/musl/src/include/../../include/string.h \
  opensource_libs/musl/src/include/features.h \
  opensource_libs/musl/src/include/../../include/features.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  opensource_libs/musl/include/sys/types.h kernel/lk/include/lk/types.h \
  opensource_libs/musl/include/inttypes.h \
  opensource_libs/musl/include/stdint.h \
  opensource_libs/musl/arch/aarch64/bits/stdint.h \
  opensource_libs/musl/include/limits.h \
  opensource_libs/musl/arch/aarch64/bits/limits.h \
  opensource_libs/musl/include/stddef.h \
  opensource_libs/musl/include/stdbool.h

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/rctee/lib/libc-trusty/module_config.h:

opensource_libs/musl/src/include/string.h:

opensource_libs/musl/src/include/../../include/string.h:

opensource_libs/musl/src/include/features.h:

opensource_libs/musl/src/include/../../include/features.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

opensource_libs/musl/include/sys/types.h:

kernel/lk/include/lk/types.h:

opensource_libs/musl/include/inttypes.h:

opensource_libs/musl/include/stdint.h:

opensource_libs/musl/arch/aarch64/bits/stdint.h:

opensource_libs/musl/include/limits.h:

opensource_libs/musl/arch/aarch64/bits/limits.h:

opensource_libs/musl/include/stddef.h:

opensource_libs/musl/include/stdbool.h:
