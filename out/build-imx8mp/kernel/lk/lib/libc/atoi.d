out/build-imx8mp/kernel/lk/lib/libc/atoi.o: kernel/lk/lib/libc/atoi.c \
  out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/rctee/lib/libc-trusty/module_config.h \
  opensource_libs/musl/src/include/stdlib.h \
  opensource_libs/musl/src/include/../../include/stdlib.h \
  opensource_libs/musl/src/include/features.h \
  opensource_libs/musl/src/include/../../include/features.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  opensource_libs/musl/include/ctype.h \
  opensource_libs/musl/src/include/errno.h \
  opensource_libs/musl/src/include/../../include/errno.h \
  opensource_libs/musl/arch/generic/bits/errno.h \
  kernel/lk/lib/libc/include/stdlib.h \
  kernel/lk/include/shared/lk/compiler.h \
  opensource_libs/musl/include/stddef.h \
  opensource_libs/musl/include/sys/types.h kernel/lk/include/lk/types.h \
  opensource_libs/musl/include/inttypes.h \
  opensource_libs/musl/include/stdint.h \
  opensource_libs/musl/arch/aarch64/bits/stdint.h \
  opensource_libs/musl/include/limits.h \
  opensource_libs/musl/arch/aarch64/bits/limits.h \
  opensource_libs/musl/include/stdbool.h \
  opensource_libs/musl/include/malloc.h \
  opensource_libs/musl/include/endian.h \
  kernel/lk/lib/libc/include_common/rand.h \
  kernel/lk/include/shared/lk/macros.h

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/rctee/lib/libc-trusty/module_config.h:

opensource_libs/musl/src/include/stdlib.h:

opensource_libs/musl/src/include/../../include/stdlib.h:

opensource_libs/musl/src/include/features.h:

opensource_libs/musl/src/include/../../include/features.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

opensource_libs/musl/include/ctype.h:

opensource_libs/musl/src/include/errno.h:

opensource_libs/musl/src/include/../../include/errno.h:

opensource_libs/musl/arch/generic/bits/errno.h:

kernel/lk/lib/libc/include/stdlib.h:

kernel/lk/include/shared/lk/compiler.h:

opensource_libs/musl/include/stddef.h:

opensource_libs/musl/include/sys/types.h:

kernel/lk/include/lk/types.h:

opensource_libs/musl/include/inttypes.h:

opensource_libs/musl/include/stdint.h:

opensource_libs/musl/arch/aarch64/bits/stdint.h:

opensource_libs/musl/include/limits.h:

opensource_libs/musl/arch/aarch64/bits/limits.h:

opensource_libs/musl/include/stdbool.h:

opensource_libs/musl/include/malloc.h:

opensource_libs/musl/include/endian.h:

kernel/lk/lib/libc/include_common/rand.h:

kernel/lk/include/shared/lk/macros.h:
