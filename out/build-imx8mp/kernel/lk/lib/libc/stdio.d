out/build-imx8mp/kernel/lk/lib/libc/stdio.o: kernel/lk/lib/libc/stdio.c \
  out/build-imx8mp/config.h \
  out/build-imx8mp/kernel/rctee/lib/libc-trusty/module_config.h \
  kernel/lk/lib/libc/include_common/io_handle.h \
  kernel/lk/lib/io/include/lib/io.h \
  kernel/lk/include/shared/lk/compiler.h \
  opensource_libs/musl/include/stddef.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  kernel/lk/include/shared/lk/list.h \
  kernel/lk/include/shared/lk/macros.h \
  opensource_libs/musl/include/stdint.h \
  opensource_libs/musl/arch/aarch64/bits/stdint.h \
  opensource_libs/musl/include/stdbool.h \
  opensource_libs/musl/include/sys/types.h \
  opensource_libs/musl/src/include/features.h \
  opensource_libs/musl/src/include/../../include/features.h \
  kernel/lk/include/lk/types.h opensource_libs/musl/include/inttypes.h \
  opensource_libs/musl/include/limits.h \
  opensource_libs/musl/arch/aarch64/bits/limits.h \
  opensource_libs/musl/src/include/stdio.h \
  opensource_libs/musl/src/include/../../include/stdio.h \
  kernel/lk/lib/libc/include_common/printf.h \
  opensource_libs/musl/include/stdarg.h \
  opensource_libs/musl/src/include/string.h \
  opensource_libs/musl/src/include/../../include/string.h

out/build-imx8mp/config.h:

out/build-imx8mp/kernel/rctee/lib/libc-trusty/module_config.h:

kernel/lk/lib/libc/include_common/io_handle.h:

kernel/lk/lib/io/include/lib/io.h:

kernel/lk/include/shared/lk/compiler.h:

opensource_libs/musl/include/stddef.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

kernel/lk/include/shared/lk/list.h:

kernel/lk/include/shared/lk/macros.h:

opensource_libs/musl/include/stdint.h:

opensource_libs/musl/arch/aarch64/bits/stdint.h:

opensource_libs/musl/include/stdbool.h:

opensource_libs/musl/include/sys/types.h:

opensource_libs/musl/src/include/features.h:

opensource_libs/musl/src/include/../../include/features.h:

kernel/lk/include/lk/types.h:

opensource_libs/musl/include/inttypes.h:

opensource_libs/musl/include/limits.h:

opensource_libs/musl/arch/aarch64/bits/limits.h:

opensource_libs/musl/src/include/stdio.h:

opensource_libs/musl/src/include/../../include/stdio.h:

kernel/lk/lib/libc/include_common/printf.h:

opensource_libs/musl/include/stdarg.h:

opensource_libs/musl/src/include/string.h:

opensource_libs/musl/src/include/../../include/string.h:
