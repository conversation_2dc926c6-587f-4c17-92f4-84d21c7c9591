
out/build-imx8mp/lk.elf:	file format elf64-littleaarch64
architecture: aarch64
start address: 0xffffffff00000000

Program Header:
    LOAD off    0x0000000000001000 vaddr 0xffffffff00000000 paddr 0x0000000056000000 align 2**12
         filesz 0x00000000000370ac memsz 0x00000000000370ac flags r-x
    LOAD off    0x00000000000380ac vaddr 0xffffffff000370ac paddr 0x00000000560370ac align 2**12
         filesz 0x0000000000124ff4 memsz 0x0000000000124ff4 flags r--
    LOAD off    0x000000000015d0a0 vaddr 0xffffffff0015c0a0 paddr 0x000000005615c0a0 align 2**12
         filesz 0x0000000000000378 memsz 0x0000000000000378 flags rw-
    LOAD off    0x000000000015d418 vaddr 0xffffffff0015c418 paddr 0x000000005615c418 align 2**12
         filesz 0x00000000000000a0 memsz 0x00000000000000a0 flags r--
    LOAD off    0x000000000015d4b8 vaddr 0xffffffff0015c4b8 paddr 0x000000005615c4b8 align 2**12
         filesz 0x0000000000000148 memsz 0x0000000000000148 flags rw-
    LOAD off    0x000000000015d600 vaddr 0xffffffff0015c600 paddr 0x000000005615c600 align 2**12
         filesz 0x0000000000002658 memsz 0x000000000000eb10 flags rw-
 DYNAMIC off    0x000000000015d4b8 vaddr 0xffffffff0015c4b8 paddr 0x000000005615c4b8 align 2**3
         filesz 0x00000000000000d0 memsz 0x00000000000000d0 flags rw-
   RELRO off    0x000000000015d4b8 vaddr 0xffffffff0015c4b8 paddr 0x000000005615c4b8 align 2**0
         filesz 0x0000000000000148 memsz 0x0000000000000b48 flags r--
   STACK off    0x0000000000000000 vaddr 0x0000000000000000 paddr 0x0000000000000000 align 2**64
         filesz 0x0000000000000000 memsz 0x0000000000000000 flags rw-

Dynamic Section:
  FLAGS           0x0000000000000002
  FLAGS_1         0x0000000008000000
  DEBUG           0x0000000000000000
  ANDROID_RELR    0xffffffff0015c418
  ANDROID_RELRSZ  0x00000000000000a0
  ANDROID_RELRENT 0x0000000000000008
  SYMTAB          0xffffffff000370d0
  SYMENT          0x0000000000000018
  STRTAB          0xffffffff00037118
  STRSZ           0x0000000000000021
  GNU_HASH        0xffffffff00037140
  HASH            0xffffffff000370ac

Sections:
Idx Name               Size     VMA              LMA              Type
  0                    00000000 0000000000000000 0000000000000000 
  1 .text              000370ac ffffffff00000000 0000000056000000 TEXT
  2 .hash              00000020 ffffffff000370ac 00000000560370ac 
  3 .dynsym            00000048 ffffffff000370d0 00000000560370d0 
  4 .dynstr            00000021 ffffffff00037118 0000000056037118 
  5 .gnu.hash          0000001c ffffffff00037140 0000000056037140 
  6 .fake_post_text    00000000 ffffffff0003715c 000000005603715c DATA
  7 .rodata            0000b5b0 ffffffff00038000 0000000056038000 DATA
  8 .__manifest_data   0000020c ffffffff000435b0 00000000560435b0 DATA
  9 .__rctee_app       00118844 ffffffff000437bc 00000000560437bc DATA
 10 .__rctee_app_list  000000a0 ffffffff0015c000 000000005615c000 DATA
 11 .drivers           00000000 ffffffff0015c0a0 000000005615c0a0 DATA
 12 .apps              00000000 ffffffff0015c0a0 000000005615c0a0 DATA
 13 .lk_init           00000378 ffffffff0015c0a0 000000005615c0a0 DATA
 14 .relr.dyn          000000a0 ffffffff0015c418 000000005615c418 
 15 .ctors             00000000 ffffffff0015c4b8 000000005615c4b8 DATA
 16 .dtors             00000000 ffffffff0015c4b8 000000005615c4b8 DATA
 17 .dynamic           000000d0 ffffffff0015c4b8 000000005615c4b8 
 18 .got               00000078 ffffffff0015c588 000000005615c588 DATA
 19 .fake_post_rodata  00000000 ffffffff0015c600 000000005615c600 DATA
 20 .data              00001c58 ffffffff0015d000 000000005615d000 DATA
 21 .devices           00000000 ffffffff0015ec58 000000005615ec58 DATA
 22 .fake_post_data    00000000 ffffffff0015ec58 000000005615ec58 DATA
 23 .bss               0000c110 ffffffff0015f000 000000005615f000 BSS
 24 .debug_info        0005a4fa 0000000000000000 0000000000000000 DEBUG
 25 .debug_abbrev      00005bd1 0000000000000000 0000000000000000 DEBUG
 26 .debug_aranges     000001f0 0000000000000000 0000000000000000 DEBUG
 27 .debug_line        0004a8c0 0000000000000000 0000000000000000 DEBUG
 28 .debug_line_str    00003e0b 0000000000000000 0000000000000000 DEBUG
 29 .debug_rnglists    0000bdbe 0000000000000000 0000000000000000 DEBUG
 30 .debug_macro       000fa7b5 0000000000000000 0000000000000000 DEBUG
 31 .debug_str_offsets 000537fc 0000000000000000 0000000000000000 DEBUG
 32 .debug_str         0003211e 0000000000000000 0000000000000000 DEBUG
 33 .debug_addr        0000ec20 0000000000000000 0000000000000000 DEBUG
 34 .debug_loclists    00030f86 0000000000000000 0000000000000000 DEBUG
 35 .symtab            00016920 0000000000000000 0000000000000000 
 36 .shstrtab          00000199 0000000000000000 0000000000000000 
 37 .strtab            00009479 0000000000000000 0000000000000000 

SYMBOL TABLE:
ffffffff00001934 l       .text	0000000000000000 $x.0
ffffffff000019e8 l       .text	0000000000000000 .notEL1
ffffffff00001a10 l       .text	0000000000000000 .inEL2
ffffffff00001a20 l       .text	0000000000000000 .confEL1
0000000000000000 l       .debug_aranges	0000000000000000 $d.1
0000000000000000 l       .debug_abbrev	0000000000000000 $d.2
0000000000000000 l       .debug_info	0000000000000000 $d.3
0000000000000000 l       .debug_line	0000000000000000 $d.4
00000000000009ae l       .debug_line_str	0000000000000000 $d.5
ffffffff00001000 l     F .text	0000000000000000 arm64_sync_exc_current_el_SP0
ffffffff00001000 l       .text	0000000000000000 $x.0
ffffffff00001800 l     F .text	0000000000000000 arm64_sync_exc_shared
ffffffff00001080 l     F .text	0000000000000000 arm64_irq_current_el_SP0
ffffffff00001860 l     F .text	0000000000000000 arm64_exc_shared_restore_short
ffffffff00001100 l     F .text	0000000000000000 arm64_fiq_current_el_SP0
ffffffff00001180 l     F .text	0000000000000000 arm64_err_exc_current_el_SP0
ffffffff00001200 l     F .text	0000000000000000 arm64_sync_exc_current_el_SPx
ffffffff00001280 l     F .text	0000000000000000 arm64_irq_current_el_SPx
ffffffff00001300 l     F .text	0000000000000000 arm64_fiq_current_el_SPx
ffffffff00001380 l     F .text	0000000000000000 arm64_err_exc_current_el_SPx
ffffffff00001400 l     F .text	0000000000000000 arm64_sync_exc_lower_el_64
ffffffff00001480 l     F .text	0000000000000000 arm64_irq_lower_el_64
ffffffff00001500 l     F .text	0000000000000000 arm64_fiq_lower_el_64
ffffffff00001580 l     F .text	0000000000000000 arm64_err_exc_lower_el_64
ffffffff00001600 l     F .text	0000000000000000 arm64_sync_exc_lower_el_32
ffffffff00001680 l     F .text	0000000000000000 arm64_irq_lower_el_32
ffffffff00001700 l     F .text	0000000000000000 arm64_fiq_lower_el_32
ffffffff00001780 l     F .text	0000000000000000 arm64_err_exc_lower_el_32
0000000000000030 l       .debug_aranges	0000000000000000 $d.1
0000000000000021 l       .debug_abbrev	0000000000000000 $d.2
00000000000001d7 l       .debug_info	0000000000000000 $d.3
000000000000008d l       .debug_line	0000000000000000 $d.4
00000000000009ae l       .debug_line_str	0000000000000000 $d.5
ffffffff00001a54 l       .text	0000000000000000 $x.0
0000000000000060 l       .debug_aranges	0000000000000000 $d.1
0000000000000042 l       .debug_abbrev	0000000000000000 $d.2
0000000000000628 l       .debug_info	0000000000000000 $d.3
00000000000002fe l       .debug_line	0000000000000000 $d.4
00000000000009ae l       .debug_line_str	0000000000000000 $d.5
ffffffff00000000 l       .text	0000000000000000 $x.0
ffffffff00000368 l       .text	0000000000000000 $d.1
ffffffff00000800 l       .text	0000000000000000 $x.2
ffffffff0015d000 l       .data	0000000000000000 $d.3
ffffffff0015f000 l       .bss	0000000000000000 $d.4
ffffffff00163000 l       .bss	0000000000000000 $d.5
ffffffff00167000 l       .bss	0000000000000000 $d.6
ffffffff00167800 l       .bss	0000000000000000 $d.7
0000000000000090 l       .debug_aranges	0000000000000000 $d.8
0000000000000000 l       .debug_rnglists	0000000000000000 $d.9
0000000000000063 l       .debug_abbrev	0000000000000000 $d.10
000000000000078f l       .debug_info	0000000000000000 $d.11
0000000000000353 l       .debug_line	0000000000000000 $d.12
00000000000009ae l       .debug_line_str	0000000000000000 $d.13
ffffffff0015d000 l     O .data	0000000000000000 .hidden mmu_on_vaddr_ptr
ffffffff00001a94 l       .text	0000000000000000 $x.0
00000000000000d0 l       .debug_aranges	0000000000000000 $d.1
0000000000000082 l       .debug_abbrev	0000000000000000 $d.2
00000000000008d8 l       .debug_info	0000000000000000 $d.3
00000000000004c8 l       .debug_line	0000000000000000 $d.4
00000000000009ae l       .debug_line_str	0000000000000000 $d.5
ffffffff00001b30 l       .text	0000000000000000 $x.0
ffffffff00038000 l       .rodata	0000000000000000 $d.1
ffffffff00001b38 l       .text	0000000000000000 $x.2
ffffffff00038010 l       .rodata	0000000000000000 $d.3
ffffffff00001b54 l       .text	0000000000000000 $x.4
ffffffff00038020 l       .rodata	0000000000000000 $d.5
ffffffff00001b74 l       .text	0000000000000000 $x.6
0000000000000100 l       .debug_aranges	0000000000000000 $d.7
00000000000000a3 l       .debug_abbrev	0000000000000000 $d.8
0000000000000a8a l       .debug_info	0000000000000000 $d.9
0000000000000534 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
ffffffff00001bb4 l       .text	0000000000000000 $x.0
ffffffff00038030 l       .rodata	0000000000000000 $d.1
ffffffff00001bb8 l       .text	0000000000000000 $x.2
ffffffff00038040 l       .rodata	0000000000000000 $d.3
ffffffff00001bd4 l       .text	0000000000000000 $x.4
ffffffff00038050 l       .rodata	0000000000000000 $d.5
ffffffff00001bf0 l       .text	0000000000000000 $x.6
0000000000000130 l       .debug_aranges	0000000000000000 $d.7
00000000000000c4 l       .debug_abbrev	0000000000000000 $d.8
0000000000000bfc l       .debug_info	0000000000000000 $d.9
000000000000059a l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 abort.c
00000000000000e5 l       .debug_abbrev	0000000000000000 $d.2
0000000000000d41 l       .debug_info	0000000000000000 $d.3
0000000000000000 l       .debug_macro	0000000000000000 $d.4
0000000000000000 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000000 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
00000000000005f3 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 close.c
0000000000000000 l       .debug_loclists	0000000000000000 $d.2
000000000000015c l       .debug_abbrev	0000000000000000 $d.3
0000000000000d98 l       .debug_info	0000000000000000 $d.4
000000000000094d l       .debug_macro	0000000000000000 $d.5
0000000000000974 l       .debug_str_offsets	0000000000000000 $d.6
00000000000194b4 l       .debug_str	0000000000000000 $d.7
0000000000000018 l       .debug_addr	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
0000000000000741 l       .debug_line	0000000000000000 $d.11
00000000000009ae l       .debug_line_str	0000000000000000 $d.12
0000000000000000 l    df *ABS*	0000000000000000 fflush.c
000000000000001e l       .debug_loclists	0000000000000000 $d.1
00000000000001e4 l       .debug_abbrev	0000000000000000 $d.2
0000000000000e05 l       .debug_info	0000000000000000 $d.3
000000000000120d l       .debug_macro	0000000000000000 $d.4
000000000000129c l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000030 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000000833 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 libc_state.c
ffffffff00001c04 l       .text	0000000000000000 $x.0
ffffffff00001c6c l       .text	0000000000000000 $x.1
ffffffff000397cf l       .rodata	0000000000000000 $d.4
000000000000003c l       .debug_loclists	0000000000000000 $d.5
000000000000025d l       .debug_abbrev	0000000000000000 $d.6
0000000000000e5f l       .debug_info	0000000000000000 $d.7
0000000000000023 l       .debug_rnglists	0000000000000000 $d.8
0000000000001a93 l       .debug_macro	0000000000000000 $d.9
0000000000001b90 l       .debug_str_offsets	0000000000000000 $d.10
00000000000194b4 l       .debug_str	0000000000000000 $d.11
0000000000000040 l       .debug_addr	0000000000000000 $d.12
0000000000000000 l       *ABS*	0000000000000000 $d.13
0000000000000000 l       *ABS*	0000000000000000 $d.14
0000000000000936 l       .debug_line	0000000000000000 $d.15
00000000000009ae l       .debug_line_str	0000000000000000 $d.16
ffffffff00001c04 l     F .text	0000000000000068 .hidden libc_state_thread_init
ffffffff00001c6c l     F .text	0000000000000074 .hidden libc_state_thread_free
0000000000000000 l    df *ABS*	0000000000000000 writev.c
ffffffff00001ce0 l       .text	0000000000000000 $x.0
00000000000000f2 l       .debug_loclists	0000000000000000 $d.1
0000000000000483 l       .debug_abbrev	0000000000000000 $d.2
00000000000013d1 l       .debug_info	0000000000000000 $d.3
00000000000033de l       .debug_macro	0000000000000000 $d.4
00000000000033d0 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
00000000000000b0 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000000eaa l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
ffffffff00001ce0 l     F .text	00000000000000a4 .hidden rctee_writev
0000000000000000 l    df *ABS*	0000000000000000 atoi.c
00000000000001ad l       .debug_loclists	0000000000000000 $d.6
00000000000005c9 l       .debug_abbrev	0000000000000000 $d.7
00000000000015fb l       .debug_info	0000000000000000 $d.8
000000000000005a l       .debug_rnglists	0000000000000000 $d.9
0000000000004773 l       .debug_macro	0000000000000000 $d.10
0000000000004660 l       .debug_str_offsets	0000000000000000 $d.11
00000000000194b4 l       .debug_str	0000000000000000 $d.12
00000000000000f0 l       .debug_addr	0000000000000000 $d.13
0000000000000000 l       *ABS*	0000000000000000 $d.14
0000000000000000 l       *ABS*	0000000000000000 $d.15
00000000000011c8 l       .debug_line	0000000000000000 $d.16
00000000000009ae l       .debug_line_str	0000000000000000 $d.17
0000000000000000 l    df *ABS*	0000000000000000 eabi.c
000000000000050e l       .debug_loclists	0000000000000000 $d.2
00000000000006ec l       .debug_abbrev	0000000000000000 $d.3
000000000000180a l       .debug_info	0000000000000000 $d.4
0000000000005bb1 l       .debug_macro	0000000000000000 $d.5
000000000000598c l       .debug_str_offsets	0000000000000000 $d.6
00000000000194b4 l       .debug_str	0000000000000000 $d.7
0000000000000148 l       .debug_addr	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
000000000000180f l       .debug_line	0000000000000000 $d.11
00000000000009ae l       .debug_line_str	0000000000000000 $d.12
0000000000000000 l    df *ABS*	0000000000000000 eabi_unwind_stubs.c
0000000000000782 l       .debug_abbrev	0000000000000000 $d.1
000000000000187f l       .debug_info	0000000000000000 $d.2
000000000000633d l       .debug_macro	0000000000000000 $d.3
00000000000061a4 l       .debug_str_offsets	0000000000000000 $d.4
00000000000194b4 l       .debug_str	0000000000000000 $d.5
0000000000000160 l       .debug_addr	0000000000000000 $d.6
0000000000000000 l       *ABS*	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
000000000000189d l       .debug_line	0000000000000000 $d.9
00000000000009ae l       .debug_line_str	0000000000000000 $d.10
0000000000000000 l    df *ABS*	0000000000000000 io_handle.c
ffffffff00001d84 l       .text	0000000000000000 $x.0
ffffffff00001d98 l       .text	0000000000000000 $x.1
000000000000053d l       .debug_loclists	0000000000000000 $d.2
00000000000007bf l       .debug_abbrev	0000000000000000 $d.3
00000000000018b3 l       .debug_info	0000000000000000 $d.4
00000000000000c6 l       .debug_rnglists	0000000000000000 $d.5
0000000000006c55 l       .debug_macro	0000000000000000 $d.6
0000000000006b1c l       .debug_str_offsets	0000000000000000 $d.7
00000000000194b4 l       .debug_str	0000000000000000 $d.8
0000000000000170 l       .debug_addr	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
0000000000000000 l       *ABS*	0000000000000000 $d.11
000000000000198c l       .debug_line	0000000000000000 $d.12
00000000000009ae l       .debug_line_str	0000000000000000 $d.13
ffffffff00001d84 l     F .text	0000000000000014 .hidden fd_io_handle
ffffffff00001d98 l     F .text	0000000000000034 .hidden file_io_handle
0000000000000000 l    df *ABS*	0000000000000000 printf.c
ffffffff00001ee4 l     F .text	0000000000000044 _vsnprintf_output
ffffffff00001f64 l     F .text	0000000000000984 _printf_engine_internal
ffffffff00001dcc l       .text	0000000000000000 $x.2
ffffffff00001e68 l       .text	0000000000000000 $x.3
ffffffff00001ee4 l       .text	0000000000000000 $x.6
ffffffff00001f28 l       .text	0000000000000000 $x.7
ffffffff00001f64 l       .text	0000000000000000 $x.8
ffffffff0000297c l     F .text	000000000000003c longlong_to_hexstring
ffffffff000028e8 l     F .text	0000000000000094 longlong_to_string
ffffffff0004269e l       .rodata	0000000000000000 $d.9
ffffffff000028e8 l       .text	0000000000000000 $x.10
ffffffff0000297c l       .text	0000000000000000 $x.11
ffffffff000427a4 l     O .rodata	0000000000000010 hextable_caps
ffffffff00042794 l     O .rodata	0000000000000010 hextable
ffffffff0003a59e l       .rodata	0000000000000000 $d.12
ffffffff000427a4 l       .rodata	0000000000000000 $d.13
0000000000000571 l       .debug_loclists	0000000000000000 $d.14
0000000000000881 l       .debug_abbrev	0000000000000000 $d.15
0000000000001a14 l       .debug_info	0000000000000000 $d.16
00000000000000dd l       .debug_rnglists	0000000000000000 $d.17
0000000000007e67 l       .debug_macro	0000000000000000 $d.18
0000000000007c6c l       .debug_str_offsets	0000000000000000 $d.19
00000000000194b4 l       .debug_str	0000000000000000 $d.20
0000000000000188 l       .debug_addr	0000000000000000 $d.21
0000000000000000 l       *ABS*	0000000000000000 $d.22
0000000000000000 l       *ABS*	0000000000000000 $d.23
0000000000001bfa l       .debug_line	0000000000000000 $d.24
00000000000009ae l       .debug_line_str	0000000000000000 $d.25
ffffffff00001dcc l     F .text	000000000000009c .hidden snprintf
ffffffff00001e68 l     F .text	000000000000007c .hidden vsnprintf
ffffffff00001f28 l     F .text	000000000000003c .hidden _printf_engine
0000000000000000 l    df *ABS*	0000000000000000 rand.c
ffffffff000029b8 l       .text	0000000000000000 $x.0
ffffffff0015d010 l     O .data	0000000000000004 randseed
ffffffff000029c4 l       .text	0000000000000000 $x.2
ffffffff0015d010 l       .data	0000000000000000 $d.3
0000000000000f07 l       .debug_loclists	0000000000000000 $d.4
0000000000000b83 l       .debug_abbrev	0000000000000000 $d.5
0000000000002472 l       .debug_info	0000000000000000 $d.6
000000000000023e l       .debug_rnglists	0000000000000000 $d.7
0000000000009133 l       .debug_macro	0000000000000000 $d.8
0000000000008ec4 l       .debug_str_offsets	0000000000000000 $d.9
00000000000194b4 l       .debug_str	0000000000000000 $d.10
0000000000000318 l       .debug_addr	0000000000000000 $d.11
0000000000000000 l       *ABS*	0000000000000000 $d.12
0000000000000000 l       *ABS*	0000000000000000 $d.13
00000000000024ad l       .debug_line	0000000000000000 $d.14
00000000000009ae l       .debug_line_str	0000000000000000 $d.15
ffffffff000029b8 l     F .text	000000000000000c .hidden srand
ffffffff000029c4 l     F .text	0000000000000024 .hidden rand
0000000000000000 l    df *ABS*	0000000000000000 stdio.c
ffffffff000029e8 l       .text	0000000000000000 $x.2
ffffffff00002a50 l       .text	0000000000000000 $x.3
ffffffff00002b08 l     F .text	000000000000003c _fprintf_output_func
ffffffff00002b08 l       .text	0000000000000000 $x.9
ffffffff00002b44 l       .text	0000000000000000 $x.11
ffffffff00002c04 l       .text	0000000000000000 $x.12
0000000000000f47 l       .debug_loclists	0000000000000000 $d.13
0000000000000c61 l       .debug_abbrev	0000000000000000 $d.14
0000000000002545 l       .debug_info	0000000000000000 $d.15
0000000000000258 l       .debug_rnglists	0000000000000000 $d.16
000000000000a1f1 l       .debug_macro	0000000000000000 $d.17
0000000000009ef4 l       .debug_str_offsets	0000000000000000 $d.18
00000000000194b4 l       .debug_str	0000000000000000 $d.19
0000000000000348 l       .debug_addr	0000000000000000 $d.20
0000000000000000 l       *ABS*	0000000000000000 $d.21
0000000000000000 l       *ABS*	0000000000000000 $d.22
00000000000026da l       .debug_line	0000000000000000 $d.23
00000000000009ae l       .debug_line_str	0000000000000000 $d.24
ffffffff00002b44 l     F .text	00000000000000c0 .hidden printf
ffffffff000029e8 l     F .text	0000000000000068 .hidden putchar
ffffffff00002a50 l     F .text	00000000000000b8 .hidden puts
ffffffff00002c04 l     F .text	000000000000009c .hidden vprintf
0000000000000000 l    df *ABS*	0000000000000000 strtol.c
0000000000001526 l       .debug_loclists	0000000000000000 $d.1
0000000000000e7e l       .debug_abbrev	0000000000000000 $d.2
0000000000002f75 l       .debug_info	0000000000000000 $d.3
000000000000b4b3 l       .debug_macro	0000000000000000 $d.4
000000000000b120 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
00000000000005e0 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000002a94 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 strtoll.c
0000000000001729 l       .debug_loclists	0000000000000000 $d.1
0000000000000f30 l       .debug_abbrev	0000000000000000 $d.2
000000000000305a l       .debug_info	0000000000000000 $d.3
000000000000c14b l       .debug_macro	0000000000000000 $d.4
000000000000bdf4 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
00000000000005f8 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000002d22 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 locale_stubs.c
000000000000192c l       .debug_loclists	0000000000000000 $d.6
0000000000000fe2 l       .debug_abbrev	0000000000000000 $d.7
000000000000313f l       .debug_info	0000000000000000 $d.8
00000000000002e5 l       .debug_rnglists	0000000000000000 $d.9
000000000000d42d l       .debug_macro	0000000000000000 $d.10
000000000000cff8 l       .debug_str_offsets	0000000000000000 $d.11
00000000000194b4 l       .debug_str	0000000000000000 $d.12
0000000000000610 l       .debug_addr	0000000000000000 $d.13
0000000000000000 l       *ABS*	0000000000000000 $d.14
0000000000000000 l       *ABS*	0000000000000000 $d.15
000000000000304b l       .debug_line	0000000000000000 $d.16
00000000000009ae l       .debug_line_str	0000000000000000 $d.17
0000000000000000 l    df *ABS*	0000000000000000 atexit.c
00000000000019a3 l       .debug_loclists	0000000000000000 $d.1
00000000000010f9 l       .debug_abbrev	0000000000000000 $d.2
00000000000032e3 l       .debug_info	0000000000000000 $d.3
000000000000dff3 l       .debug_macro	0000000000000000 $d.4
000000000000dba8 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000658 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
00000000000032ae l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 pthreads.c
00000000000019c1 l       .debug_loclists	0000000000000000 $d.12
0000000000001163 l       .debug_abbrev	0000000000000000 $d.13
0000000000003333 l       .debug_info	0000000000000000 $d.14
0000000000000305 l       .debug_rnglists	0000000000000000 $d.15
000000000000e77c l       .debug_macro	0000000000000000 $d.16
000000000000e3b4 l       .debug_str_offsets	0000000000000000 $d.17
00000000000194b4 l       .debug_str	0000000000000000 $d.18
0000000000000668 l       .debug_addr	0000000000000000 $d.19
0000000000000000 l       *ABS*	0000000000000000 $d.20
0000000000000000 l       *ABS*	0000000000000000 $d.21
000000000000333c l       .debug_line	0000000000000000 $d.22
00000000000009ae l       .debug_line_str	0000000000000000 $d.23
0000000000000000 l    df *ABS*	0000000000000000 isalnum.c
0000000000001aa9 l       .debug_loclists	0000000000000000 $d.2
00000000000012a6 l       .debug_abbrev	0000000000000000 $d.3
0000000000003624 l       .debug_info	0000000000000000 $d.4
000000000000033a l       .debug_rnglists	0000000000000000 $d.5
000000000000f808 l       .debug_macro	0000000000000000 $d.6
000000000000f3c8 l       .debug_str_offsets	0000000000000000 $d.7
00000000000194b4 l       .debug_str	0000000000000000 $d.8
00000000000006d0 l       .debug_addr	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
0000000000000000 l       *ABS*	0000000000000000 $d.11
0000000000003626 l       .debug_line	0000000000000000 $d.12
00000000000009ae l       .debug_line_str	0000000000000000 $d.13
0000000000000000 l    df *ABS*	0000000000000000 isalpha.c
0000000000001add l       .debug_loclists	0000000000000000 $d.2
000000000000137a l       .debug_abbrev	0000000000000000 $d.3
00000000000036cb l       .debug_info	0000000000000000 $d.4
0000000000000351 l       .debug_rnglists	0000000000000000 $d.5
0000000000010012 l       .debug_macro	0000000000000000 $d.6
000000000000fc50 l       .debug_str_offsets	0000000000000000 $d.7
00000000000194b4 l       .debug_str	0000000000000000 $d.8
00000000000006e8 l       .debug_addr	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
0000000000000000 l       *ABS*	0000000000000000 $d.11
0000000000003748 l       .debug_line	0000000000000000 $d.12
00000000000009ae l       .debug_line_str	0000000000000000 $d.13
0000000000000000 l    df *ABS*	0000000000000000 isascii.c
0000000000001b11 l       .debug_loclists	0000000000000000 $d.1
000000000000144e l       .debug_abbrev	0000000000000000 $d.2
0000000000003772 l       .debug_info	0000000000000000 $d.3
000000000001081f l       .debug_macro	0000000000000000 $d.4
00000000000104d8 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000700 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000003866 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 isblank.c
0000000000001b2f l       .debug_loclists	0000000000000000 $d.2
00000000000014aa l       .debug_abbrev	0000000000000000 $d.3
00000000000037bc l       .debug_info	0000000000000000 $d.4
0000000000000368 l       .debug_rnglists	0000000000000000 $d.5
0000000000011027 l       .debug_macro	0000000000000000 $d.6
0000000000010d4c l       .debug_str_offsets	0000000000000000 $d.7
00000000000194b4 l       .debug_str	0000000000000000 $d.8
0000000000000710 l       .debug_addr	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
0000000000000000 l       *ABS*	0000000000000000 $d.11
000000000000395a l       .debug_line	0000000000000000 $d.12
00000000000009ae l       .debug_line_str	0000000000000000 $d.13
0000000000000000 l    df *ABS*	0000000000000000 iscntrl.c
0000000000001b63 l       .debug_loclists	0000000000000000 $d.2
000000000000157e l       .debug_abbrev	0000000000000000 $d.3
000000000000385f l       .debug_info	0000000000000000 $d.4
000000000000037f l       .debug_rnglists	0000000000000000 $d.5
0000000000011830 l       .debug_macro	0000000000000000 $d.6
00000000000115d0 l       .debug_str_offsets	0000000000000000 $d.7
00000000000194b4 l       .debug_str	0000000000000000 $d.8
0000000000000728 l       .debug_addr	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
0000000000000000 l       *ABS*	0000000000000000 $d.11
0000000000003a70 l       .debug_line	0000000000000000 $d.12
00000000000009ae l       .debug_line_str	0000000000000000 $d.13
0000000000000000 l    df *ABS*	0000000000000000 isdigit.c
0000000000001b97 l       .debug_loclists	0000000000000000 $d.2
0000000000001652 l       .debug_abbrev	0000000000000000 $d.3
0000000000003906 l       .debug_info	0000000000000000 $d.4
0000000000000396 l       .debug_rnglists	0000000000000000 $d.5
000000000001203a l       .debug_macro	0000000000000000 $d.6
0000000000011e58 l       .debug_str_offsets	0000000000000000 $d.7
00000000000194b4 l       .debug_str	0000000000000000 $d.8
0000000000000748 l       .debug_addr	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
0000000000000000 l       *ABS*	0000000000000000 $d.11
0000000000003b86 l       .debug_line	0000000000000000 $d.12
00000000000009ae l       .debug_line_str	0000000000000000 $d.13
0000000000000000 l    df *ABS*	0000000000000000 isgraph.c
0000000000001bcb l       .debug_loclists	0000000000000000 $d.2
00000000000016da l       .debug_abbrev	0000000000000000 $d.3
000000000000398d l       .debug_info	0000000000000000 $d.4
00000000000003ad l       .debug_rnglists	0000000000000000 $d.5
0000000000012847 l       .debug_macro	0000000000000000 $d.6
00000000000126e0 l       .debug_str_offsets	0000000000000000 $d.7
00000000000194b4 l       .debug_str	0000000000000000 $d.8
0000000000000760 l       .debug_addr	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
0000000000000000 l       *ABS*	0000000000000000 $d.11
0000000000003c9a l       .debug_line	0000000000000000 $d.12
00000000000009ae l       .debug_line_str	0000000000000000 $d.13
0000000000000000 l    df *ABS*	0000000000000000 islower.c
0000000000001bff l       .debug_loclists	0000000000000000 $d.2
00000000000017ae l       .debug_abbrev	0000000000000000 $d.3
0000000000003a34 l       .debug_info	0000000000000000 $d.4
00000000000003c4 l       .debug_rnglists	0000000000000000 $d.5
0000000000013054 l       .debug_macro	0000000000000000 $d.6
0000000000012f68 l       .debug_str_offsets	0000000000000000 $d.7
00000000000194b4 l       .debug_str	0000000000000000 $d.8
0000000000000778 l       .debug_addr	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
0000000000000000 l       *ABS*	0000000000000000 $d.11
0000000000003db2 l       .debug_line	0000000000000000 $d.12
00000000000009ae l       .debug_line_str	0000000000000000 $d.13
0000000000000000 l    df *ABS*	0000000000000000 isprint.c
0000000000001c33 l       .debug_loclists	0000000000000000 $d.2
0000000000001882 l       .debug_abbrev	0000000000000000 $d.3
0000000000003adb l       .debug_info	0000000000000000 $d.4
00000000000003db l       .debug_rnglists	0000000000000000 $d.5
0000000000013861 l       .debug_macro	0000000000000000 $d.6
00000000000137f0 l       .debug_str_offsets	0000000000000000 $d.7
00000000000194b4 l       .debug_str	0000000000000000 $d.8
0000000000000790 l       .debug_addr	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
0000000000000000 l       *ABS*	0000000000000000 $d.11
0000000000003eca l       .debug_line	0000000000000000 $d.12
00000000000009ae l       .debug_line_str	0000000000000000 $d.13
0000000000000000 l    df *ABS*	0000000000000000 ispunct.c
0000000000001c67 l       .debug_loclists	0000000000000000 $d.2
0000000000001956 l       .debug_abbrev	0000000000000000 $d.3
0000000000003b82 l       .debug_info	0000000000000000 $d.4
00000000000003f2 l       .debug_rnglists	0000000000000000 $d.5
000000000001406e l       .debug_macro	0000000000000000 $d.6
0000000000014078 l       .debug_str_offsets	0000000000000000 $d.7
00000000000194b4 l       .debug_str	0000000000000000 $d.8
00000000000007a8 l       .debug_addr	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
0000000000000000 l       *ABS*	0000000000000000 $d.11
0000000000003fe2 l       .debug_line	0000000000000000 $d.12
00000000000009ae l       .debug_line_str	0000000000000000 $d.13
0000000000000000 l    df *ABS*	0000000000000000 isspace.c
0000000000001cbf l       .debug_loclists	0000000000000000 $d.2
0000000000001a1b l       .debug_abbrev	0000000000000000 $d.3
0000000000003c28 l       .debug_info	0000000000000000 $d.4
0000000000000409 l       .debug_rnglists	0000000000000000 $d.5
0000000000014878 l       .debug_macro	0000000000000000 $d.6
0000000000014900 l       .debug_str_offsets	0000000000000000 $d.7
00000000000194b4 l       .debug_str	0000000000000000 $d.8
00000000000007c0 l       .debug_addr	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
0000000000000000 l       *ABS*	0000000000000000 $d.11
000000000000410f l       .debug_line	0000000000000000 $d.12
00000000000009ae l       .debug_line_str	0000000000000000 $d.13
0000000000000000 l    df *ABS*	0000000000000000 isupper.c
0000000000001cf3 l       .debug_loclists	0000000000000000 $d.2
0000000000001aef l       .debug_abbrev	0000000000000000 $d.3
0000000000003ccf l       .debug_info	0000000000000000 $d.4
0000000000000420 l       .debug_rnglists	0000000000000000 $d.5
0000000000015085 l       .debug_macro	0000000000000000 $d.6
0000000000015188 l       .debug_str_offsets	0000000000000000 $d.7
00000000000194b4 l       .debug_str	0000000000000000 $d.8
00000000000007d8 l       .debug_addr	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
0000000000000000 l       *ABS*	0000000000000000 $d.11
000000000000422d l       .debug_line	0000000000000000 $d.12
00000000000009ae l       .debug_line_str	0000000000000000 $d.13
0000000000000000 l    df *ABS*	0000000000000000 isxdigit.c
0000000000001d27 l       .debug_loclists	0000000000000000 $d.2
0000000000001bc3 l       .debug_abbrev	0000000000000000 $d.3
0000000000003d76 l       .debug_info	0000000000000000 $d.4
0000000000000437 l       .debug_rnglists	0000000000000000 $d.5
0000000000015892 l       .debug_macro	0000000000000000 $d.6
0000000000015a10 l       .debug_str_offsets	0000000000000000 $d.7
00000000000194b4 l       .debug_str	0000000000000000 $d.8
00000000000007f0 l       .debug_addr	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
0000000000000000 l       *ABS*	0000000000000000 $d.11
0000000000004345 l       .debug_line	0000000000000000 $d.12
00000000000009ae l       .debug_line_str	0000000000000000 $d.13
0000000000000000 l    df *ABS*	0000000000000000 toascii.c
0000000000001d5b l       .debug_loclists	0000000000000000 $d.1
0000000000001c97 l       .debug_abbrev	0000000000000000 $d.2
0000000000003e1d l       .debug_info	0000000000000000 $d.3
000000000001609c l       .debug_macro	0000000000000000 $d.4
0000000000016298 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000808 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000004467 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 tolower.c
0000000000001d79 l       .debug_loclists	0000000000000000 $d.2
0000000000001cf3 l       .debug_abbrev	0000000000000000 $d.3
0000000000003e67 l       .debug_info	0000000000000000 $d.4
000000000000044e l       .debug_rnglists	0000000000000000 $d.5
00000000000168a1 l       .debug_macro	0000000000000000 $d.6
0000000000016b0c l       .debug_str_offsets	0000000000000000 $d.7
00000000000194b4 l       .debug_str	0000000000000000 $d.8
0000000000000818 l       .debug_addr	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
0000000000000000 l       *ABS*	0000000000000000 $d.11
000000000000455b l       .debug_line	0000000000000000 $d.12
00000000000009ae l       .debug_line_str	0000000000000000 $d.13
0000000000000000 l    df *ABS*	0000000000000000 toupper.c
0000000000001dad l       .debug_loclists	0000000000000000 $d.2
0000000000001dc7 l       .debug_abbrev	0000000000000000 $d.3
0000000000003f0e l       .debug_info	0000000000000000 $d.4
0000000000000465 l       .debug_rnglists	0000000000000000 $d.5
00000000000170ab l       .debug_macro	0000000000000000 $d.6
0000000000017394 l       .debug_str_offsets	0000000000000000 $d.7
00000000000194b4 l       .debug_str	0000000000000000 $d.8
0000000000000830 l       .debug_addr	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
0000000000000000 l       *ABS*	0000000000000000 $d.11
0000000000004670 l       .debug_line	0000000000000000 $d.12
00000000000009ae l       .debug_line_str	0000000000000000 $d.13
0000000000000000 l    df *ABS*	0000000000000000 c_locale.c
ffffffff000427b8 l       .rodata	0000000000000000 $d.2
0000000000001e9b l       .debug_abbrev	0000000000000000 $d.4
0000000000003fb5 l       .debug_info	0000000000000000 $d.5
00000000000178b5 l       .debug_macro	0000000000000000 $d.6
0000000000017c1c l       .debug_str_offsets	0000000000000000 $d.7
00000000000194b4 l       .debug_str	0000000000000000 $d.8
0000000000000850 l       .debug_addr	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
0000000000004785 l       .debug_line	0000000000000000 $d.11
00000000000009ae l       .debug_line_str	0000000000000000 $d.12
ffffffff000427b8 l     O .rodata	0000000000000030 .hidden __c_locale
0000000000000000 l    df *ABS*	0000000000000000 abs.c
0000000000001de1 l       .debug_loclists	0000000000000000 $d.1
0000000000001f39 l       .debug_abbrev	0000000000000000 $d.2
000000000000409c l       .debug_info	0000000000000000 $d.3
00000000000185c3 l       .debug_macro	0000000000000000 $d.4
0000000000018904 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000878 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
000000000000494d l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 bsearch.c
0000000000001dff l       .debug_loclists	0000000000000000 $d.1
0000000000001f95 l       .debug_abbrev	0000000000000000 $d.2
00000000000040e6 l       .debug_info	0000000000000000 $d.3
0000000000018de3 l       .debug_macro	0000000000000000 $d.4
0000000000019190 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000888 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000004a52 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 div.c
0000000000001e74 l       .debug_loclists	0000000000000000 $d.1
000000000000203e l       .debug_abbrev	0000000000000000 $d.2
00000000000041a8 l       .debug_info	0000000000000000 $d.3
000000000001960c l       .debug_macro	0000000000000000 $d.4
0000000000019a40 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
00000000000008a0 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000004b85 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 imaxabs.c
0000000000001e92 l       .debug_loclists	0000000000000000 $d.1
00000000000020d0 l       .debug_abbrev	0000000000000000 $d.2
000000000000421b l       .debug_info	0000000000000000 $d.3
0000000000019e30 l       .debug_macro	0000000000000000 $d.4
000000000001a2dc l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
00000000000008b0 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000004c8a l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 imaxdiv.c
0000000000001eb0 l       .debug_loclists	0000000000000000 $d.1
0000000000002139 l       .debug_abbrev	0000000000000000 $d.2
000000000000426d l       .debug_info	0000000000000000 $d.3
000000000001aa53 l       .debug_macro	0000000000000000 $d.4
000000000001aef4 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
00000000000008c0 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000004da8 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 labs.c
0000000000001ee0 l       .debug_loclists	0000000000000000 $d.1
00000000000021bc l       .debug_abbrev	0000000000000000 $d.2
00000000000042e7 l       .debug_info	0000000000000000 $d.3
000000000001b67a l       .debug_macro	0000000000000000 $d.4
000000000001bb1c l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
00000000000008d0 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000004ec6 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 ldiv.c
0000000000001efe l       .debug_loclists	0000000000000000 $d.1
0000000000002218 l       .debug_abbrev	0000000000000000 $d.2
0000000000004331 l       .debug_info	0000000000000000 $d.3
000000000001be9a l       .debug_macro	0000000000000000 $d.4
000000000001c3a8 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
00000000000008e0 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000004fcb l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 llabs.c
0000000000001f2e l       .debug_loclists	0000000000000000 $d.1
000000000000229b l       .debug_abbrev	0000000000000000 $d.2
00000000000043a3 l       .debug_info	0000000000000000 $d.3
000000000001c6be l       .debug_macro	0000000000000000 $d.4
000000000001cc44 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
00000000000008f0 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
00000000000050d0 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 lldiv.c
0000000000001f4c l       .debug_loclists	0000000000000000 $d.1
00000000000022f7 l       .debug_abbrev	0000000000000000 $d.2
00000000000043ed l       .debug_info	0000000000000000 $d.3
000000000001cede l       .debug_macro	0000000000000000 $d.4
000000000001d4d0 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000900 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
00000000000051d5 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 qsort.c
0000000000001f7c l       .debug_loclists	0000000000000000 $d.3
000000000000237a l       .debug_abbrev	0000000000000000 $d.4
000000000000445f l       .debug_info	0000000000000000 $d.5
000000000000047c l       .debug_rnglists	0000000000000000 $d.6
000000000001d702 l       .debug_macro	0000000000000000 $d.7
000000000001dd6c l       .debug_str_offsets	0000000000000000 $d.8
00000000000194b4 l       .debug_str	0000000000000000 $d.9
0000000000000910 l       .debug_addr	0000000000000000 $d.10
0000000000000000 l       *ABS*	0000000000000000 $d.11
0000000000000000 l       *ABS*	0000000000000000 $d.12
00000000000052da l       .debug_line	0000000000000000 $d.13
00000000000009ae l       .debug_line_str	0000000000000000 $d.14
0000000000000000 l    df *ABS*	0000000000000000 bcmp.c
0000000000002539 l       .debug_abbrev	0000000000000000 $d.1
0000000000004c00 l       .debug_info	0000000000000000 $d.2
000000000001e192 l       .debug_macro	0000000000000000 $d.3
000000000001e860 l       .debug_str_offsets	0000000000000000 $d.4
00000000000194b4 l       .debug_str	0000000000000000 $d.5
0000000000000a28 l       .debug_addr	0000000000000000 $d.6
0000000000000000 l       *ABS*	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000005996 l       .debug_line	0000000000000000 $d.9
00000000000009ae l       .debug_line_str	0000000000000000 $d.10
0000000000000000 l    df *ABS*	0000000000000000 memccpy.c
00000000000025b1 l       .debug_loclists	0000000000000000 $d.1
00000000000025ab l       .debug_abbrev	0000000000000000 $d.2
0000000000004c6d l       .debug_info	0000000000000000 $d.3
00000000000004fe l       .debug_rnglists	0000000000000000 $d.4
000000000001e9ba l       .debug_macro	0000000000000000 $d.5
000000000001f0d4 l       .debug_str_offsets	0000000000000000 $d.6
00000000000194b4 l       .debug_str	0000000000000000 $d.7
0000000000000a38 l       .debug_addr	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
0000000000005ab0 l       .debug_line	0000000000000000 $d.11
00000000000009ae l       .debug_line_str	0000000000000000 $d.12
0000000000000000 l    df *ABS*	0000000000000000 memmem.c
00000000000026ee l       .debug_loclists	0000000000000000 $d.3
0000000000002658 l       .debug_abbrev	0000000000000000 $d.4
0000000000004d5a l       .debug_info	0000000000000000 $d.5
0000000000000518 l       .debug_rnglists	0000000000000000 $d.6
000000000001f522 l       .debug_macro	0000000000000000 $d.7
000000000001fc7c l       .debug_str_offsets	0000000000000000 $d.8
00000000000194b4 l       .debug_str	0000000000000000 $d.9
0000000000000a50 l       .debug_addr	0000000000000000 $d.10
0000000000000000 l       *ABS*	0000000000000000 $d.11
0000000000000000 l       *ABS*	0000000000000000 $d.12
0000000000005d88 l       .debug_line	0000000000000000 $d.13
00000000000009ae l       .debug_line_str	0000000000000000 $d.14
0000000000000000 l    df *ABS*	0000000000000000 mempcpy.c
0000000000002b8a l       .debug_loclists	0000000000000000 $d.1
00000000000027e0 l       .debug_abbrev	0000000000000000 $d.2
0000000000004fe3 l       .debug_info	0000000000000000 $d.3
000000000001fefd l       .debug_macro	0000000000000000 $d.4
00000000000206c0 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000a88 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000006167 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 memrchr.c
0000000000002bd6 l       .debug_loclists	0000000000000000 $d.1
0000000000002857 l       .debug_abbrev	0000000000000000 $d.2
0000000000005057 l       .debug_info	0000000000000000 $d.3
0000000000020729 l       .debug_macro	0000000000000000 $d.4
0000000000020f38 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000a98 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000006289 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 stpcpy.c
0000000000002c2d l       .debug_loclists	0000000000000000 $d.1
00000000000028f6 l       .debug_abbrev	0000000000000000 $d.2
00000000000050e4 l       .debug_info	0000000000000000 $d.3
0000000000020f20 l       .debug_macro	0000000000000000 $d.4
00000000000217ac l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000aa8 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
00000000000063a4 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 stpncpy.c
0000000000002ce6 l       .debug_loclists	0000000000000000 $d.1
000000000000298d l       .debug_abbrev	0000000000000000 $d.2
0000000000005194 l       .debug_info	0000000000000000 $d.3
0000000000021a81 l       .debug_macro	0000000000000000 $d.4
0000000000022338 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000ab8 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
000000000000660b l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 strcasecmp.c
0000000000002dad l       .debug_loclists	0000000000000000 $d.2
0000000000002a31 l       .debug_abbrev	0000000000000000 $d.3
0000000000005252 l       .debug_info	0000000000000000 $d.4
0000000000000545 l       .debug_rnglists	0000000000000000 $d.5
00000000000225e4 l       .debug_macro	0000000000000000 $d.6
0000000000022ecc l       .debug_str_offsets	0000000000000000 $d.7
00000000000194b4 l       .debug_str	0000000000000000 $d.8
0000000000000ad0 l       .debug_addr	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
0000000000000000 l       *ABS*	0000000000000000 $d.11
00000000000068c4 l       .debug_line	0000000000000000 $d.12
00000000000009ae l       .debug_line_str	0000000000000000 $d.13
0000000000000000 l    df *ABS*	0000000000000000 strcasestr.c
0000000000002e82 l       .debug_loclists	0000000000000000 $d.1
0000000000002b18 l       .debug_abbrev	0000000000000000 $d.2
0000000000005356 l       .debug_info	0000000000000000 $d.3
0000000000022e25 l       .debug_macro	0000000000000000 $d.4
0000000000023770 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000af0 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000006a4b l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 strchrnul.c
0000000000002ec3 l       .debug_loclists	0000000000000000 $d.1
0000000000002bca l       .debug_abbrev	0000000000000000 $d.2
0000000000005402 l       .debug_info	0000000000000000 $d.3
0000000000023653 l       .debug_macro	0000000000000000 $d.4
0000000000023ff0 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000b08 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000006b85 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 strcspn.c
0000000000002f41 l       .debug_loclists	0000000000000000 $d.1
0000000000002c52 l       .debug_abbrev	0000000000000000 $d.2
00000000000054aa l       .debug_info	0000000000000000 $d.3
00000000000241b6 l       .debug_macro	0000000000000000 $d.4
0000000000024b84 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000b18 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000006dcc l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 strerror_r.c
0000000000002f8b l       .debug_loclists	0000000000000000 $d.1
0000000000002cfd l       .debug_abbrev	0000000000000000 $d.2
000000000000553f l       .debug_info	0000000000000000 $d.3
00000000000249b2 l       .debug_macro	0000000000000000 $d.4
0000000000025400 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000b28 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000006f1a l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 strncasecmp.c
0000000000002ffc l       .debug_loclists	0000000000000000 $d.2
0000000000002d79 l       .debug_abbrev	0000000000000000 $d.3
00000000000055c2 l       .debug_info	0000000000000000 $d.4
000000000000055e l       .debug_rnglists	0000000000000000 $d.5
00000000000253eb l       .debug_macro	0000000000000000 $d.6
0000000000025ea4 l       .debug_str_offsets	0000000000000000 $d.7
00000000000194b4 l       .debug_str	0000000000000000 $d.8
0000000000000b38 l       .debug_addr	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
0000000000000000 l       *ABS*	0000000000000000 $d.11
000000000000708f l       .debug_line	0000000000000000 $d.12
00000000000009ae l       .debug_line_str	0000000000000000 $d.13
0000000000000000 l    df *ABS*	0000000000000000 strndup.c
0000000000003143 l       .debug_loclists	0000000000000000 $d.1
0000000000002e6d l       .debug_abbrev	0000000000000000 $d.2
00000000000056ef l       .debug_info	0000000000000000 $d.3
0000000000025c2f l       .debug_macro	0000000000000000 $d.4
0000000000026754 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000b50 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
000000000000723d l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 strsep.c
000000000000318c l       .debug_loclists	0000000000000000 $d.1
0000000000002f1f l       .debug_abbrev	0000000000000000 $d.2
0000000000005797 l       .debug_info	0000000000000000 $d.3
0000000000026495 l       .debug_macro	0000000000000000 $d.4
0000000000027008 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000b68 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
000000000000737d l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 strtok_r.c
00000000000031cd l       .debug_loclists	0000000000000000 $d.1
0000000000002f95 l       .debug_abbrev	0000000000000000 $d.2
0000000000005810 l       .debug_info	0000000000000000 $d.3
0000000000026cc0 l       .debug_macro	0000000000000000 $d.4
000000000002787c l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000b78 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
00000000000074bb l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 strverscmp.c
0000000000003218 l       .debug_loclists	0000000000000000 $d.1
0000000000003003 l       .debug_abbrev	0000000000000000 $d.2
000000000000588f l       .debug_info	0000000000000000 $d.3
0000000000000577 l       .debug_rnglists	0000000000000000 $d.4
00000000000274b3 l       .debug_macro	0000000000000000 $d.5
00000000000280e0 l       .debug_str_offsets	0000000000000000 $d.6
00000000000194b4 l       .debug_str	0000000000000000 $d.7
0000000000000b88 l       .debug_addr	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
00000000000075f1 l       .debug_line	0000000000000000 $d.11
00000000000009ae l       .debug_line_str	0000000000000000 $d.12
0000000000000000 l    df *ABS*	0000000000000000 swab.c
0000000000003344 l       .debug_loclists	0000000000000000 $d.1
00000000000030b5 l       .debug_abbrev	0000000000000000 $d.2
000000000000595c l       .debug_info	0000000000000000 $d.3
0000000000027d3b l       .debug_macro	0000000000000000 $d.4
00000000000289a8 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000b98 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
00000000000077c4 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 stderr.c
ffffffff00168000 l     O .bss	0000000000000080 buf
ffffffff00168000 l       .bss	0000000000000000 $d.0
ffffffff0015d018 l       .data	0000000000000000 $d.1
0000000000003168 l       .debug_abbrev	0000000000000000 $d.4
00000000000059f4 l       .debug_info	0000000000000000 $d.5
0000000000028a78 l       .debug_macro	0000000000000000 $d.6
0000000000029660 l       .debug_str_offsets	0000000000000000 $d.7
00000000000194b4 l       .debug_str	0000000000000000 $d.8
0000000000000ba8 l       .debug_addr	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
00000000000078f8 l       .debug_line	0000000000000000 $d.11
00000000000009ae l       .debug_line_str	0000000000000000 $d.12
ffffffff0015d018 l     O .data	00000000000000e8 .hidden __stderr_FILE
0000000000000000 l    df *ABS*	0000000000000000 stdin.c
ffffffff00168080 l     O .bss	0000000000000008 buf
ffffffff00168080 l       .bss	0000000000000000 $d.0
ffffffff0015d100 l       .data	0000000000000000 $d.1
0000000000003233 l       .debug_abbrev	0000000000000000 $d.4
0000000000005c30 l       .debug_info	0000000000000000 $d.5
00000000000294ba l       .debug_macro	0000000000000000 $d.6
000000000002a13c l       .debug_str_offsets	0000000000000000 $d.7
00000000000194b4 l       .debug_str	0000000000000000 $d.8
0000000000000bd0 l       .debug_addr	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
0000000000007a5f l       .debug_line	0000000000000000 $d.11
00000000000009ae l       .debug_line_str	0000000000000000 $d.12
ffffffff0015d100 l     O .data	00000000000000e8 .hidden __stdin_FILE
0000000000000000 l    df *ABS*	0000000000000000 stdout.c
ffffffff00168088 l     O .bss	0000000000000080 buf
ffffffff00168088 l       .bss	0000000000000000 $d.0
ffffffff0015d1e8 l       .data	0000000000000000 $d.1
00000000000032fe l       .debug_abbrev	0000000000000000 $d.4
0000000000005e6c l       .debug_info	0000000000000000 $d.5
0000000000029efc l       .debug_macro	0000000000000000 $d.6
000000000002ac18 l       .debug_str_offsets	0000000000000000 $d.7
00000000000194b4 l       .debug_str	0000000000000000 $d.8
0000000000000bf8 l       .debug_addr	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
0000000000007bc6 l       .debug_line	0000000000000000 $d.11
00000000000009ae l       .debug_line_str	0000000000000000 $d.12
ffffffff0015d1e8 l     O .data	00000000000000e8 .hidden __stdout_FILE
0000000000000000 l    df *ABS*	0000000000000000 __stdio_close.c
ffffffff00002ca0 l       .text	0000000000000000 $x.1
0000000000003384 l       .debug_loclists	0000000000000000 $d.2
00000000000033c9 l       .debug_abbrev	0000000000000000 $d.3
00000000000060a8 l       .debug_info	0000000000000000 $d.4
000000000000058e l       .debug_rnglists	0000000000000000 $d.5
000000000002a93e l       .debug_macro	0000000000000000 $d.6
000000000002b6f4 l       .debug_str_offsets	0000000000000000 $d.7
00000000000194b4 l       .debug_str	0000000000000000 $d.8
0000000000000c20 l       .debug_addr	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
0000000000000000 l       *ABS*	0000000000000000 $d.11
0000000000007d2d l       .debug_line	0000000000000000 $d.12
00000000000009ae l       .debug_line_str	0000000000000000 $d.13
ffffffff00002ca0 l     F .text	0000000000000008 .hidden __stdio_close
0000000000000000 l    df *ABS*	0000000000000000 __stdio_read.c
ffffffff00002ca8 l       .text	0000000000000000 $x.0
00000000000033a4 l       .debug_loclists	0000000000000000 $d.1
00000000000034bc l       .debug_abbrev	0000000000000000 $d.2
00000000000062e2 l       .debug_info	0000000000000000 $d.3
000000000002b37d l       .debug_macro	0000000000000000 $d.4
000000000002c1d0 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000c38 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000007ec4 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
ffffffff00002ca8 l     F .text	0000000000000018 .hidden __stdio_read
0000000000000000 l    df *ABS*	0000000000000000 __stdio_write.c
ffffffff00002cc0 l       .text	0000000000000000 $x.0
00000000000033bf l       .debug_loclists	0000000000000000 $d.1
0000000000003590 l       .debug_abbrev	0000000000000000 $d.2
000000000000650e l       .debug_info	0000000000000000 $d.3
000000000002bdf8 l       .debug_macro	0000000000000000 $d.4
000000000002ccbc l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000c48 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000008061 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
ffffffff00002cc0 l     F .text	0000000000000128 .hidden __stdio_write
0000000000000000 l    df *ABS*	0000000000000000 __stdio_seek.c
ffffffff00002de8 l       .text	0000000000000000 $x.0
0000000000003497 l       .debug_loclists	0000000000000000 $d.1
00000000000036cf l       .debug_abbrev	0000000000000000 $d.2
00000000000067e6 l       .debug_info	0000000000000000 $d.3
000000000002c882 l       .debug_macro	0000000000000000 $d.4
000000000002d7d4 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000c68 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
00000000000082af l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
ffffffff00002de8 l     F .text	0000000000000008 .hidden __stdio_seek
0000000000000000 l    df *ABS*	0000000000000000 __ctype_get_mb_cur_max.c
00000000000037a3 l       .debug_abbrev	0000000000000000 $d.1
0000000000006a12 l       .debug_info	0000000000000000 $d.2
000000000002d2c1 l       .debug_macro	0000000000000000 $d.3
000000000002e2b0 l       .debug_str_offsets	0000000000000000 $d.4
00000000000194b4 l       .debug_str	0000000000000000 $d.5
0000000000000c78 l       .debug_addr	0000000000000000 $d.6
0000000000000000 l       *ABS*	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
000000000000842d l       .debug_line	0000000000000000 $d.9
00000000000009ae l       .debug_line_str	0000000000000000 $d.10
0000000000000000 l    df *ABS*	0000000000000000 internal.c
0000000000003851 l       .debug_abbrev	0000000000000000 $d.1
0000000000006acd l       .debug_info	0000000000000000 $d.2
000000000002de42 l       .debug_macro	0000000000000000 $d.3
000000000002ee30 l       .debug_str_offsets	0000000000000000 $d.4
00000000000194b4 l       .debug_str	0000000000000000 $d.5
0000000000000c88 l       .debug_addr	0000000000000000 $d.6
0000000000000000 l       *ABS*	0000000000000000 $d.7
00000000000085e7 l       .debug_line	0000000000000000 $d.8
00000000000009ae l       .debug_line_str	0000000000000000 $d.9
0000000000000000 l    df *ABS*	0000000000000000 mbtowc.c
00000000000034b5 l       .debug_loclists	0000000000000000 $d.1
00000000000038b8 l       .debug_abbrev	0000000000000000 $d.2
0000000000006b1d l       .debug_info	0000000000000000 $d.3
000000000002eb77 l       .debug_macro	0000000000000000 $d.4
000000000002fb14 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000c98 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
00000000000087c8 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 wcrtomb.c
000000000000357a l       .debug_loclists	0000000000000000 $d.1
00000000000039a5 l       .debug_abbrev	0000000000000000 $d.2
0000000000006c5a l       .debug_info	0000000000000000 $d.3
000000000002fb7b l       .debug_macro	0000000000000000 $d.4
0000000000030a9c l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000cb0 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000008ab8 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 bcopy.c
000000000000361a l       .debug_loclists	0000000000000000 $d.1
0000000000003a85 l       .debug_abbrev	0000000000000000 $d.2
0000000000006d79 l       .debug_info	0000000000000000 $d.3
0000000000030b7b l       .debug_macro	0000000000000000 $d.4
0000000000031a14 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000cc0 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000008d88 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 bzero.c
0000000000003649 l       .debug_loclists	0000000000000000 $d.1
0000000000003b0c l       .debug_abbrev	0000000000000000 $d.2
0000000000006de1 l       .debug_info	0000000000000000 $d.3
0000000000031b8d l       .debug_macro	0000000000000000 $d.4
0000000000032960 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000cd0 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000008f54 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 memchr.c
0000000000003664 l       .debug_loclists	0000000000000000 $d.1
0000000000003b87 l       .debug_abbrev	0000000000000000 $d.2
0000000000006e3a l       .debug_info	0000000000000000 $d.3
0000000000032b9e l       .debug_macro	0000000000000000 $d.4
00000000000338a8 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000ce0 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000009120 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 memcmp.c
00000000000036a9 l       .debug_loclists	0000000000000000 $d.1
0000000000003c35 l       .debug_abbrev	0000000000000000 $d.2
0000000000006ed4 l       .debug_info	0000000000000000 $d.3
0000000000033bb5 l       .debug_macro	0000000000000000 $d.4
0000000000034808 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000cf0 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000009309 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 memcpy.c
ffffffff00002df0 l       .text	0000000000000000 $x.0
0000000000003700 l       .debug_loclists	0000000000000000 $d.1
0000000000003cde l       .debug_abbrev	0000000000000000 $d.2
0000000000006f6d l       .debug_info	0000000000000000 $d.3
0000000000034ca6 l       .debug_macro	0000000000000000 $d.4
0000000000035830 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000d00 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000009510 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
ffffffff00002df0 l     F .text	0000000000000110 .hidden memcpy
0000000000000000 l    df *ABS*	0000000000000000 memmove.c
000000000000386f l       .debug_loclists	0000000000000000 $d.1
0000000000003d7d l       .debug_abbrev	0000000000000000 $d.2
0000000000007025 l       .debug_info	0000000000000000 $d.3
0000000000035cc8 l       .debug_macro	0000000000000000 $d.4
00000000000367a4 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000d10 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
000000000000979e l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 memset.c
ffffffff00002f00 l       .text	0000000000000000 $x.0
0000000000003ad8 l       .debug_loclists	0000000000000000 $d.1
0000000000003e1c l       .debug_abbrev	0000000000000000 $d.2
00000000000070dd l       .debug_info	0000000000000000 $d.3
0000000000036cea l       .debug_macro	0000000000000000 $d.4
0000000000037718 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000d20 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000009aee l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
ffffffff00002f00 l     F .text	000000000000016c .hidden memset
0000000000000000 l    df *ABS*	0000000000000000 strcat.c
0000000000003ba1 l       .debug_loclists	0000000000000000 $d.1
0000000000003ebe l       .debug_abbrev	0000000000000000 $d.2
0000000000007197 l       .debug_info	0000000000000000 $d.3
0000000000037d06 l       .debug_macro	0000000000000000 $d.4
000000000003868c l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000d30 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000009d84 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 strchr.c
0000000000003bc8 l       .debug_loclists	0000000000000000 $d.1
0000000000003f37 l       .debug_abbrev	0000000000000000 $d.2
0000000000007203 l       .debug_info	0000000000000000 $d.3
0000000000038d17 l       .debug_macro	0000000000000000 $d.4
00000000000395d4 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000d40 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000009f69 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 strcmp.c
ffffffff0000306c l       .text	0000000000000000 $x.0
0000000000003bde l       .debug_loclists	0000000000000000 $d.1
0000000000003fb0 l       .debug_abbrev	0000000000000000 $d.2
000000000000726a l       .debug_info	0000000000000000 $d.3
0000000000039d28 l       .debug_macro	0000000000000000 $d.4
000000000003a51c l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000d50 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
000000000000a14f l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
ffffffff0000306c l     F .text	000000000000001c .hidden strcmp
0000000000000000 l    df *ABS*	0000000000000000 strcoll.c
0000000000004029 l       .debug_abbrev	0000000000000000 $d.1
00000000000072f4 l       .debug_info	0000000000000000 $d.2
000000000003ad3d l       .debug_macro	0000000000000000 $d.3
000000000003b474 l       .debug_str_offsets	0000000000000000 $d.4
00000000000194b4 l       .debug_str	0000000000000000 $d.5
0000000000000d60 l       .debug_addr	0000000000000000 $d.6
0000000000000000 l       *ABS*	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
000000000000a329 l       .debug_line	0000000000000000 $d.9
00000000000009ae l       .debug_line_str	0000000000000000 $d.10
0000000000000000 l    df *ABS*	0000000000000000 strcpy.c
0000000000003c33 l       .debug_loclists	0000000000000000 $d.1
0000000000004090 l       .debug_abbrev	0000000000000000 $d.2
0000000000007353 l       .debug_info	0000000000000000 $d.3
000000000003b530 l       .debug_macro	0000000000000000 $d.4
000000000003bcd8 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000d70 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
000000000000a42c l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 strdup.c
ffffffff00003088 l       .text	0000000000000000 $x.0
0000000000003c53 l       .debug_loclists	0000000000000000 $d.1
0000000000004109 l       .debug_abbrev	0000000000000000 $d.2
00000000000073bf l       .debug_info	0000000000000000 $d.3
000000000003c541 l       .debug_macro	0000000000000000 $d.4
000000000003cc20 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000d80 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
000000000000a601 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
ffffffff00003088 l     F .text	0000000000000058 .hidden strdup
0000000000000000 l    df *ABS*	0000000000000000 strerror.c
0000000000003c8a l       .debug_loclists	0000000000000000 $d.2
0000000000004191 l       .debug_abbrev	0000000000000000 $d.3
0000000000007437 l       .debug_info	0000000000000000 $d.4
000000000003cda5 l       .debug_macro	0000000000000000 $d.5
000000000003d4cc l       .debug_str_offsets	0000000000000000 $d.6
00000000000194b4 l       .debug_str	0000000000000000 $d.7
0000000000000d90 l       .debug_addr	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
000000000000a740 l       .debug_line	0000000000000000 $d.11
00000000000009ae l       .debug_line_str	0000000000000000 $d.12
0000000000000000 l    df *ABS*	0000000000000000 strlcat.c
0000000000003ca8 l       .debug_loclists	0000000000000000 $d.1
0000000000004223 l       .debug_abbrev	0000000000000000 $d.2
00000000000074bf l       .debug_info	0000000000000000 $d.3
000000000003ddb6 l       .debug_macro	0000000000000000 $d.4
000000000003e414 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000db0 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
000000000000a914 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 strlcpy.c
ffffffff000030e0 l       .text	0000000000000000 $x.0
0000000000003d2f l       .debug_loclists	0000000000000000 $d.1
00000000000042d5 l       .debug_abbrev	0000000000000000 $d.2
0000000000007570 l       .debug_info	0000000000000000 $d.3
000000000003edcc l       .debug_macro	0000000000000000 $d.4
000000000003f370 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000dc8 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
000000000000ab35 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
ffffffff000030e0 l     F .text	0000000000000068 .hidden strlcpy
0000000000000000 l    df *ABS*	0000000000000000 strlen.c
ffffffff00003148 l       .text	0000000000000000 $x.0
0000000000003d9e l       .debug_loclists	0000000000000000 $d.1
0000000000004358 l       .debug_abbrev	0000000000000000 $d.2
00000000000075f0 l       .debug_info	0000000000000000 $d.3
000000000003fde0 l       .debug_macro	0000000000000000 $d.4
00000000000402c4 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000dd8 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
000000000000ad31 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
ffffffff00003148 l     F .text	0000000000000018 .hidden strlen
0000000000000000 l    df *ABS*	0000000000000000 strncat.c
0000000000003dcc l       .debug_loclists	0000000000000000 $d.1
00000000000043de l       .debug_abbrev	0000000000000000 $d.2
0000000000007659 l       .debug_info	0000000000000000 $d.3
0000000000040df2 l       .debug_macro	0000000000000000 $d.4
0000000000041210 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000de8 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
000000000000af04 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 strncpy.c
ffffffff00003160 l       .text	0000000000000000 $x.0
0000000000003e04 l       .debug_loclists	0000000000000000 $d.1
0000000000004464 l       .debug_abbrev	0000000000000000 $d.2
00000000000076da l       .debug_info	0000000000000000 $d.3
0000000000041e06 l       .debug_macro	0000000000000000 $d.4
0000000000042164 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000df8 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
000000000000b0f7 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
ffffffff00003160 l     F .text	0000000000000020 .hidden strncpy
0000000000000000 l    df *ABS*	0000000000000000 strncmp.c
ffffffff00003180 l       .text	0000000000000000 $x.0
0000000000003e35 l       .debug_loclists	0000000000000000 $d.1
00000000000044ea l       .debug_abbrev	0000000000000000 $d.2
000000000000775b l       .debug_info	0000000000000000 $d.3
0000000000042e1a l       .debug_macro	0000000000000000 $d.4
00000000000430b8 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000e08 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
000000000000b2cf l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
ffffffff00003180 l     F .text	0000000000000040 .hidden strncmp
0000000000000000 l    df *ABS*	0000000000000000 strnicmp.c
0000000000003ef1 l       .debug_loclists	0000000000000000 $d.1
0000000000004570 l       .debug_abbrev	0000000000000000 $d.2
00000000000077fa l       .debug_info	0000000000000000 $d.3
0000000000043e32 l       .debug_macro	0000000000000000 $d.4
000000000004401c l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000e18 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
000000000000b4b5 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 strnlen.c
ffffffff000031c0 l       .text	0000000000000000 $x.0
0000000000003f87 l       .debug_loclists	0000000000000000 $d.1
00000000000045f3 l       .debug_abbrev	0000000000000000 $d.2
0000000000007886 l       .debug_info	0000000000000000 $d.3
0000000000044e9c l       .debug_macro	0000000000000000 $d.4
0000000000044fa8 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000e28 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
000000000000b6c4 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
ffffffff000031c0 l     F .text	0000000000000030 .hidden strnlen
0000000000000000 l    df *ABS*	0000000000000000 strpbrk.c
0000000000003fb4 l       .debug_loclists	0000000000000000 $d.1
0000000000004688 l       .debug_abbrev	0000000000000000 $d.2
00000000000078f9 l       .debug_info	0000000000000000 $d.3
0000000000045eaf l       .debug_macro	0000000000000000 $d.4
0000000000045ef8 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000e38 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
000000000000b8b0 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 strrchr.c
0000000000004006 l       .debug_loclists	0000000000000000 $d.1
0000000000004701 l       .debug_abbrev	0000000000000000 $d.2
000000000000796d l       .debug_info	0000000000000000 $d.3
0000000000046ec1 l       .debug_macro	0000000000000000 $d.4
0000000000046e44 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000e48 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
000000000000baa4 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 strspn.c
0000000000004026 l       .debug_loclists	0000000000000000 $d.1
0000000000004789 l       .debug_abbrev	0000000000000000 $d.2
00000000000079dd l       .debug_info	0000000000000000 $d.3
0000000000047ed3 l       .debug_macro	0000000000000000 $d.4
0000000000047d90 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000e58 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
000000000000bc85 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 strstr.c
000000000000408a l       .debug_loclists	0000000000000000 $d.1
000000000000480f l       .debug_abbrev	0000000000000000 $d.2
0000000000007a61 l       .debug_info	0000000000000000 $d.3
0000000000048ee8 l       .debug_macro	0000000000000000 $d.4
0000000000048ce8 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000e68 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
000000000000be7c l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 strtok.c
00000000000040e4 l       .debug_loclists	0000000000000000 $d.2
0000000000004885 l       .debug_abbrev	0000000000000000 $d.3
0000000000007ad9 l       .debug_info	0000000000000000 $d.4
0000000000049efb l       .debug_macro	0000000000000000 $d.5
0000000000049c38 l       .debug_str_offsets	0000000000000000 $d.6
00000000000194b4 l       .debug_str	0000000000000000 $d.7
0000000000000e78 l       .debug_addr	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
0000000000000000 l       *ABS*	0000000000000000 $d.10
000000000000c079 l       .debug_line	0000000000000000 $d.11
00000000000009ae l       .debug_line_str	0000000000000000 $d.12
0000000000000000 l    df *ABS*	0000000000000000 strxfrm.c
0000000000004125 l       .debug_loclists	0000000000000000 $d.1
000000000000490a l       .debug_abbrev	0000000000000000 $d.2
0000000000007b58 l       .debug_info	0000000000000000 $d.3
000000000004af0e l       .debug_macro	0000000000000000 $d.4
000000000004ab88 l       .debug_str_offsets	0000000000000000 $d.5
00000000000194b4 l       .debug_str	0000000000000000 $d.6
0000000000000e90 l       .debug_addr	0000000000000000 $d.7
0000000000000000 l       *ABS*	0000000000000000 $d.8
0000000000000000 l       *ABS*	0000000000000000 $d.9
000000000000c278 l       .debug_line	0000000000000000 $d.10
00000000000009ae l       .debug_line_str	0000000000000000 $d.11
0000000000000000 l    df *ABS*	0000000000000000 pure_virtual.cpp
000000000000418a l       .debug_loclists	0000000000000000 $d.3
0000000000004996 l       .debug_abbrev	0000000000000000 $d.4
0000000000007be8 l       .debug_info	0000000000000000 $d.5
00000000000005a5 l       .debug_rnglists	0000000000000000 $d.6
000000000004bf23 l       .debug_macro	0000000000000000 $d.7
000000000004bae0 l       .debug_str_offsets	0000000000000000 $d.8
00000000000194b4 l       .debug_str	0000000000000000 $d.9
0000000000000ea8 l       .debug_addr	0000000000000000 $d.10
0000000000000000 l       *ABS*	0000000000000000 $d.11
0000000000000000 l       *ABS*	0000000000000000 $d.12
000000000000c45c l       .debug_line	0000000000000000 $d.13
00000000000009ae l       .debug_line_str	0000000000000000 $d.14
ffffffff000031f0 l       .text	0000000000000000 $x.0
ffffffff00003238 l       .text	0000000000000000 $d.1
0000000000000160 l       .debug_aranges	0000000000000000 $d.2
0000000000004a60 l       .debug_abbrev	0000000000000000 $d.3
0000000000007c89 l       .debug_info	0000000000000000 $d.4
000000000000c6e0 l       .debug_line	0000000000000000 $d.5
00000000000009ae l       .debug_line_str	0000000000000000 $d.6
ffffffff00003240 l       .text	0000000000000000 $x.0
0000000000000190 l       .debug_aranges	0000000000000000 $d.1
0000000000004a81 l       .debug_abbrev	0000000000000000 $d.2
0000000000007ddb l       .debug_info	0000000000000000 $d.3
000000000000c737 l       .debug_line	0000000000000000 $d.4
00000000000009ae l       .debug_line_str	0000000000000000 $d.5
ffffffff00003260 l       .text	0000000000000000 $x.0
00000000000001c0 l       .debug_aranges	0000000000000000 $d.1
0000000000004aa2 l       .debug_abbrev	0000000000000000 $d.2
0000000000007ef9 l       .debug_info	0000000000000000 $d.3
000000000000c784 l       .debug_line	0000000000000000 $d.4
00000000000009ae l       .debug_line_str	0000000000000000 $d.5
ffffffff00003260 l     F .text	0000000000000000 .hidden arm64_syscall
ffffffff00044000 l       .__rctee_app	0000000000000000 .ta.body.start
ffffffff00044000 l       .__rctee_app	0000000000000000 $d.0
ffffffff00107000 l       .__rctee_app	0000000000000000 .ta.body.end
ffffffff000435b0 l       .__manifest_data	0000000000000000 .ta.manifest.start
ffffffff000435b0 l       .__manifest_data	0000000000000000 $d.1
ffffffff00043600 l       .__manifest_data	0000000000000000 .ta.manifest.end
ffffffff0015c000 l       .__rctee_app_list	0000000000000000 $d.2
000000000000c7de l       .debug_line	0000000000000000 $d.3
00000000000009ae l       .debug_line_str	0000000000000000 $d.4
ffffffff00107000 l       .__rctee_app	0000000000000000 .ta.body.start
ffffffff00107000 l       .__rctee_app	0000000000000000 $d.0
ffffffff0012f000 l       .__rctee_app	0000000000000000 .ta.body.end
ffffffff00043600 l       .__manifest_data	0000000000000000 .ta.manifest.start
ffffffff00043600 l       .__manifest_data	0000000000000000 $d.1
ffffffff00043644 l       .__manifest_data	0000000000000000 .ta.manifest.end
ffffffff0015c020 l       .__rctee_app_list	0000000000000000 $d.2
000000000000c80f l       .debug_line	0000000000000000 $d.3
00000000000009ae l       .debug_line_str	0000000000000000 $d.4
ffffffff0012f000 l       .__rctee_app	0000000000000000 .ta.body.start
ffffffff0012f000 l       .__rctee_app	0000000000000000 $d.0
ffffffff00140000 l       .__rctee_app	0000000000000000 .ta.body.end
ffffffff00043644 l       .__manifest_data	0000000000000000 .ta.manifest.start
ffffffff00043644 l       .__manifest_data	0000000000000000 $d.1
ffffffff000436bc l       .__manifest_data	0000000000000000 .ta.manifest.end
ffffffff0015c040 l       .__rctee_app_list	0000000000000000 $d.2
000000000000c840 l       .debug_line	0000000000000000 $d.3
00000000000009ae l       .debug_line_str	0000000000000000 $d.4
ffffffff00140000 l       .__rctee_app	0000000000000000 .ta.body.start
ffffffff00140000 l       .__rctee_app	0000000000000000 $d.0
ffffffff0014d000 l       .__rctee_app	0000000000000000 .ta.body.end
ffffffff000436bc l       .__manifest_data	0000000000000000 .ta.manifest.start
ffffffff000436bc l       .__manifest_data	0000000000000000 $d.1
ffffffff00043764 l       .__manifest_data	0000000000000000 .ta.manifest.end
ffffffff0015c060 l       .__rctee_app_list	0000000000000000 $d.2
000000000000c871 l       .debug_line	0000000000000000 $d.3
00000000000009ae l       .debug_line_str	0000000000000000 $d.4
ffffffff0014d000 l       .__rctee_app	0000000000000000 .ta.body.start
ffffffff0014d000 l       .__rctee_app	0000000000000000 $d.0
ffffffff0015c000 l       .__rctee_app	0000000000000000 .ta.body.end
ffffffff00043764 l       .__manifest_data	0000000000000000 .ta.manifest.start
ffffffff00043764 l       .__manifest_data	0000000000000000 $d.1
ffffffff000437bc l       .__manifest_data	0000000000000000 .ta.manifest.end
ffffffff0015c080 l       .__rctee_app_list	0000000000000000 $d.2
000000000000c8a2 l       .debug_line	0000000000000000 $d.3
00000000000009ae l       .debug_line_str	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 addtf3.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 comparetf2.c
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l       *ABS*	0000000000000000 $d.5
0000000000000000 l    df *ABS*	0000000000000000 divtc3.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 divtf3.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 extenddftf2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 extendhftf2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 extendsftf2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 fixtfdi.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 fixtfsi.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 fixtfti.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 fixunstfdi.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 fixunstfsi.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 fixunstfti.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 floatditf.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 floatsitf.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 floattitf.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 floatunditf.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 floatunsitf.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 floatuntitf.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 multc3.c
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l       *ABS*	0000000000000000 $d.5
0000000000000000 l    df *ABS*	0000000000000000 multf3.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 powitf2.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 subtf3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 trunctfdf2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 trunctfhf2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 trunctfsf2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 absvdi2.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 absvsi2.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 absvti2.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 adddf3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 addsf3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 addvdi3.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 addvsi3.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 addvti3.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 apple_versioning.c
0000000000000000 l       *ABS*	0000000000000000 $d.1
0000000000000000 l    df *ABS*	0000000000000000 ashldi3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 ashlti3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 ashrdi3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 ashrti3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 bswapdi2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 bswapsi2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 clzdi2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 clzsi2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 clzti2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 cmpdi2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 cmpti2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 comparedf2.c
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l       *ABS*	0000000000000000 $d.5
0000000000000000 l    df *ABS*	0000000000000000 comparesf2.c
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l       *ABS*	0000000000000000 $d.5
0000000000000000 l    df *ABS*	0000000000000000 ctzdi2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 ctzsi2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 ctzti2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 divdc3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 divdf3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 divdi3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 divmoddi4.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 divmodsi4.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 divmodti4.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 divsc3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 divsf3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 divsi3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 divti3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 extendsfdf2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 extendhfsf2.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 ffsdi2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 ffssi2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 ffsti2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 fixdfdi.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 fixdfsi.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 fixdfti.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 fixsfdi.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 fixsfsi.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 fixsfti.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 fixunsdfdi.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 fixunsdfsi.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 fixunsdfti.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 fixunssfdi.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 fixunssfsi.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 fixunssfti.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 floatdidf.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 floatdisf.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 floatsidf.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 floatsisf.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 floattidf.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 floattisf.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 floatundidf.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 floatundisf.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 floatunsidf.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 floatunsisf.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 floatuntidf.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 floatuntisf.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 int_util.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 lshrdi3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 lshrti3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 moddi3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 modsi3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 modti3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 muldc3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 muldf3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 muldi3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 mulodi4.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 mulosi4.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 muloti4.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 mulsc3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 mulsf3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 multi3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 mulvdi3.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 mulvsi3.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 mulvti3.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 negdf2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 negdi2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 negsf2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 negti2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 negvdi2.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 negvsi2.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 negvti2.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 os_version_check.c
0000000000000000 l       *ABS*	0000000000000000 $d.5
0000000000000000 l       *ABS*	0000000000000000 $d.6
0000000000000000 l    df *ABS*	0000000000000000 paritydi2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 paritysi2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 parityti2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 popcountdi2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 popcountsi2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 popcountti2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 powidf2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 powisf2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 subdf3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 subsf3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 subvdi3.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 subvsi3.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 subvti3.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 trampoline_setup.c
0000000000000000 l       *ABS*	0000000000000000 $d.1
0000000000000000 l    df *ABS*	0000000000000000 truncdfhf2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 truncdfsf2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 truncsfhf2.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 ucmpdi2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 ucmpti2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 udivdi3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 udivmoddi4.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 udivmodsi4.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 udivmodti4.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 udivsi3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 udivti3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 umoddi3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 umodsi3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 umodti3.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 truncdfbf2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 truncsfbf2.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 emutls.c
0000000000000000 l       *ABS*	0000000000000000 $d.6
0000000000000000 l       *ABS*	0000000000000000 $d.7
0000000000000000 l    df *ABS*	0000000000000000 enable_execute_stack.c
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l    df *ABS*	0000000000000000 eprintf.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 atomic.c
0000000000000000 l       *ABS*	0000000000000000 $d.102
0000000000000000 l       *ABS*	0000000000000000 $d.103
0000000000000000 l    df *ABS*	0000000000000000 gcc_personality_v0.c
0000000000000000 l       *ABS*	0000000000000000 $d.5
0000000000000000 l       *ABS*	0000000000000000 $d.6
0000000000000000 l    df *ABS*	0000000000000000 clear_cache.c
0000000000000000 l       *ABS*	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l    df *ABS*	0000000000000000 cpu_model.c
ffffffff0003a5a5 l       .rodata	0000000000000000 $d.2
ffffffff00168108 l       .bss	0000000000000000 $d.3
0000000000000000 l       *ABS*	0000000000000000 $d.5
0000000000000000 l       *ABS*	0000000000000000 $d.6
ffffffff00168108 l     O .bss	0000000000000001 .hidden __aarch64_have_lse_atomics
0000000000000000 l    df *ABS*	0000000000000000 fp_mode.c
0000000000000000 l       *ABS*	0000000000000000 $d.4
0000000000000000 l       *ABS*	0000000000000000 $d.5
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l       *ABS*	0000000000000000 $d.2
0000000000000000 l    df *ABS*	0000000000000000 ld-temp.o
ffffffff000032b4 l     F .text	0000000000000018 console_smcall_init.cfi
ffffffff000032b4 l       .text	0000000000000000 $x.0
ffffffff0016810c l     O .bss	0000000000000001 no_console
ffffffff0015d2d0 l     O .data	0000000000000018 console_entity
ffffffff0003312c l     F .text	00000000000000d8 sm_register_entity
ffffffff000032cc l     F .text	000000000000002c console_stdcall.cfi
ffffffff000032cc l       .text	0000000000000000 $x.1
ffffffff000032f8 l     F .text	0000000000000040 platform_dputc
ffffffff000032f8 l       .text	0000000000000000 $x.2
ffffffff00003338 l     F .text	000000000000000c platform_dgetc
ffffffff00003338 l       .text	0000000000000000 $x.3
ffffffff0000ff18 l     F .text	000000000000026c thread_sleep_ns
ffffffff00003344 l     F .text	0000000000000014 platform_after_vm_init.cfi
ffffffff00003344 l       .text	0000000000000000 $x.4
ffffffff0000a0d8 l     F .text	00000000000000dc arm_gic_init
ffffffff0000a8b4 l     F .text	0000000000000c30 arm_generic_timer_init
ffffffff00003358 l     F .text	00000000000000d0 platform_init_mmu_mappings
ffffffff00003358 l       .text	0000000000000000 $x.5
ffffffff0015d5c8 l     O .data	00000000000000a0 mmu_initial_mappings
ffffffff0015d2e8 l     O .data	0000000000000060 ram_arena
ffffffff00011a0c l     F .text	00000000000002b8 pmm_add_arena
ffffffff00003428 l     F .text	0000000000000010 smc_load_access_policy
ffffffff00003428 l       .text	0000000000000000 $x.6
ffffffff00036e04 l     F .text	0000000000000004 default_access_policy
ffffffff00003438 l     F .text	0000000000000008 default_access_policy.cfi
ffffffff00003438 l       .text	0000000000000000 $x.7
ffffffff00003440 l     F .text	0000000000000024 add_app_ranges.cfi
ffffffff00003440 l       .text	0000000000000000 $x.8
ffffffff0015d348 l     O .data	0000000000000030 oemcrypto_range
ffffffff0002c0e8 l     F .text	00000000000000b4 rctee_app_allow_mmio_range
ffffffff0015d378 l     O .data	0000000000000030 confirmation_ui_range
ffffffff00003464 l     F .text	00000000000000d8 init_caam_env.cfi
ffffffff00003464 l       .text	0000000000000000 $x.9
ffffffff0001790c l     F .text	0000000000000004 memalign
ffffffff00168110 l     O .bss	0000000000000008 g_rings
ffffffff00168118 l     O .bss	0000000000000008 g_job
ffffffff00168120 l     O .bss	0000000000000008 sram_base
ffffffff0001369c l     F .text	00000000000000ac vaddr_to_paddr
ffffffff00168128 l     O .bss	0000000000000001 caam_ready
ffffffff0000353c l     F .text	0000000000000010 platform_init_caam.cfi
ffffffff0000353c l       .text	0000000000000000 $x.10
ffffffff0015d418 l     O .data	0000000000000018 caam_ops
ffffffff00025880 l     F .text	0000000000000058 install_sys_fd_handler
ffffffff0000354c l     F .text	0000000000001210 sys_caam_ioctl.cfi
ffffffff0000354c l       .text	0000000000000000 $x.11
ffffffff0000475c l     F .text	000000000000019c imx_rand
ffffffff0015d430 l     O .data	0000000000000010 hwcrypto_ta_uuid
ffffffff0015d3e0 l     O .data	0000000000000038 lock
ffffffff0000bb64 l     F .text	0000000000000230 mutex_acquire_timeout
ffffffff000056f4 l     F .text	0000000000000100 run_job
ffffffff0000bd94 l     F .text	0000000000000218 mutex_release
ffffffff00042b4c l     O .rodata	0000000000000030 aes_decriptor_template_ecb_cbc
ffffffff00042b7c l     O .rodata	000000000000005c aes_decriptor_template_ctr
ffffffff00042bd8 l     O .rodata	0000000000000040 aes_decriptor_template_gcm
ffffffff00042c18 l     O .rodata	0000000000000030 des_decriptor_template_ede_cbc
ffffffff000048f8 l     F .text	0000000000000160 caam_gen_blob
ffffffff00004a58 l     F .text	0000000000000160 caam_decap_blob
ffffffff00004bb8 l     F .text	00000000000004b4 caam_gen_dek_blob
ffffffff0000506c l     F .text	0000000000000498 caam_decap_dek_blob
ffffffff00005504 l     F .text	00000000000001f0 caam_gen_kdfv1_root_key
ffffffff000057f4 l     F .text	0000000000000288 caam_gen_bkek_key
ffffffff00005a7c l     F .text	00000000000001ec caam_gen_mppubk
ffffffff00005c68 l     F .text	00000000000000a0 caam_get_keybox
ffffffff000427e8 l       .rodata	0000000000000000 $d.12
ffffffff0000475c l       .text	0000000000000000 $x.13
ffffffff0015d3c0 l     O .data	0000000000000020 entropy
ffffffff0000a858 l     F .text	000000000000005c current_time_ns
ffffffff000048f8 l       .text	0000000000000000 $x.14
ffffffff00004a58 l       .text	0000000000000000 $x.15
ffffffff00004bb8 l       .text	0000000000000000 $x.16
ffffffff00005d08 l     F .text	0000000000000200 sm_alloc_pages
ffffffff00042b04 l     O .rodata	0000000000000024 decriptor_template_gen_dek_blob
ffffffff00005f08 l     F .text	00000000000000d8 sm_deallocate_partition
ffffffff0000506c l       .text	0000000000000000 $x.17
ffffffff00042b28 l     O .rodata	0000000000000024 decriptor_template_decap_dek_blob
ffffffff00005504 l       .text	0000000000000000 $x.18
ffffffff000056f4 l       .text	0000000000000000 $x.19
ffffffff000057f4 l       .text	0000000000000000 $x.20
ffffffff00005a7c l       .text	0000000000000000 $x.21
ffffffff00005c68 l       .text	0000000000000000 $x.22
ffffffff00005d08 l       .text	0000000000000000 $x.23
ffffffff00005f08 l       .text	0000000000000000 $x.24
ffffffff00005fe0 l     F .text	0000000000000084 platform_random_get_bytes
ffffffff00005fe0 l       .text	0000000000000000 $x.25
ffffffff00006064 l     F .text	0000000000000010 platform_init_csu.cfi
ffffffff00006064 l       .text	0000000000000000 $x.26
ffffffff0015d440 l     O .data	0000000000000018 csu_ops
ffffffff00006074 l     F .text	00000000000001bc sys_csu_ioctl.cfi
ffffffff00006074 l       .text	0000000000000000 $x.27
ffffffff0015d458 l     O .data	0000000000000010 hwsecure_ta_uuid
ffffffff0015d468 l     O .data	0000000000000010 secure_fb_impl_ta_uuid
ffffffff00168129 l     O .bss	0000000000000001 tee_ctrl_lcdif
ffffffff0016812c l     O .bss	0000000000000004 last_tee_fb_addr
ffffffff00168130 l     O .bss	0000000000000004 last_linux_fb_addr
ffffffff00006230 l     F .text	0000000000000010 imx_linux_smcall_init.cfi
ffffffff00006230 l       .text	0000000000000000 $x.28
ffffffff0015d478 l     O .data	0000000000000018 imx_linux_entity
ffffffff00006240 l     F .text	0000000000000100 imx_linux_fastcall.cfi
ffffffff00006240 l       .text	0000000000000000 $x.29
ffffffff0004282c l       .rodata	0000000000000000 $d.30
ffffffff00006340 l     F .text	0000000000000030 snvs_smcall_init.cfi
ffffffff00006340 l       .text	0000000000000000 $x.31
ffffffff0015d490 l     O .data	0000000000000018 snvs_entity
ffffffff00006370 l     F .text	0000000000000254 snvs_fastcall.cfi
ffffffff00006370 l       .text	0000000000000000 $x.32
ffffffff00042841 l       .rodata	0000000000000000 $d.33
ffffffff000065c4 l     F .text	0000000000000020 monotonic_time_s
ffffffff000065c4 l       .text	0000000000000000 $x.34
ffffffff000065e4 l     F .text	0000000000000010 vpu_smcall_init.cfi
ffffffff000065e4 l       .text	0000000000000000 $x.35
ffffffff0015d4a8 l     O .data	0000000000000018 vpu_entity
ffffffff000065f4 l     F .text	0000000000000544 vpu_fastcall.cfi
ffffffff000065f4 l       .text	0000000000000000 $x.36
ffffffff0015d4d0 l     O .data	0000000000000028 inout_buffer_g2_paddr
ffffffff0015d4c0 l     O .data	0000000000000010 inout_buffer_paddr
ffffffff00006b38 l     F .text	0000000000000144 vpu_write_regs
ffffffff0004287c l       .rodata	0000000000000000 $d.37
ffffffff00006b38 l       .text	0000000000000000 $x.38
ffffffff00006c7c l     F .text	0000000000000010 vpu_enc_smcall_init.cfi
ffffffff00006c7c l       .text	0000000000000000 $x.39
ffffffff0015d4f8 l     O .data	0000000000000018 vpu_enc_entity
ffffffff00006c8c l     F .text	00000000000000bc vpu_enc_fastcall.cfi
ffffffff00006c8c l       .text	0000000000000000 $x.40
ffffffff00006d48 l     F .text	0000000000000004 app_thread_entry.cfi
ffffffff00006d48 l       .text	0000000000000000 $x.41
ffffffff00006d4c l     F .text	0000000000000034 arch_enter_uspace
ffffffff00006d4c l       .text	0000000000000000 $x.42
ffffffff00006d80 l       .text	0000000000000000 $x.43
ffffffff0015d510 l     O .data	0000000000000008 arm_boot_cpu_lock
ffffffff00036e10 l     F .text	000000000000000c arm_ipi_generic_handler
ffffffff00009ef0 l     F .text	00000000000001e8 register_int_handler
ffffffff00036e14 l     F .text	000000000000000c arm_ipi_reschedule_handler
ffffffff00168134 l     O .bss	0000000000000004 secondaries_to_init
ffffffff0016ac9c l     O .bss	0000000000000004 secondary_bootstrap_thread_count
ffffffff00168d80 l     O .bss	0000000000000c40 _idle_threads
ffffffff0000c384 l     F .text	00000000000006c0 thread_set_pinned_cpu
ffffffff00169c20 l     O .bss	0000000000000008 thread_lock
ffffffff0015d6d0 l     O .data	0000000000000004 thread_lock_owner
ffffffff00168a40 l     O .bss	0000000000000010 thread_list
ffffffff001699c0 l     O .bss	0000000000000010 cpu_priority
ffffffff000347a8 l     F .text	00000000000000e8 platform_cpu_priority_set
ffffffff0016aca0 l     O .bss	0000000000000018 secondary_bootstrap_threads
ffffffff0000e008 l     F .text	0000000000000488 thread_resume
ffffffff0001076c l     F .text	000000000000004c thread_secondary_cpu_entry
ffffffff000072b8 l       .text	0000000000000000 $x.44
ffffffff00168138 l     O .bss	0000000000000020 current_fpstate
ffffffff00013748 l     F .text	0000000000000068 vaddr_to_aspace
ffffffff0001599c l     F .text	0000000000000240 vmm_get_address_description
ffffffff00007820 l     F .text	0000000000000140 print_fault_code
ffffffff00007960 l     F .text	0000000000000060 dump_iframe
ffffffff0002c8ec l     F .text	000000000000001c rctee_app_crash
ffffffff00042888 l       .rodata	0000000000000000 $d.45
ffffffff00007820 l       .text	0000000000000000 $x.46
ffffffff00042960 l       .rodata	0000000000000000 $d.47
ffffffff00007960 l       .text	0000000000000000 $x.48
ffffffff000079c0 l       .text	0000000000000000 $x.49
ffffffff00007a04 l     F .text	00000000000000d8 arch_clear_pages_and_tags
ffffffff00007a04 l       .text	0000000000000000 $x.50
ffffffff00007adc l     F .text	000000000000010c initial_thread_func.cfi
ffffffff00007adc l       .text	0000000000000000 $x.51
ffffffff0000fa70 l     F .text	0000000000000210 thread_exit
ffffffff00007be8 l     F .text	0000000000000080 arch_context_switch
ffffffff00007be8 l       .text	0000000000000000 $x.52
ffffffff00007c68 l     F .text	0000000000000048 arm64_pan_init.cfi
ffffffff00007c68 l       .text	0000000000000000 $x.53
ffffffff00007cb0 l     F .text	0000000000000008 arm_ipi_generic_handler.cfi
ffffffff00007cb0 l       .text	0000000000000000 $x.54
ffffffff00007cb8 l     F .text	0000000000000058 arm_ipi_reschedule_handler.cfi
ffffffff00007cb8 l       .text	0000000000000000 $x.55
ffffffff00169a70 l     O .bss	0000000000000140 thread_stats
ffffffff00169a60 l     O .bss	000000000000000c mp
ffffffff00007d10 l     F .text	0000000000000270 arm64_mmu_map_pt
ffffffff00007d10 l       .text	0000000000000000 $x.56
ffffffff0015d668 l     O .data	0000000000000008 boot_alloc_end
ffffffff00007f80 l     F .text	0000000000000144 arm64_mmu_unmap_pt
ffffffff00007f80 l       .text	0000000000000000 $x.57
ffffffff000080c4 l       .text	0000000000000000 $x.58
ffffffff00008524 l     F .text	0000000000000430 arch_mmu_query
ffffffff00008524 l       .text	0000000000000000 $x.59
ffffffff0001354c l     F .text	0000000000000150 paddr_to_kvaddr
ffffffff00042998 l       .rodata	0000000000000000 $d.60
ffffffff00008954 l     F .text	000000000000048c arm64_mmu_map_aspace
ffffffff00008954 l       .text	0000000000000000 $x.61
ffffffff00008de0 l     F .text	00000000000002f8 arm64_mmu_map_pt.150
ffffffff000090d8 l     F .text	00000000000001a4 arm64_tlbflush_if_asid_changed
ffffffff000429a8 l       .rodata	0000000000000000 $d.62
ffffffff00008de0 l       .text	0000000000000000 $x.63
ffffffff00012c58 l     F .text	0000000000000158 pmm_alloc_contiguous
ffffffff0000927c l     F .text	0000000000000240 arm64_mmu_unmap_pt.164
ffffffff000090d8 l       .text	0000000000000000 $x.64
ffffffff0000927c l       .text	0000000000000000 $x.65
ffffffff0015d530 l     O .data	0000000000000010 arena_list
ffffffff0015d540 l     O .data	0000000000000038 lock.406
ffffffff00012974 l     F .text	0000000000000148 pmm_free_locked
ffffffff000094bc l     F .text	00000000000002d8 arch_mmu_unmap
ffffffff000094bc l       .text	0000000000000000 $x.66
ffffffff00009794 l     F .text	00000000000002a0 arch_mmu_init_aspace
ffffffff00009794 l       .text	0000000000000000 $x.67
ffffffff00017960 l     F .text	000000000000027c miniheap_memalign
ffffffff0016afd8 l     O .bss	0000000000000098 _kernel_aspace
ffffffff00009a34 l     F .text	00000000000000fc arm_gic_suspend_cpu.cfi
ffffffff00009a34 l       .text	0000000000000000 $x.68
ffffffff00168990 l     O .bss	0000000000000001 arm_gics.0
ffffffff0016899c l     O .bss	0000000000000004 enabled_spi_mask.0
ffffffff001689a0 l     O .bss	0000000000000004 enabled_spi_mask.1
ffffffff001689a4 l     O .bss	0000000000000004 enabled_spi_mask.2
ffffffff001689a8 l     O .bss	0000000000000004 enabled_spi_mask.3
ffffffff00168998 l     O .bss	0000000000000001 arm_gics.2
ffffffff001689ac l     O .bss	0000000000000010 enabled_ppi_mask
ffffffff00009b30 l     F .text	0000000000000308 arm_gic_resume_cpu.cfi
ffffffff00009b30 l       .text	0000000000000000 $x.69
ffffffff00168180 l     O .bss	0000000000000008 gicd_lock
ffffffff00168190 l     O .bss	0000000000000800 int_handler_table_per_cpu
ffffffff00042c48 l     O .rodata	0000000000000800 int_handler_table_shared
ffffffff0000a4b8 l     F .text	0000000000000134 arm_gicv3_configure_irq_locked
ffffffff00009e38 l     F .text	00000000000000b8 arm_gic_init_percpu.cfi
ffffffff00009e38 l       .text	0000000000000000 $x.70
ffffffff00009ef0 l       .text	0000000000000000 $x.71
ffffffff0000a0d8 l       .text	0000000000000000 $x.72
ffffffff0000a1b4 l       .text	0000000000000000 $x.73
ffffffff0016ae48 l     O .bss	0000000000000004 irq_thread_ready
ffffffff0016ae50 l     O .bss	00000000000000c0 nsirqevent
ffffffff0000b970 l     F .text	00000000000001f4 event_signal
ffffffff0015df90 l     O .data	0000000000000070 stdcallstate
ffffffff00032bc0 l     F .text	000000000000025c sm_return_and_wait_for_next_stdcall
ffffffff0000a314 l     F .text	00000000000000ec smc_intc_get_next_irq.cfi
ffffffff0000a314 l       .text	0000000000000000 $x.74
ffffffff00168188 l     O .bss	0000000000000001 doorbell_enabled
ffffffff0000a400 l     F .text	00000000000000b8 sm_intc_fiq_enter
ffffffff0000a400 l       .text	0000000000000000 $x.75
ffffffff0000a4b8 l       .text	0000000000000000 $x.76
ffffffff0000a5ec l     F .text	0000000000000050 arm_generic_timer_suspend_cpu.cfi
ffffffff0000a5ec l       .text	0000000000000000 $x.77
ffffffff00168a00 l     O .bss	0000000000000040 saved_state
ffffffff0000a63c l     F .text	0000000000000054 arm_generic_timer_resume_cpu.cfi
ffffffff0000a63c l       .text	0000000000000000 $x.78
ffffffff0000a690 l     F .text	00000000000000bc arm_generic_timer_init_secondary_cpu.cfi
ffffffff0000a690 l       .text	0000000000000000 $x.79
ffffffff001689c8 l     O .bss	0000000000000001 timer_irq
ffffffff00036e18 l     F .text	000000000000000c platform_tick
ffffffff0000a74c l     F .text	0000000000000090 platform_tick.cfi
ffffffff0000a74c l       .text	0000000000000000 $x.80
ffffffff001689c0 l     O .bss	0000000000000008 t_callback
ffffffff001689d0 l     O .bss	000000000000000c ns_per_cntpct
ffffffff00010fc8 l     F .text	0000000000000334 timer_tick.cfi
ffffffff0000a7dc l     F .text	000000000000007c platform_set_oneshot_timer
ffffffff0000a7dc l       .text	0000000000000000 $x.81
ffffffff001689e0 l     O .bss	000000000000000c cntpct_per_ns
ffffffff00036e28 l     F .text	0000000000000004 timer_tick
ffffffff0000a858 l       .text	0000000000000000 $x.82
ffffffff0000a8b4 l       .text	0000000000000000 $x.83
ffffffff001689f0 l     O .bss	000000000000000c ms_per_cntpct
ffffffff0000b4e4 l     F .text	00000000000002ac event_destroy
ffffffff0000b4e4 l       .text	0000000000000000 $x.84
ffffffff0000e8e4 l     F .text	00000000000004c4 wait_queue_wake_all
ffffffff0000b790 l     F .text	00000000000001e0 event_wait_timeout
ffffffff0000b790 l       .text	0000000000000000 $x.85
ffffffff0000f12c l     F .text	0000000000000364 wait_queue_block
ffffffff0000b970 l       .text	0000000000000000 $x.86
ffffffff000107b8 l     F .text	0000000000000464 wait_queue_wake_one
ffffffff0000bb64 l       .text	0000000000000000 $x.87
ffffffff0000bd94 l       .text	0000000000000000 $x.88
ffffffff0000bfac l     F .text	00000000000003d8 thread_create_etc
ffffffff0000bfac l       .text	0000000000000000 $x.89
ffffffff00015300 l     F .text	00000000000001c0 vmm_alloc
ffffffff00017d54 l     F .text	00000000000000d0 miniheap_free
ffffffff00036e4c l     F .text	000000000000001c initial_thread_func
ffffffff0000c384 l       .text	0000000000000000 $x.90
ffffffff0000cb04 l     F .text	00000000000002c8 thread_cond_mp_reschedule
ffffffff0000ca44 l     F .text	00000000000000c0 thread_preempt_lock_held
ffffffff000429b8 l       .rodata	0000000000000000 $d.91
ffffffff0000ca44 l       .text	0000000000000000 $x.92
ffffffff0000cdcc l     F .text	000000000000025c thread_preempt_inner
ffffffff0000cb04 l       .text	0000000000000000 $x.93
ffffffff001699d0 l     O .bss	0000000000000004 run_queue_bitmap
ffffffff00168b80 l     O .bss	0000000000000200 run_queue
ffffffff0000cdcc l       .text	0000000000000000 $x.94
ffffffff0000d028 l     F .text	0000000000000200 insert_in_run_queue_head
ffffffff0000d430 l     F .text	000000000000074c thread_resched
ffffffff0000d228 l     F .text	0000000000000208 insert_in_run_queue_tail
ffffffff0000d028 l       .text	0000000000000000 $x.95
ffffffff0000d228 l       .text	0000000000000000 $x.96
ffffffff0000d430 l       .text	0000000000000000 $x.97
ffffffff00168a50 l     O .bss	0000000000000100 preempt_timer
ffffffff000112fc l     F .text	00000000000003a0 timer_cancel_etc
ffffffff00036e1c l     F .text	000000000000000c thread_timer_callback
ffffffff00010c1c l     F .text	00000000000002c0 timer_set
ffffffff00016574 l     F .text	0000000000000358 vmm_context_switch
ffffffff0000db7c l     F .text	000000000000011c thread_mp_reschedule
ffffffff0000db7c l       .text	0000000000000000 $x.98
ffffffff0000dc98 l     F .text	0000000000000198 thread_timer_callback.cfi
ffffffff0000dc98 l       .text	0000000000000000 $x.99
ffffffff0000de30 l     F .text	00000000000001d8 thread_set_real_time
ffffffff0000de30 l       .text	0000000000000000 $x.100
ffffffff0000e008 l       .text	0000000000000000 $x.101
ffffffff0000e490 l     F .text	0000000000000214 thread_yield
ffffffff0000e490 l       .text	0000000000000000 $x.102
ffffffff0000e6a4 l     F .text	0000000000000240 thread_detach
ffffffff0000e6a4 l       .text	0000000000000000 $x.103
ffffffff0000eda8 l     F .text	0000000000000384 thread_join
ffffffff0000e8e4 l       .text	0000000000000000 $x.104
ffffffff0000eda8 l       .text	0000000000000000 $x.105
ffffffff0000f490 l     F .text	0000000000000110 thread_free
ffffffff0000f12c l       .text	0000000000000000 $x.106
ffffffff00036e20 l     F .text	000000000000000c wait_queue_timeout_handler
ffffffff0000f490 l       .text	0000000000000000 $x.107
ffffffff00015e10 l     F .text	0000000000000230 vmm_free_region_etc
ffffffff0000f5a0 l     F .text	00000000000004d0 wait_queue_timeout_handler.cfi
ffffffff0000f5a0 l       .text	0000000000000000 $x.108
ffffffff0000fa70 l       .text	0000000000000000 $x.109
ffffffff00168b50 l     O .bss	0000000000000010 dead_threads
ffffffff00168b60 l     O .bss	0000000000000020 reaper_wait_queue
ffffffff0000fc80 l     F .text	0000000000000174 thread_exit_from_panic
ffffffff0000fc80 l       .text	0000000000000000 $x.110
ffffffff0000fdf4 l     F .text	00000000000000e0 thread_unlock_prepare
ffffffff0000fed4 l     F .text	000000000000003c spin_unlock_restore
ffffffff0000fdf4 l       .text	0000000000000000 $x.111
ffffffff0000fed4 l       .text	0000000000000000 $x.112
ffffffff0000ff10 l       .text	0000000000000000 $x.113
ffffffff0000ff18 l       .text	0000000000000000 $x.114
ffffffff00036e24 l     F .text	000000000000000c thread_sleep_handler
ffffffff00010184 l     F .text	0000000000000294 thread_sleep_handler.cfi
ffffffff00010184 l       .text	0000000000000000 $x.115
ffffffff00010418 l     F .text	000000000000006c thread_sleep_until_ns
ffffffff00010418 l       .text	0000000000000000 $x.116
ffffffff00010484 l     F .text	00000000000001dc reaper_thread_routine.cfi
ffffffff00010484 l       .text	0000000000000000 $x.117
ffffffff00010660 l     F .text	0000000000000018 thread_set_name
ffffffff00010660 l       .text	0000000000000000 $x.118
ffffffff00010678 l     F .text	00000000000000f0 thread_become_idle
ffffffff00010678 l       .text	0000000000000000 $x.119
ffffffff00010768 l     F .text	0000000000000004 arch_curr_cpu_num
ffffffff0001184c l     F .text	000000000000003c mp_set_curr_cpu_active
ffffffff00010768 l       .text	0000000000000000 $x.120
ffffffff0001076c l       .text	0000000000000000 $x.121
ffffffff000107b8 l       .text	0000000000000000 $x.122
ffffffff00010c1c l       .text	0000000000000000 $x.123
ffffffff001699d8 l     O .bss	0000000000000008 timer_lock
ffffffff00010edc l     F .text	00000000000000ec insert_timer_in_queue
ffffffff001699e0 l     O .bss	0000000000000080 timers
ffffffff00010edc l       .text	0000000000000000 $x.124
ffffffff00010fc8 l       .text	0000000000000000 $x.125
ffffffff000112fc l       .text	0000000000000000 $x.126
ffffffff0001169c l     F .text	00000000000001b0 sem_post
ffffffff0001169c l       .text	0000000000000000 $x.127
ffffffff0016ada8 l     O .bss	0000000000000078 g_conn_req_queue
ffffffff0001184c l       .text	0000000000000000 $x.128
ffffffff00011888 l     F .text	00000000000000c0 phys_mem_obj_check_flags.cfi
ffffffff00011888 l       .text	0000000000000000 $x.129
ffffffff00011948 l     F .text	0000000000000068 phys_mem_obj_get_page.cfi
ffffffff00011948 l       .text	0000000000000000 $x.130
ffffffff000119b0 l     F .text	000000000000005c phys_mem_obj_destroy.cfi
ffffffff000119b0 l       .text	0000000000000000 $x.131
ffffffff00011a0c l       .text	0000000000000000 $x.132
ffffffff00011cc4 l     F .text	00000000000000e4 pmm_unreserve_pages
ffffffff00011cc4 l       .text	0000000000000000 $x.133
ffffffff00011da8 l     F .text	00000000000004ec pmm_alloc_from_res_group
ffffffff00011da8 l       .text	0000000000000000 $x.134
ffffffff0015d590 l     O .data	0000000000000038 res_group_lock
ffffffff00012294 l     F .text	0000000000000514 pmm_alloc_pages_locked
ffffffff0015d578 l     O .data	0000000000000018 pmm_vmm_obj_ops
ffffffff00012294 l       .text	0000000000000000 $x.135
ffffffff000127a8 l     F .text	0000000000000008 pmm_vmm_obj_check_flags.cfi
ffffffff000127a8 l       .text	0000000000000000 $x.136
ffffffff000127b0 l     F .text	00000000000000f8 pmm_vmm_obj_get_page.cfi
ffffffff000127b0 l       .text	0000000000000000 $x.137
ffffffff000128a8 l     F .text	00000000000000cc pmm_vmm_obj_destroy.cfi
ffffffff000128a8 l       .text	0000000000000000 $x.138
ffffffff00012db0 l     F .text	0000000000000198 res_group_del_ref
ffffffff00012974 l       .text	0000000000000000 $x.139
ffffffff00012abc l     F .text	00000000000000c8 pmm_set_cleared
ffffffff00012abc l       .text	0000000000000000 $x.140
ffffffff00012b84 l     F .text	0000000000000064 pmm_set_tagged
ffffffff00012b84 l       .text	0000000000000000 $x.141
ffffffff00012be8 l     F .text	0000000000000070 pmm_free
ffffffff00012be8 l       .text	0000000000000000 $x.142
ffffffff00012c58 l       .text	0000000000000000 $x.143
ffffffff00012db0 l       .text	0000000000000000 $x.144
ffffffff00012f48 l     F .text	00000000000001dc vm_init_preheap.cfi
ffffffff00012f48 l       .text	0000000000000000 $x.145
ffffffff0015d670 l     O .data	0000000000000010 aspace_list
ffffffff00013124 l     F .text	0000000000000220 mark_pages_in_use
ffffffff00169c18 l     O .bss	0000000000000001 boot_alloc_start
ffffffff00013124 l       .text	0000000000000000 $x.146
ffffffff00013344 l     F .text	0000000000000208 vm_init_postheap.cfi
ffffffff00013344 l       .text	0000000000000000 $x.147
ffffffff0015d680 l     O .data	0000000000000038 vmm_lock
ffffffff00014150 l     F .text	0000000000000618 alloc_region
ffffffff0001354c l       .text	0000000000000000 $x.148
ffffffff00169c10 l     O .bss	0000000000000008 aux_slock
ffffffff0001369c l       .text	0000000000000000 $x.149
ffffffff00013748 l       .text	0000000000000000 $x.150
ffffffff000137b0 l     F .text	00000000000000dc vmm_obj_slice_release
ffffffff000137b0 l       .text	0000000000000000 $x.151
ffffffff0001388c l     F .text	00000000000000e0 vmm_obj_del_ref
ffffffff0001388c l       .text	0000000000000000 $x.152
ffffffff0001396c l     F .text	000000000000006c vmm_obj_slice_bind
ffffffff0001396c l       .text	0000000000000000 $x.153
ffffffff000139d8 l     F .text	00000000000000d8 vmm_obj_slice_bind_locked
ffffffff000139d8 l       .text	0000000000000000 $x.154
ffffffff00013ab0 l     F .text	000000000000040c alloc_spot
ffffffff00013ab0 l       .text	0000000000000000 $x.155
ffffffff00013ebc l     F .text	0000000000000148 scan_gap
ffffffff00014004 l     F .text	000000000000014c spot_in_gap
ffffffff00013ebc l       .text	0000000000000000 $x.156
ffffffff00014004 l       .text	0000000000000000 $x.157
ffffffff00014150 l       .text	0000000000000000 $x.158
ffffffff00014768 l     F .text	0000000000000194 vmm_find_region_in_bst
ffffffff000148fc l     F .text	00000000000000e4 is_range_inside_region
ffffffff00036ee4 l     F .text	0000000000000014 vmm_res_obj_check_flags
ffffffff00016a80 l     F .text	0000000000000488 bst_update_rank_insert
ffffffff00014768 l       .text	0000000000000000 $x.159
ffffffff000148fc l       .text	0000000000000000 $x.160
ffffffff000149e0 l     F .text	0000000000000008 vmm_res_obj_check_flags.cfi
ffffffff000149e0 l       .text	0000000000000000 $x.161
ffffffff000149e8 l     F .text	0000000000000468 vmm_alloc_obj
ffffffff000149e8 l       .text	0000000000000000 $x.163
ffffffff00014e50 l     F .text	000000000000028c vmm_map_obj_locked
ffffffff00016f08 l     F .text	00000000000007b8 bst_delete
ffffffff00014e50 l       .text	0000000000000000 $x.164
ffffffff000150dc l     F .text	0000000000000224 vmm_alloc_physical_etc
ffffffff000150dc l       .text	0000000000000000 $x.165
ffffffff00015300 l       .text	0000000000000000 $x.166
ffffffff000154c0 l     F .text	000000000000021c vmm_alloc_no_physical
ffffffff000154c0 l       .text	0000000000000000 $x.167
ffffffff0015d6b8 l     O .data	0000000000000018 vmm_res_obj_ops
ffffffff000156dc l     F .text	0000000000000008 vmm_res_obj_get_page.cfi
ffffffff000156dc l       .text	0000000000000000 $x.168
ffffffff000156e4 l     F .text	00000000000002b8 vmm_res_obj_destroy.cfi
ffffffff000156e4 l       .text	0000000000000000 $x.169
ffffffff0001599c l       .text	0000000000000000 $x.170
ffffffff00015bdc l     F .text	0000000000000234 vmm_get_obj
ffffffff00015bdc l       .text	0000000000000000 $x.171
ffffffff00015e10 l       .text	0000000000000000 $x.172
ffffffff00016040 l     F .text	0000000000000534 vmm_free_aspace
ffffffff00016040 l       .text	0000000000000000 $x.173
ffffffff00016574 l       .text	0000000000000000 $x.174
ffffffff00169be0 l     O .bss	0000000000000020 active_aspace
ffffffff00169c00 l     O .bss	0000000000000008 last_asid
ffffffff00169c08 l     O .bss	0000000000000001 old_asid_active
ffffffff00169bc0 l     O .bss	0000000000000020 active_asid_version
ffffffff000168cc l     F .text	00000000000001b4 vmm_set_active_aspace
ffffffff000168cc l       .text	0000000000000000 $x.175
ffffffff00016a80 l       .text	0000000000000000 $x.176
ffffffff00016f08 l       .text	0000000000000000 $x.177
ffffffff000176c0 l       .text	0000000000000000 $x.178
ffffffff0001acf4 l     F .text	0000000000000324 dump_thread_backtrace
ffffffff00032ed0 l     F .text	0000000000000180 platform_halt
ffffffff00017900 l       .text	0000000000000000 $x.179
ffffffff0001790c l       .text	0000000000000000 $x.180
ffffffff00017910 l       .text	0000000000000000 $x.181
ffffffff0001795c l       .text	0000000000000000 $x.183
ffffffff00017960 l       .text	0000000000000000 $x.184
ffffffff00169c28 l     O .bss	0000000000000068 theheap
ffffffff00017bdc l     F .text	0000000000000178 heap_insert_free_chunk
ffffffff00017bdc l       .text	0000000000000000 $x.185
ffffffff00017d54 l       .text	0000000000000000 $x.186
ffffffff00017e24 l     F .text	0000000000000330 __debug_stdio_write.cfi
ffffffff00017e24 l       .text	0000000000000000 $x.187
ffffffff0015d728 l     O .data	0000000000000004 lock_held_by
ffffffff0015d730 l     O .data	0000000000000008 early_log_end
ffffffff0015d6d8 l     O .data	0000000000000008 early_log_writeptr
ffffffff00169c90 l     O .bss	0000000000000008 print_spin_lock
ffffffff0015d6e0 l     O .data	0000000000000010 print_callbacks
ffffffff00018154 l     F .text	000000000000019c __debug_stdio_write_commit.cfi
ffffffff00018154 l       .text	0000000000000000 $x.188
ffffffff000182f0 l     F .text	000000000000002c __debug_stdio_read.cfi
ffffffff000182f0 l       .text	0000000000000000 $x.189
ffffffff0001831c l     F .text	00000000000000d8 __debug_stdio_lock.cfi
ffffffff0001831c l       .text	0000000000000000 $x.190
ffffffff0015d738 l     O .data	0000000000000038 print_mutex
ffffffff0016ac98 l     O .bss	0000000000000004 print_saved_state
ffffffff000183f4 l     F .text	00000000000000f8 __debug_stdio_unlock.cfi
ffffffff000183f4 l       .text	0000000000000000 $x.191
ffffffff000184ec l       .text	0000000000000000 $x.192
ffffffff00018570 l       .text	0000000000000000 $x.193
ffffffff000185f8 l       .text	0000000000000000 $x.194
ffffffff00018680 l       .text	0000000000000000 $x.195
ffffffff00018708 l       .text	0000000000000000 $x.197
ffffffff0015e998 l     O .data	000000000000002d lk_version
ffffffff0016af48 l     O .bss	0000000000000008 lk_boot_args.0
ffffffff0016af50 l     O .bss	0000000000000008 lk_boot_args.1
ffffffff0016af58 l     O .bss	0000000000000008 lk_boot_args.2
ffffffff0016af60 l     O .bss	0000000000000008 lk_boot_args.3
ffffffff00036f8c l     F .text	0000000000000040 reaper_thread_routine
ffffffff00169bb0 l     O .bss	0000000000000010 write_port_list
ffffffff00036f90 l     F .text	0000000000000040 bootstrap2
ffffffff000191a0 l     F .text	00000000000007a4 bootstrap2.cfi
ffffffff000191a0 l       .text	0000000000000000 $x.198
ffffffff00036f94 l     F .text	0000000000000040 secondary_cpu_bootstrap2
ffffffff00036f88 l     F .text	0000000000000040 app_thread_entry
ffffffff00019944 l     F .text	0000000000000120 secondary_cpu_bootstrap2.cfi
ffffffff00019944 l       .text	0000000000000000 $x.199
ffffffff00019a64 l     F .text	00000000000001cc busy_test_init.cfi
ffffffff00019a64 l       .text	0000000000000000 $x.200
ffffffff00043570 l     O .rodata	0000000000000010 kernel_uuid
ffffffff0002209c l     F .text	00000000000001a8 ipc_port_create
ffffffff00022790 l     F .text	0000000000000298 ipc_port_publish
ffffffff00036f98 l     F .text	0000000000000040 busy_test_server
ffffffff00019c30 l     F .text	00000000000001a0 busy_test_server.cfi
ffffffff00019c30 l       .text	0000000000000000 $x.201
ffffffff0015d770 l     O .data	0000000000000030 busy_test_event
ffffffff000203b0 l     F .text	00000000000001b8 handle_wait
ffffffff000236a4 l     F .text	0000000000000250 ipc_port_accept
ffffffff00019dd0 l     F .text	000000000000006c busy_test_cpu_init.cfi
ffffffff00019dd0 l       .text	0000000000000000 $x.202
ffffffff00036f9c l     F .text	0000000000000040 busy_test_busy_func
ffffffff00019e3c l     F .text	0000000000000028 busy_test_busy_func.cfi
ffffffff00019e3c l       .text	0000000000000000 $x.203
ffffffff00019e64 l     F .text	0000000000000138 app_manifest_read_string
ffffffff00019e64 l       .text	0000000000000000 $x.204
ffffffff00019f9c l     F .text	0000000000000814 arm_ffa_init.cfi
ffffffff00019f9c l       .text	0000000000000000 $x.205
ffffffff0016acdc l     O .bss	0000000000000002 ffa_local_id
ffffffff0016acc8 l     O .bss	0000000000000001 arm_ffa_init_is_success
ffffffff0016acd9 l     O .bss	0000000000000001 supports_rx_release
ffffffff0016acd8 l     O .bss	0000000000000001 supports_ns_bit
ffffffff0016ace8 l     O .bss	0000000000000008 ffa_buf_size
ffffffff0016ace0 l     O .bss	0000000000000008 ffa_tx
ffffffff0016acd0 l     O .bss	0000000000000008 ffa_rx
ffffffff0001a7b0 l     F .text	00000000000003b4 arm_ffa_sched_nonsecure
ffffffff0001a7b0 l       .text	0000000000000000 $x.206
ffffffff0016acb8 l     O .bss	0000000000000010 src_dst_ids
ffffffff0001ab64 l     F .text	0000000000000190 arm_ffa_rx_release
ffffffff0001ab64 l       .text	0000000000000000 $x.207
ffffffff0015d7a0 l     O .data	0000000000000038 ffa_rxtx_buffer_lock
ffffffff0001acf4 l       .text	0000000000000000 $x.208
ffffffff0001b018 l     F .text	00000000000002e0 dump_function
ffffffff0001b018 l       .text	0000000000000000 $x.209
ffffffff0001b2f8 l     F .text	0000000000000128 print_function_info
ffffffff0001b2f8 l       .text	0000000000000000 $x.210
ffffffff0001b420 l     F .text	00000000000000ec ext_mem_obj_check_flags.cfi
ffffffff0001b420 l       .text	0000000000000000 $x.211
ffffffff0001b50c l     F .text	0000000000000094 ext_mem_obj_get_page.cfi
ffffffff0001b50c l       .text	0000000000000000 $x.212
ffffffff0001b5a0 l     F .text	00000000000001cc ext_mem_map_obj_id
ffffffff0001b5a0 l       .text	0000000000000000 $x.213
ffffffff000336d8 l     F .text	0000000000000adc ext_mem_get_vmm_obj
ffffffff0001b76c l     F .text	0000000000000268 ktipc_server_add_port
ffffffff0001b76c l       .text	0000000000000000 $x.214
ffffffff00036e40 l     F .text	0000000000000008 port_event_handler
ffffffff00020b94 l     F .text	0000000000000184 handle_set_attach
ffffffff0001b9d4 l     F .text	0000000000000294 port_event_handler.cfi
ffffffff0001b9d4 l       .text	0000000000000000 $x.215
ffffffff00036e44 l     F .text	0000000000000008 chan_event_handler
ffffffff00021204 l     F .text	0000000000000268 handle_set_detach_ref
ffffffff0001bc68 l     F .text	0000000000000298 chan_event_handler.cfi
ffffffff0001bc68 l       .text	0000000000000000 $x.216
ffffffff000201a8 l     F .text	0000000000000088 handle_decref
ffffffff0001bf00 l     F .text	00000000000001d8 ksrv_thread.cfi
ffffffff0001bf00 l       .text	0000000000000000 $x.217
ffffffff0015dc18 l     O .data	0000000000000030 hset_ops
ffffffff0002146c l     F .text	00000000000003e8 handle_set_wait
ffffffff0001c0d8 l     F .text	00000000000001c4 ktipc_recv_iov
ffffffff0001c0d8 l       .text	0000000000000000 $x.218
ffffffff0015dc48 l     O .data	0000000000000030 ipc_chan_handle_ops
ffffffff000251c4 l     F .text	0000000000000224 ipc_read_msg
ffffffff000248a4 l     F .text	00000000000002b4 ipc_put_msg
ffffffff0001c29c l     F .text	0000000000000384 ktipctest_init.cfi
ffffffff0001c29c l       .text	0000000000000000 $x.219
ffffffff0015e960 l     O .data	0000000000000038 unittest_lock
ffffffff0016afc8 l     O .bss	0000000000000008 unittest_handle_set
ffffffff0015d928 l     O .data	0000000000000078 ktipctest_init.test
ffffffff0016afd0 l     O .bss	0000000000000008 unittest_thread
ffffffff00036fc4 l     F .text	0000000000000040 unittest_loop
ffffffff0001c620 l     F .text	00000000000000ec run_ktipctest.cfi
ffffffff0001c620 l       .text	0000000000000000 $x.220
ffffffff0016acf0 l     O .bss	0000000000000004 _test_context.0
ffffffff0016acf8 l     O .bss	0000000000000004 _test_context.1
ffffffff0016ad00 l     O .bss	0000000000000004 _test_context.2
ffffffff0016ad28 l     O .bss	0000000000000008 _test_context.7
ffffffff0016ad18 l     O .bss	0000000000000008 _test_context.5
ffffffff0001c70c l     F .text	0000000000000294 run_test_suite
ffffffff0015d9a0 l     O .data	0000000000000010 _test_param_list
ffffffff000348d8 l     F .text	00000000000000e0 unittest_printf
ffffffff0001c70c l       .text	0000000000000000 $x.221
ffffffff0015d7d8 l     O .data	0000000000000010 _test_list
ffffffff0016ad08 l     O .bss	0000000000000008 _test_context.3
ffffffff0001c9a0 l     F .text	00000000000002f8 ktipctest_connecterr.cfi
ffffffff0001c9a0 l       .text	0000000000000000 $x.222
ffffffff0016ad10 l     O .bss	0000000000000008 _test_context.4
ffffffff0016ad20 l     O .bss	0000000000000008 _test_context.6
ffffffff0016ad30 l     O .bss	0000000000000001 _test_context.8
ffffffff0016ad38 l     O .bss	0000000000000001 _test_context.9
ffffffff00022f94 l     F .text	000000000000043c ipc_port_connect_async
ffffffff0001cc98 l     F .text	00000000000001cc wait_for_hup
ffffffff0001cc98 l       .text	0000000000000000 $x.223
ffffffff0001ce64 l     F .text	00000000000002f8 ktipctest_blockedport.cfi
ffffffff0001ce64 l       .text	0000000000000000 $x.224
ffffffff0001d15c l     F .text	00000000000006d8 ktipctest_echo.cfi
ffffffff0001d15c l       .text	0000000000000000 $x.225
ffffffff0015d908 l     O .data	000000000000001c test_pattern
ffffffff0001d834 l     F .text	00000000000001f0 send_cmd
ffffffff0001d834 l       .text	0000000000000000 $x.226
ffffffff00023f38 l     F .text	0000000000000454 msg_write_locked
ffffffff00020574 l     F .text	000000000000011c handle_notify
ffffffff0001da24 l     F .text	00000000000008c0 ktipctest_echo8.cfi
ffffffff0001da24 l       .text	0000000000000000 $x.227
ffffffff0001e2e4 l     F .text	00000000000007ec ktipctest_close.cfi
ffffffff0001e2e4 l       .text	0000000000000000 $x.228
ffffffff0001ead0 l     F .text	000000000000073c ktipctest_blockedsend.cfi
ffffffff0001ead0 l       .text	0000000000000000 $x.229
ffffffff0001f20c l     F .text	0000000000000180 ktipc_test_server_init.cfi
ffffffff0001f20c l       .text	0000000000000000 $x.230
ffffffff0015d9b0 l     O .data	0000000000000048 ktipc_test_server
ffffffff00036fa0 l     F .text	0000000000000040 ksrv_thread
ffffffff0015d9f8 l     O .data	0000000000000028 test_srv_port
ffffffff0015da20 l     O .data	0000000000000028 test_srv_ops
ffffffff0015da48 l     O .data	0000000000000028 blocked_srv_port
ffffffff0015da70 l     O .data	0000000000000028 connecterr_srv_port
ffffffff0015da98 l     O .data	0000000000000028 connecterr_srv_ops
ffffffff0001f38c l     F .text	000000000000003c connecterr_handle_connect.cfi
ffffffff0001f38c l       .text	0000000000000000 $x.231
ffffffff0001f3c8 l     F .text	0000000000000008 nop_handle_msg.cfi
ffffffff0001f3c8 l       .text	0000000000000000 $x.232
ffffffff0001f3d0 l     F .text	0000000000000004 nop_handle_channel_cleanup.cfi
ffffffff0001f3d0 l       .text	0000000000000000 $x.233
ffffffff0001f3d4 l     F .text	000000000000006c test_handle_connect.cfi
ffffffff0001f3d4 l       .text	0000000000000000 $x.234
ffffffff0001f440 l     F .text	0000000000000388 test_handle_msg.cfi
ffffffff0001f440 l       .text	0000000000000000 $x.235
ffffffff0016ad3c l     O .bss	0000000000000040 echo_buf
ffffffff000429cc l       .rodata	0000000000000000 $d.236
ffffffff0001f7c8 l     F .text	0000000000000060 test_handle_channel_cleanup.cfi
ffffffff0001f7c8 l       .text	0000000000000000 $x.237
ffffffff0016ad7c l     O .bss	0000000000000004 close_counter
ffffffff0001f828 l     F .text	00000000000001c8 test_handle_send_unblocked.cfi
ffffffff0001f828 l       .text	0000000000000000 $x.238
ffffffff0001f9f0 l     F .text	000000000000006c parse_u8
ffffffff0001f9f0 l       .text	0000000000000000 $x.239
ffffffff0001fa5c l     F .text	0000000000000098 memlog_init.cfi
ffffffff0001fa5c l       .text	0000000000000000 $x.240
ffffffff0015e640 l     O .data	0000000000000038 smc_table_lock
ffffffff0015e040 l     O .data	0000000000000200 sm_fastcall_table
ffffffff00036f58 l     F .text	0000000000000060 smc_undefined
ffffffff0015e240 l     O .data	0000000000000200 sm_nopcall_table
ffffffff0015e440 l     O .data	0000000000000200 sm_stdcall_table
ffffffff00036f40 l     F .text	0000000000000060 memlog_stdcall
ffffffff0001faf4 l     F .text	000000000000037c memlog_stdcall.cfi
ffffffff0001faf4 l       .text	0000000000000000 $x.241
ffffffff0015db98 l     O .data	0000000000000010 log_list
ffffffff00036e68 l     F .text	0000000000000004 memlog_print_callback
ffffffff00036e6c l     F .text	0000000000000004 memlog_commit_callback
ffffffff00169c98 l     O .bss	0000000000001000 early_log_buffer
ffffffff0001fe70 l     F .text	0000000000000190 memlog_print_callback.cfi
ffffffff0001fe70 l       .text	0000000000000000 $x.242
ffffffff0016ad80 l     O .bss	0000000000000008 log_lock
ffffffff00020000 l     F .text	00000000000000d0 memlog_commit_callback.cfi
ffffffff00020000 l       .text	0000000000000000 $x.243
ffffffff000200d0 l     F .text	00000000000000d8 handle_init_etc
ffffffff000200d0 l       .text	0000000000000000 $x.244
ffffffff000201a8 l       .text	0000000000000000 $x.245
ffffffff00020230 l     F .text	00000000000000c0 handle_close
ffffffff00020230 l       .text	0000000000000000 $x.247
ffffffff000202f0 l     F .text	00000000000000c0 handle_del_waiter
ffffffff000202f0 l       .text	0000000000000000 $x.248
ffffffff000203b0 l       .text	0000000000000000 $x.249
ffffffff00036e70 l     F .text	000000000000000c handle_event_waiter_notify
ffffffff00020568 l     F .text	000000000000000c handle_event_waiter_notify.cfi
ffffffff00020568 l       .text	0000000000000000 $x.250
ffffffff00020574 l       .text	0000000000000000 $x.251
ffffffff00020690 l     F .text	00000000000001f0 handle_list_add
ffffffff00020690 l       .text	0000000000000000 $x.252
ffffffff00020880 l     F .text	000000000000015c handle_list_del
ffffffff00020880 l       .text	0000000000000000 $x.253
ffffffff000209dc l     F .text	00000000000000a0 _finish_wait_handle
ffffffff000209dc l       .text	0000000000000000 $x.254
ffffffff00020a7c l     F .text	0000000000000094 hset_poll.cfi
ffffffff00020a7c l       .text	0000000000000000 $x.255
ffffffff00020b10 l     F .text	0000000000000084 hset_destroy.cfi
ffffffff00020b10 l       .text	0000000000000000 $x.256
ffffffff00020b94 l       .text	0000000000000000 $x.257
ffffffff00020dd4 l     F .text	0000000000000288 hset_attach_ref
ffffffff0015dbe0 l     O .data	0000000000000038 g_hset_lock
ffffffff00020d18 l     F .text	00000000000000bc hset_find_target
ffffffff00020d18 l       .text	0000000000000000 $x.258
ffffffff00020dd4 l       .text	0000000000000000 $x.259
ffffffff00036e74 l     F .text	000000000000000c hset_waiter_notify
ffffffff0002105c l     F .text	00000000000001a8 hset_waiter_notify.cfi
ffffffff0002105c l       .text	0000000000000000 $x.260
ffffffff00021204 l       .text	0000000000000000 $x.261
ffffffff0002146c l       .text	0000000000000000 $x.262
ffffffff00036e78 l     F .text	000000000000000c handle_event_waiter_notify.991
ffffffff00021854 l     F .text	000000000000000c handle_event_waiter_notify.991.cfi
ffffffff00021854 l       .text	0000000000000000 $x.263
ffffffff00021860 l     F .text	0000000000000114 handle_ref_wait
ffffffff00021860 l       .text	0000000000000000 $x.264
ffffffff00021974 l     F .text	00000000000000fc user_iovec_to_membuf
ffffffff00021974 l       .text	0000000000000000 $x.265
ffffffff00021a70 l     F .text	0000000000000124 chan_poll.cfi
ffffffff00021a70 l       .text	0000000000000000 $x.266
ffffffff00021b94 l     F .text	00000000000000c4 chan_handle_destroy.cfi
ffffffff00021b94 l       .text	0000000000000000 $x.267
ffffffff0015dca8 l     O .data	0000000000000038 ipc_port_lock
ffffffff00021c58 l     F .text	00000000000000d0 chan_shutdown
ffffffff00021d28 l     F .text	0000000000000194 chan_del_ref
ffffffff00021c58 l       .text	0000000000000000 $x.268
ffffffff00021ebc l     F .text	00000000000000b4 chan_shutdown_locked
ffffffff00021d28 l       .text	0000000000000000 $x.269
ffffffff00023c6c l     F .text	0000000000000110 ipc_msg_queue_destroy
ffffffff00021ebc l       .text	0000000000000000 $x.270
ffffffff00021f70 l     F .text	000000000000012c chan_add_ref
ffffffff00021f70 l       .text	0000000000000000 $x.271
ffffffff0002209c l       .text	0000000000000000 $x.272
ffffffff0015dc78 l     O .data	0000000000000030 ipc_port_handle_ops
ffffffff00022244 l     F .text	00000000000000ac port_poll.cfi
ffffffff00022244 l       .text	0000000000000000 $x.273
ffffffff000222f0 l     F .text	00000000000004a0 port_handle_destroy.cfi
ffffffff000222f0 l       .text	0000000000000000 $x.274
ffffffff0015de10 l     O .data	0000000000000038 apps_lock
ffffffff0015de68 l     O .data	0000000000000010 rctee_app_list
ffffffff0015dce0 l     O .data	0000000000000010 waiting_for_port_chan_list
ffffffff00022790 l       .text	0000000000000000 $x.275
ffffffff0015dcf0 l     O .data	0000000000000010 ipc_port_list
ffffffff00022a28 l     F .text	000000000000015c port_attach_client
ffffffff00022a28 l       .text	0000000000000000 $x.276
ffffffff000434b4 l     O .rodata	0000000000000010 zero_uuid
ffffffff00022b84 l     F .text	0000000000000184 chan_alloc
ffffffff00023b48 l     F .text	0000000000000124 ipc_msg_queue_create
ffffffff00022b84 l       .text	0000000000000000 $x.277
ffffffff00022d08 l     F .text	000000000000028c sys_port_create
ffffffff00022d08 l       .text	0000000000000000 $x.278
ffffffff0016ad88 l     O .bss	0000000000000004 _uctx_slot_id
ffffffff0016ad90 l     O .bss	0000000000000004 als_slot_cnt
ffffffff00026eec l     F .text	0000000000000348 uctx_handle_install
ffffffff000273a8 l     F .text	0000000000000188 uctx_handle_remove
ffffffff00022f94 l       .text	0000000000000000 $x.279
ffffffff0015de78 l     O .data	0000000000000030 app_mgr_event
ffffffff000233d0 l     F .text	00000000000002d4 sys_connect
ffffffff000233d0 l       .text	0000000000000000 $x.280
ffffffff0015dba8 l     O .data	0000000000000038 es_lock
ffffffff000236a4 l       .text	0000000000000000 $x.281
ffffffff000238f4 l     F .text	0000000000000254 sys_accept
ffffffff000238f4 l       .text	0000000000000000 $x.282
ffffffff00027234 l     F .text	0000000000000174 uctx_handle_get
ffffffff00023b48 l       .text	0000000000000000 $x.283
ffffffff00023c6c l       .text	0000000000000000 $x.284
ffffffff00023d7c l     F .text	00000000000001bc sys_send_msg
ffffffff00023d7c l       .text	0000000000000000 $x.285
ffffffff00023f38 l       .text	0000000000000000 $x.286
ffffffff0002438c l     F .text	00000000000000a8 ipc_send_msg
ffffffff0002438c l       .text	0000000000000000 $x.287
ffffffff00024434 l     F .text	0000000000000244 sys_get_msg
ffffffff00024434 l       .text	0000000000000000 $x.288
ffffffff00024678 l     F .text	00000000000000f8 ipc_get_msg
ffffffff00024678 l       .text	0000000000000000 $x.289
ffffffff00024770 l     F .text	0000000000000134 sys_put_msg
ffffffff00024770 l       .text	0000000000000000 $x.290
ffffffff000248a4 l       .text	0000000000000000 $x.291
ffffffff00024b58 l     F .text	000000000000066c sys_read_msg
ffffffff00024b58 l       .text	0000000000000000 $x.292
ffffffff000251c4 l       .text	0000000000000000 $x.293
ffffffff000253e8 l     F .text	0000000000000210 memref_create_from_vmm_obj
ffffffff000253e8 l       .text	0000000000000000 $x.294
ffffffff0015dd00 l     O .data	0000000000000030 memref_handle_ops
ffffffff000255f8 l     F .text	0000000000000110 memref_handle_destroy.cfi
ffffffff000255f8 l       .text	0000000000000000 $x.295
ffffffff00025708 l     F .text	0000000000000178 memref_mmap.cfi
ffffffff00025708 l       .text	0000000000000000 $x.296
ffffffff00025880 l       .text	0000000000000000 $x.297
ffffffff0015dd30 l     O .data	0000000000000038 fd_lock
ffffffff0015dd68 l     O .data	0000000000000050 sys_fds
ffffffff000258d8 l     F .text	00000000000002dc sys_std_writev.cfi
ffffffff000258d8 l       .text	0000000000000000 $x.298
ffffffff00025bb4 l     F .text	0000000000000060 sys_writev.cfi
ffffffff00025bb4 l       .text	0000000000000000 $x.299
ffffffff0015ddd0 l     O .data	0000000000000018 fd_op
ffffffff00025c14 l     F .text	00000000000000cc sys_brk
ffffffff00025c14 l       .text	0000000000000000 $x.300
ffffffff00025ce0 l     F .text	0000000000000014 sys_exit_etc
ffffffff00025ce0 l       .text	0000000000000000 $x.301
ffffffff0002c660 l     F .text	000000000000001c rctee_app_exit
ffffffff00025cf4 l     F .text	0000000000000060 sys_readv.cfi
ffffffff00025cf4 l       .text	0000000000000000 $x.302
ffffffff00025d54 l     F .text	000000000000007c sys_ioctl
ffffffff00025d54 l       .text	0000000000000000 $x.303
ffffffff00025dd0 l     F .text	0000000000000024 sys_nanosleep
ffffffff00025dd0 l       .text	0000000000000000 $x.304
ffffffff00025df4 l     F .text	00000000000000a8 sys_gettime
ffffffff00025df4 l       .text	0000000000000000 $x.305
ffffffff00025e9c l     F .text	0000000000000308 sys_mmap
ffffffff00025e9c l       .text	0000000000000000 $x.306
ffffffff000261a4 l     F .text	0000000000000044 sys_munmap
ffffffff000261a4 l       .text	0000000000000000 $x.307
ffffffff000261e8 l     F .text	0000000000000264 sys_prepare_dma
ffffffff000261e8 l       .text	0000000000000000 $x.308
ffffffff0002c19c l     F .text	00000000000001ac rctee_app_allow_dma_range
ffffffff0002644c l     F .text	00000000000002c0 sys_finish_dma
ffffffff0002644c l       .text	0000000000000000 $x.309
ffffffff0002670c l     F .text	0000000000000010 sys_set_user_tls
ffffffff0002670c l       .text	0000000000000000 $x.310
ffffffff0002671c l     F .text	0000000000000304 sys_memref_create
ffffffff0002671c l       .text	0000000000000000 $x.311
ffffffff00026a20 l     F .text	0000000000000010 sys_dump_memory_info
ffffffff00026a20 l       .text	0000000000000000 $x.312
ffffffff00026a30 l     F .text	0000000000000108 uctx_init.cfi
ffffffff00026a30 l       .text	0000000000000000 $x.313
ffffffff0016ad8c l     O .bss	0000000000000001 apps_started
ffffffff0015de58 l     O .data	0000000000000010 app_notifier_list
ffffffff0015dde8 l     O .data	0000000000000028 _uctx_notifier
ffffffff00026b38 l     F .text	00000000000000fc _uctx_startup.cfi
ffffffff00026b38 l       .text	0000000000000000 $x.314
ffffffff00026c34 l     F .text	000000000000014c _uctx_shutdown.cfi
ffffffff00026c34 l       .text	0000000000000000 $x.315
ffffffff00026d80 l     F .text	000000000000016c remove_handle
ffffffff00026d80 l       .text	0000000000000000 $x.316
ffffffff00026eec l       .text	0000000000000000 $x.317
ffffffff00027234 l       .text	0000000000000000 $x.318
ffffffff000273a8 l       .text	0000000000000000 $x.319
ffffffff00027530 l     F .text	0000000000000320 sys_wait.cfi
ffffffff00027530 l       .text	0000000000000000 $x.320
ffffffff00027850 l     F .text	0000000000000278 sys_wait_any
ffffffff00027850 l       .text	0000000000000000 $x.321
ffffffff00027ac8 l     F .text	0000000000000400 rebuild_hset_all
ffffffff00027ac8 l       .text	0000000000000000 $x.322
ffffffff00027ec8 l     F .text	0000000000000148 sys_dup
ffffffff00027ec8 l       .text	0000000000000000 $x.323
ffffffff00028010 l     F .text	0000000000000148 sys_close
ffffffff00028010 l       .text	0000000000000000 $x.324
ffffffff00028158 l     F .text	0000000000000148 sys_set_cookie
ffffffff00028158 l       .text	0000000000000000 $x.325
ffffffff000282a0 l     F .text	00000000000001cc sys_handle_set_create
ffffffff000282a0 l       .text	0000000000000000 $x.326
ffffffff0002846c l     F .text	000000000000050c sys_handle_set_ctrl
ffffffff0002846c l       .text	0000000000000000 $x.327
ffffffff00028978 l     F .text	00000000000000f0 uctx_handle_readv.cfi
ffffffff00028978 l       .text	0000000000000000 $x.328
ffffffff00028a68 l     F .text	00000000000000f0 uctx_handle_writev.cfi
ffffffff00028a68 l       .text	0000000000000000 $x.329
ffffffff00028b58 l     F .text	0000000000000100 start_apps.cfi
ffffffff00028b58 l       .text	0000000000000000 $x.330
ffffffff00028c58 l     F .text	00000000000001cc rctee_init.cfi
ffffffff00028c58 l       .text	0000000000000000 $x.331
ffffffff00036fa4 l     F .text	0000000000000040 app_mgr
ffffffff0002a8b0 l     F .text	00000000000003e8 rctee_app_create
ffffffff00028e24 l     F .text	0000000000001a8c app_mgr.cfi
ffffffff00028e24 l       .text	0000000000000000 $x.332
ffffffff00036fa8 l     F .text	0000000000000040 rctee_thread_startup
ffffffff0002a8b0 l       .text	0000000000000000 $x.333
ffffffff0016ada0 l     O .bss	0000000000000004 rctee_next_app_id
ffffffff0002ac98 l     F .text	00000000000010e4 load_app_config_options
ffffffff0002ac98 l       .text	0000000000000000 $x.334
ffffffff0015de48 l     O .data	0000000000000010 allowed_mmio_ranges_list
ffffffff0015d518 l     O .data	0000000000000018 phys_mem_obj_ops
ffffffff00036ebc l     F .text	0000000000000004 destroy_app_phys_mem
ffffffff0002bdcc l     F .text	000000000000009c rctee_app_find_by_uuid_locked
ffffffff000429d0 l       .rodata	0000000000000000 $d.335
ffffffff0002bd7c l     F .text	0000000000000050 destroy_app_phys_mem.cfi
ffffffff0002bd7c l       .text	0000000000000000 $x.336
ffffffff0002bdcc l       .text	0000000000000000 $x.337
ffffffff0002be68 l     F .text	000000000000006c rctee_thread_startup.cfi
ffffffff0002be68 l       .text	0000000000000000 $x.338
ffffffff0002bed4 l     F .text	0000000000000214 rctee_thread_write_elf_tables
ffffffff0002bed4 l       .text	0000000000000000 $x.339
ffffffff0002c0e8 l       .text	0000000000000000 $x.340
ffffffff0002c19c l       .text	0000000000000000 $x.341
ffffffff0002c348 l     F .text	00000000000000ec rctee_thread_exit
ffffffff0002c348 l       .text	0000000000000000 $x.342
ffffffff0002c434 l     F .text	000000000000022c rctee_app_create_and_start
ffffffff0002c434 l       .text	0000000000000000 $x.343
ffffffff0002c660 l       .text	0000000000000000 $x.344
ffffffff0002c67c l     F .text	0000000000000260 rctee_app_exit_etc
ffffffff0002c67c l       .text	0000000000000000 $x.345
ffffffff0002c8dc l     F .text	0000000000000010 mutex_acquire
ffffffff0002c8dc l       .text	0000000000000000 $x.346
ffffffff0002c8ec l       .text	0000000000000000 $x.347
ffffffff0002c908 l     F .text	00000000000000d4 vqueue_destroy
ffffffff0002c908 l       .text	0000000000000000 $x.348
ffffffff0002c9dc l     F .text	00000000000000b0 vqueue_signal_avail
ffffffff0002c9dc l       .text	0000000000000000 $x.349
ffffffff0002ca8c l     F .text	00000000000002fc vqueue_get_avail_buf
ffffffff0002ca8c l       .text	0000000000000000 $x.350
ffffffff0002cd88 l     F .text	00000000000001a8 vqueue_map_iovs
ffffffff0002cd88 l       .text	0000000000000000 $x.351
ffffffff0002cf30 l     F .text	000000000000017c vqueue_unmap_iovs
ffffffff0002cf30 l       .text	0000000000000000 $x.352
ffffffff0002d0ac l     F .text	000000000000015c vqueue_add_buf
ffffffff0002d0ac l       .text	0000000000000000 $x.353
ffffffff0002d208 l     F .text	00000000000000d4 rctee_sm_init.cfi
ffffffff0002d208 l       .text	0000000000000000 $x.354
ffffffff00036f44 l     F .text	0000000000000060 rctee_sm_fastcall
ffffffff00036f48 l     F .text	0000000000000060 rctee_sm_nopcall
ffffffff00036f4c l     F .text	0000000000000060 rctee_sm_stdcall
ffffffff0002d2dc l     F .text	0000000000000078 rctee_sm_fastcall.cfi
ffffffff0002d2dc l       .text	0000000000000000 $x.355
ffffffff00031610 l     F .text	00000000000009e4 ql_rcipc_handle_cmd
ffffffff0002d354 l     F .text	00000000000000d8 rctee_sm_nopcall.cfi
ffffffff0002d354 l       .text	0000000000000000 $x.356
ffffffff0015def0 l     O .data	0000000000000028 _virtio_bus
ffffffff0002d42c l     F .text	0000000000000d08 rctee_sm_stdcall.cfi
ffffffff0002d42c l       .text	0000000000000000 $x.357
ffffffff0015df68 l     O .data	0000000000000010 _dev_list
ffffffff000314f8 l     F .text	0000000000000118 dev_acquire
ffffffff0016ae30 l     O .bss	0000000000000008 _dev_list_lock
ffffffff0016ae28 l     O .bss	0000000000000004 _dev_cnt
ffffffff0015dea8 l     O .data	0000000000000038 virtio_bus_notifier_lock
ffffffff0015dee0 l     O .data	0000000000000010 virtio_bus_notifier_list
ffffffff00042a3c l       .rodata	0000000000000000 $d.358
ffffffff0002e134 l     F .text	0000000000000158 rcipc_ext_mem_destroy.cfi
ffffffff0002e134 l       .text	0000000000000000 $x.359
ffffffff00036ec0 l     F .text	0000000000000008 _send_buf
ffffffff0002e378 l     F .text	0000000000000238 rcipc_send_data
ffffffff0002e28c l     F .text	00000000000000ec _send_buf.cfi
ffffffff0002e28c l       .text	0000000000000000 $x.360
ffffffff0002e378 l       .text	0000000000000000 $x.361
ffffffff0002e5b0 l     F .text	00000000000000c4 tx_data_cb.cfi
ffffffff0002e5b0 l       .text	0000000000000000 $x.362
ffffffff0002e674 l     F .text	0000000000000008 rcipc_descr_size.cfi
ffffffff0002e674 l       .text	0000000000000000 $x.363
ffffffff0002e67c l     F .text	0000000000000040 rcipc_get_vdev_descr.cfi
ffffffff0002e67c l       .text	0000000000000000 $x.364
ffffffff0002e6bc l     F .text	00000000000003fc rcipc_vdev_probe.cfi
ffffffff0002e6bc l       .text	0000000000000000 $x.365
ffffffff00036fac l     F .text	0000000000000040 conn_req_thread_func
ffffffff0016ae20 l     O .bss	0000000000000008 conn_req_thread
ffffffff0015df58 l     O .data	0000000000000010 notify_cbs
ffffffff00036fb0 l     F .text	0000000000000040 rcipc_rx_thread_func
ffffffff00036fb4 l     F .text	0000000000000040 rcipc_tx_thread_func
ffffffff00030b74 l     F .text	0000000000000054 _go_online
ffffffff0002eab8 l     F .text	0000000000000294 rcipc_vdev_reset.cfi
ffffffff0002eab8 l       .text	0000000000000000 $x.366
ffffffff0002ed4c l     F .text	00000000000000d0 rcipc_vdev_kick_vq.cfi
ffffffff0002ed4c l       .text	0000000000000000 $x.367
ffffffff0002ee1c l     F .text	0000000000000cb8 conn_req_thread_func.cfi
ffffffff0002ee1c l       .text	0000000000000000 $x.368
ffffffff0016ad98 l     O .bss	0000000000000008 g_apploader_chandle
ffffffff0002fad4 l     F .text	0000000000000810 rcipc_rx_thread_func.cfi
ffffffff0002fad4 l       .text	0000000000000000 $x.369
ffffffff0015df40 l     O .data	0000000000000018 rcipc_ext_mem_ops
ffffffff00030bc8 l     F .text	0000000000000898 handle_ctrl_msg
ffffffff000302e4 l     F .text	0000000000000890 rcipc_tx_thread_func.cfi
ffffffff000302e4 l       .text	0000000000000000 $x.370
ffffffff00036ec4 l     F .text	0000000000000008 tx_data_cb
ffffffff00030b74 l       .text	0000000000000000 $x.371
ffffffff00030bc8 l       .text	0000000000000000 $x.372
ffffffff00031460 l     F .text	000000000000002c rcipc_ext_mem_check_flags.cfi
ffffffff00031460 l       .text	0000000000000000 $x.373
ffffffff0003148c l     F .text	000000000000002c rcipc_ext_mem_get_page.cfi
ffffffff0003148c l       .text	0000000000000000 $x.374
ffffffff000314b8 l     F .text	0000000000000020 rcipc_tx_vq_notify_cb.cfi
ffffffff000314b8 l       .text	0000000000000000 $x.375
ffffffff000314d8 l     F .text	0000000000000020 rcipc_rx_vq_notify_cb.cfi
ffffffff000314d8 l       .text	0000000000000000 $x.376
ffffffff000314f8 l       .text	0000000000000000 $x.377
ffffffff00031610 l       .text	0000000000000000 $x.378
ffffffff00042a70 l       .rodata	0000000000000000 $d.379
ffffffff00031ff4 l     F .text	000000000000005c register_rcipc_init.cfi
ffffffff00031ff4 l       .text	0000000000000000 $x.380
ffffffff0015df78 l     O .data	0000000000000018 register_rcipc_init.vb_notifier
ffffffff00032050 l     F .text	0000000000000198 rcipc_init.cfi
ffffffff00032050 l       .text	0000000000000000 $x.381
ffffffff0015df18 l     O .data	0000000000000028 _rcipc_dev_ops
ffffffff000434c4 l     O .rodata	000000000000006c _descr0
ffffffff000321e8 l     F .text	00000000000000d4 sm_release_boot_args.cfi
ffffffff000321e8 l       .text	0000000000000000 $x.382
ffffffff0016af38 l     O .bss	0000000000000008 boot_args
ffffffff0015e008 l     O .data	0000000000000038 boot_args_lock
ffffffff0016af40 l     O .bss	0000000000000004 boot_args_refcnt
ffffffff000322bc l     F .text	0000000000000108 resume_nsthreads
ffffffff000322bc l       .text	0000000000000000 $x.383
ffffffff0016af18 l     O .bss	0000000000000020 nsirqthreads
ffffffff0016af68 l     O .bss	0000000000000020 nsidlethreads
ffffffff000323c4 l     F .text	0000000000000290 sm_init.cfi
ffffffff000323c4 l       .text	0000000000000000 $x.384
ffffffff00036fbc l     F .text	0000000000000040 sm_irq_loop
ffffffff00036fc0 l     F .text	0000000000000040 sm_wait_for_smcall
ffffffff00036fb8 l     F .text	0000000000000040 sm_stdcall_loop
ffffffff0016af88 l     O .bss	0000000000000008 stdcallthread
ffffffff00032654 l     F .text	000000000000015c sm_stdcall_loop.cfi
ffffffff00032654 l       .text	0000000000000000 $x.385
ffffffff000327b0 l     F .text	00000000000000ac sm_irq_loop.cfi
ffffffff000327b0 l       .text	0000000000000000 $x.386
ffffffff0003285c l     F .text	0000000000000124 sm_wait_for_smcall.cfi
ffffffff0003285c l       .text	0000000000000000 $x.387
ffffffff00032980 l     F .text	0000000000000240 sm_check_and_lock_api_version
ffffffff00032980 l       .text	0000000000000000 $x.388
ffffffff0016ae40 l     O .bss	0000000000000004 sm_api_version_min
ffffffff0015e000 l     O .data	0000000000000004 sm_api_version_max
ffffffff0016ae38 l     O .bss	0000000000000008 sm_api_version_lock
ffffffff0016ae44 l     O .bss	0000000000000004 sm_api_version
ffffffff00032bc0 l       .text	0000000000000000 $x.389
ffffffff0016af10 l     O .bss	0000000000000001 platform_halted
ffffffff00032e1c l     F .text	00000000000000ac smc_sm_api_version.cfi
ffffffff00032e1c l       .text	0000000000000000 $x.390
ffffffff00032ec8 l     F .text	0000000000000008 smc_get_smp_max_cpus.cfi
ffffffff00032ec8 l       .text	0000000000000000 $x.391
ffffffff00032ed0 l       .text	0000000000000000 $x.392
ffffffff00033050 l     F .text	00000000000000dc smc_undefined.cfi
ffffffff00033050 l       .text	0000000000000000 $x.393
ffffffff0003312c l       .text	0000000000000000 $x.394
ffffffff00033204 l     F .text	0000000000000050 smc_stdcall_secure_monitor.cfi
ffffffff00033204 l       .text	0000000000000000 $x.395
ffffffff0015e6e8 l     O .data	0000000000000030 sm_stdcall_function_table
ffffffff00033254 l     F .text	00000000000000dc smc_restart_stdcall.cfi
ffffffff00033254 l       .text	0000000000000000 $x.396
ffffffff00033330 l     F .text	0000000000000008 smc_nop_stdcall.cfi
ffffffff00033330 l       .text	0000000000000000 $x.397
ffffffff00033338 l     F .text	0000000000000014 smc_nop_secure_monitor.cfi
ffffffff00033338 l       .text	0000000000000000 $x.398
ffffffff0003334c l     F .text	0000000000000050 smc_fastcall_secure_monitor.cfi
ffffffff0003334c l       .text	0000000000000000 $x.399
ffffffff0015e678 l     O .data	0000000000000070 sm_fastcall_function_table
ffffffff0003339c l     F .text	0000000000000020 smc_fiq_enter.cfi
ffffffff0003339c l       .text	0000000000000000 $x.400
ffffffff000333bc l     F .text	0000000000000130 smc_cpu_suspend.cfi
ffffffff000333bc l       .text	0000000000000000 $x.401
ffffffff000334ec l     F .text	0000000000000130 smc_cpu_resume.cfi
ffffffff000334ec l       .text	0000000000000000 $x.402
ffffffff0003361c l     F .text	0000000000000050 smc_get_version_str.cfi
ffffffff0003361c l       .text	0000000000000000 $x.403
ffffffff0003366c l     F .text	000000000000006c shared_mem_init.cfi
ffffffff0003366c l       .text	0000000000000000 $x.404
ffffffff000336d8 l       .text	0000000000000000 $x.405
ffffffff0015e730 l     O .data	0000000000000018 sm_mem_obj_compat_ops
ffffffff0015e718 l     O .data	0000000000000018 sm_mem_obj_ops
ffffffff00042a84 l       .rodata	0000000000000000 $d.406
ffffffff000341b4 l     F .text	0000000000000008 sm_mem_obj_compat_destroy.cfi
ffffffff000341b4 l       .text	0000000000000000 $x.407
ffffffff000341bc l     F .text	0000000000000200 sm_mem_obj_destroy.cfi
ffffffff000341bc l       .text	0000000000000000 $x.408
ffffffff000343bc l     F .text	0000000000000284 smc_trusty_sched_share_register.cfi
ffffffff000343bc l       .text	0000000000000000 $x.409
ffffffff0016af98 l     O .bss	0000000000000008 sched_shared_datalock
ffffffff0016af90 l     O .bss	0000000000000008 sched_shared_mem
ffffffff0016afa0 l     O .bss	0000000000000020 shareinfo
ffffffff00034640 l     F .text	0000000000000168 smc_trusty_sched_share_unregister.cfi
ffffffff00034640 l       .text	0000000000000000 $x.410
ffffffff000347a8 l       .text	0000000000000000 $x.411
ffffffff00034890 l       .text	0000000000000000 $x.412
ffffffff000348d8 l       .text	0000000000000000 $x.413
ffffffff0016afc0 l     O .bss	0000000000000008 ipc_printf_handle
ffffffff000349b8 l     F .text	000000000000017c send_msg_wait
ffffffff000349b8 l       .text	0000000000000000 $x.414
ffffffff00034b34 l     F .text	00000000000001c0 unittest_loop.cfi
ffffffff00034b34 l       .text	0000000000000000 $x.415
ffffffff00034cf4 l     F .text	00000000000000fc apploader_service_init.cfi
ffffffff00034cf4 l       .text	0000000000000000 $x.416
ffffffff0015e9c8 l     O .data	0000000000000048 apploader_ktipc_server
ffffffff0015ea10 l     O .data	0000000000000028 apploader_service_port
ffffffff0015ea38 l     O .data	0000000000000028 apploader_service_ops
ffffffff00034df0 l     F .text	0000000000000074 apploader_service_handle_connect.cfi
ffffffff00034df0 l       .text	0000000000000000 $x.417
ffffffff00034e64 l     F .text	00000000000009c8 apploader_service_handle_msg.cfi
ffffffff00034e64 l       .text	0000000000000000 $x.418
ffffffff0003596c l     F .text	00000000000000e4 apploader_service_send_response
ffffffff0003590c l     F .text	0000000000000060 apploader_service_translate_error
ffffffff0003582c l     F .text	00000000000000e0 apploader_service_handle_channel_cleanup.cfi
ffffffff0003582c l       .text	0000000000000000 $x.419
ffffffff0003590c l       .text	0000000000000000 $x.420
ffffffff0003596c l       .text	0000000000000000 $x.421
ffffffff00035a50 l     F .text	00000000000000fc generic_ta_service_init.cfi
ffffffff00035a50 l       .text	0000000000000000 $x.422
ffffffff0015ea80 l     O .data	0000000000000048 generic_ta_ktipc_server
ffffffff0015eac8 l     O .data	0000000000000028 generic_ta_service_port
ffffffff0015eaf0 l     O .data	0000000000000028 generic_ta_service_ops
ffffffff00035b4c l     F .text	00000000000000e8 generic_ta_service_handle_connect.cfi
ffffffff00035b4c l       .text	0000000000000000 $x.423
ffffffff00035c34 l     F .text	0000000000000968 generic_ta_service_handle_msg.cfi
ffffffff00035c34 l       .text	0000000000000000 $x.424
ffffffff0016b070 l     O .bss	0000000000000010 generic_ta_service_handle_get_property_string.version_str
ffffffff00042af4 l       .rodata	0000000000000000 $d.425
ffffffff0003659c l     F .text	0000000000000004 generic_ta_service_handle_channel_cleanup.cfi
ffffffff0003659c l       .text	0000000000000000 $x.426
ffffffff000365a0 l     F .text	00000000000000fc hwrng_ktipc_server_init.cfi
ffffffff000365a0 l       .text	0000000000000000 $x.427
ffffffff0015eb18 l     O .data	0000000000000048 hwrng_ktipc_server
ffffffff0015eb60 l     O .data	0000000000000028 hwrng_srv_port
ffffffff0015eb88 l     O .data	0000000000000028 hwrng_srv_ops
ffffffff0003669c l     F .text	0000000000000078 hwrng_handle_connect.cfi
ffffffff0003669c l       .text	0000000000000000 $x.428
ffffffff00036714 l     F .text	00000000000000f8 hwrng_handle_msg.cfi
ffffffff00036714 l       .text	0000000000000000 $x.429
ffffffff00036860 l     F .text	000000000000023c hwrng_handle_req_queue
ffffffff0015ebb0 l     O .data	0000000000000010 hwrng_req_list
ffffffff0003680c l     F .text	000000000000001c hwrng_handle_channel_cleanup.cfi
ffffffff0003680c l       .text	0000000000000000 $x.430
ffffffff00036828 l     F .text	0000000000000038 hwrng_handle_send_unblocked.cfi
ffffffff00036828 l       .text	0000000000000000 $x.431
ffffffff00036860 l       .text	0000000000000000 $x.432
ffffffff0016b080 l     O .bss	0000000000000008 rng_data_avail_count
ffffffff0016b088 l     O .bss	0000000000000080 rng_data
ffffffff0016b108 l     O .bss	0000000000000008 rng_data_avail_pos
ffffffff00036a9c l     F .text	00000000000000fc smc_service_init.cfi
ffffffff00036a9c l       .text	0000000000000000 $x.433
ffffffff0015ebc0 l     O .data	0000000000000048 smc_ktipc_server
ffffffff0015ec08 l     O .data	0000000000000028 smc_service_port
ffffffff0015ec30 l     O .data	0000000000000028 smc_service_ops
ffffffff00036b98 l     F .text	0000000000000088 smc_service_handle_connect.cfi
ffffffff00036b98 l       .text	0000000000000000 $x.434
ffffffff00036c20 l     F .text	00000000000001e0 smc_service_handle_msg.cfi
ffffffff00036c20 l       .text	0000000000000000 $x.435
ffffffff00036e00 l     F .text	0000000000000004 smc_service_handle_channel_cleanup.cfi
ffffffff00036e00 l       .text	0000000000000000 $x.436
ffffffff00036e04 l       .text	0000000000000000 $x.437
ffffffff00036e08 l       .text	0000000000000000 $x.438
ffffffff00036e10 l       .text	0000000000000000 $x.439
ffffffff00036e1c l       .text	0000000000000000 $x.440
ffffffff00036e28 l       .text	0000000000000000 $x.441
ffffffff00036e2c l       .text	0000000000000000 $x.443
ffffffff00036e30 l       .text	0000000000000000 $x.444
ffffffff00036e34 l       .text	0000000000000000 $x.445
ffffffff00036e40 l       .text	0000000000000000 $x.446
ffffffff00036e48 l       .text	0000000000000000 $x.447
ffffffff00036e4c l       .text	0000000000000000 $x.448
ffffffff00036e68 l       .text	0000000000000000 $x.449
ffffffff00036e6c l       .text	0000000000000000 $x.450
ffffffff00036e70 l       .text	0000000000000000 $x.452
ffffffff00036e7c l       .text	0000000000000000 $x.453
ffffffff00036e88 l       .text	0000000000000000 $x.454
ffffffff00036e98 l       .text	0000000000000000 $x.455
ffffffff00036e9c l       .text	0000000000000000 $x.456
ffffffff00036ea4 l       .text	0000000000000000 $x.457
ffffffff00036ebc l       .text	0000000000000000 $x.458
ffffffff00036ec0 l       .text	0000000000000000 $x.459
ffffffff00036ec8 l       .text	0000000000000000 $x.460
ffffffff00036ecc l       .text	0000000000000000 $x.461
ffffffff00036ed0 l       .text	0000000000000000 $x.462
ffffffff00036ed4 l       .text	0000000000000000 $x.463
ffffffff00036ed8 l       .text	0000000000000000 $x.464
ffffffff00036edc l       .text	0000000000000000 $x.465
ffffffff00036ef0 l       .text	0000000000000000 $x.466
ffffffff00036f04 l       .text	0000000000000000 $x.467
ffffffff00036f0c l       .text	0000000000000000 $x.468
ffffffff00036f10 l       .text	0000000000000000 $x.469
ffffffff00036f28 l       .text	0000000000000000 $x.470
ffffffff00036f88 l       .text	0000000000000000 $x.471
ffffffff00036fc8 l       .text	0000000000000000 $x.472
ffffffff0003705c l       .text	0000000000000000 $x.473
ffffffff00037074 l       .text	0000000000000000 $x.474
ffffffff00037094 l       .text	0000000000000000 $x.475
ffffffff0016810c l       .bss	0000000000000000 $d.476
ffffffff0015d2d0 l       .data	0000000000000000 $d.477
ffffffff00036f28 l     F .text	0000000000000060 console_stdcall
ffffffff00040c8e l       .rodata	0000000000000000 $d.478
ffffffff0015c0a0 l       .lk_init	0000000000000000 $d.479
ffffffff00036fc8 l     F .text	0000000000000094 console_smcall_init
ffffffff0015d2e8 l       .data	0000000000000000 $d.480
ffffffff00036fcc l     F .text	0000000000000094 platform_after_vm_init
ffffffff00036fd0 l     F .text	0000000000000094 add_app_ranges
ffffffff0015d348 l       .data	0000000000000000 $d.481
ffffffff0015d378 l       .data	0000000000000000 $d.482
ffffffff00168110 l       .bss	0000000000000000 $d.483
ffffffff00168118 l       .bss	0000000000000000 $d.484
ffffffff00168120 l       .bss	0000000000000000 $d.485
ffffffff00168128 l       .bss	0000000000000000 $d.486
ffffffff0015d3c0 l       .data	0000000000000000 $d.487
ffffffff0015d3e0 l       .data	0000000000000000 $d.488
ffffffff00036fd4 l     F .text	0000000000000094 init_caam_env
ffffffff0015d418 l       .data	0000000000000000 $d.489
ffffffff00036e08 l     F .text	0000000000000008 sys_caam_ioctl
ffffffff00036fd8 l     F .text	0000000000000094 platform_init_caam
ffffffff0015d430 l       .data	0000000000000000 $d.490
ffffffff00042b04 l       .rodata	0000000000000000 $d.491
ffffffff00042b28 l       .rodata	0000000000000000 $d.492
ffffffff00042b4c l       .rodata	0000000000000000 $d.493
ffffffff00042b7c l       .rodata	0000000000000000 $d.494
ffffffff00042bd8 l       .rodata	0000000000000000 $d.495
ffffffff00042c18 l       .rodata	0000000000000000 $d.496
ffffffff0015d440 l       .data	0000000000000000 $d.497
ffffffff00036e0c l     F .text	0000000000000008 sys_csu_ioctl
ffffffff00036fdc l     F .text	0000000000000094 platform_init_csu
ffffffff0015d458 l       .data	0000000000000000 $d.498
ffffffff0015d468 l       .data	0000000000000000 $d.499
ffffffff00168129 l       .bss	0000000000000000 $d.500
ffffffff0016812c l       .bss	0000000000000000 $d.501
ffffffff00168130 l       .bss	0000000000000000 $d.502
ffffffff0015d478 l       .data	0000000000000000 $d.503
ffffffff00036f2c l     F .text	0000000000000060 imx_linux_fastcall
ffffffff00036fe0 l     F .text	0000000000000094 imx_linux_smcall_init
ffffffff0015d490 l       .data	0000000000000000 $d.504
ffffffff00036f30 l     F .text	0000000000000060 snvs_fastcall
ffffffff00036fe4 l     F .text	0000000000000094 snvs_smcall_init
ffffffff0015d4a8 l       .data	0000000000000000 $d.505
ffffffff00036f34 l     F .text	0000000000000060 vpu_fastcall
ffffffff00036fe8 l     F .text	0000000000000094 vpu_smcall_init
ffffffff0015d4c0 l       .data	0000000000000000 $d.506
ffffffff0015d4d0 l       .data	0000000000000000 $d.507
ffffffff0015d4f8 l       .data	0000000000000000 $d.508
ffffffff00036f38 l     F .text	0000000000000060 vpu_enc_fastcall
ffffffff00036fec l     F .text	0000000000000094 vpu_enc_smcall_init
ffffffff00168134 l       .bss	0000000000000000 $d.509
ffffffff0015d510 l       .data	0000000000000000 $d.510
ffffffff00168138 l       .bss	0000000000000000 $d.511
ffffffff00168158 l       .bss	0000000000000000 $d.512
ffffffff00036ff0 l     F .text	0000000000000094 arm64_pan_init
ffffffff00168160 l       .bss	0000000000000000 $d.513
ffffffff00168180 l       .bss	0000000000000000 $d.514
ffffffff00036ffc l     F .text	0000000000000094 arm_gic_init_percpu
ffffffff00036ff4 l     F .text	0000000000000094 arm_gic_suspend_cpu
ffffffff00036ff8 l     F .text	0000000000000094 arm_gic_resume_cpu
ffffffff00168188 l       .bss	0000000000000000 $d.515
ffffffff00168190 l       .bss	0000000000000000 $d.516
ffffffff00042c48 l       .rodata	0000000000000000 $d.517
ffffffff00168990 l       .bss	0000000000000000 $d.518
ffffffff00168998 l       .bss	0000000000000000 $d.519
ffffffff0016899c l       .bss	0000000000000000 $d.520
ffffffff001689a0 l       .bss	0000000000000000 $d.521
ffffffff001689a4 l       .bss	0000000000000000 $d.522
ffffffff001689a8 l       .bss	0000000000000000 $d.523
ffffffff001689ac l       .bss	0000000000000000 $d.524
ffffffff001689c0 l       .bss	0000000000000000 $d.525
ffffffff001689c8 l       .bss	0000000000000000 $d.526
ffffffff00037008 l     F .text	0000000000000094 arm_generic_timer_init_secondary_cpu
ffffffff001689d0 l       .bss	0000000000000000 $d.527
ffffffff00037000 l     F .text	0000000000000094 arm_generic_timer_suspend_cpu
ffffffff00037004 l     F .text	0000000000000094 arm_generic_timer_resume_cpu
ffffffff001689e0 l       .bss	0000000000000000 $d.528
ffffffff001689f0 l       .bss	0000000000000000 $d.529
ffffffff00168a00 l       .bss	0000000000000000 $d.530
ffffffff00168a40 l       .bss	0000000000000000 $d.531
ffffffff00168a50 l       .bss	0000000000000000 $d.532
ffffffff00168b50 l       .bss	0000000000000000 $d.533
ffffffff00168b60 l       .bss	0000000000000000 $d.534
ffffffff00168b80 l       .bss	0000000000000000 $d.535
ffffffff00168d80 l       .bss	0000000000000000 $d.536
ffffffff001699c0 l       .bss	0000000000000000 $d.537
ffffffff001699d0 l       .bss	0000000000000000 $d.538
ffffffff001699d8 l       .bss	0000000000000000 $d.539
ffffffff001699e0 l       .bss	0000000000000000 $d.540
ffffffff00169a60 l       .bss	0000000000000000 $d.541
ffffffff00169a70 l       .bss	0000000000000000 $d.542
ffffffff00169bb0 l       .bss	0000000000000000 $d.543
ffffffff00169bc0 l       .bss	0000000000000000 $d.544
ffffffff00169be0 l       .bss	0000000000000000 $d.545
ffffffff00169c00 l       .bss	0000000000000000 $d.546
ffffffff00169c08 l       .bss	0000000000000000 $d.547
ffffffff0015d518 l       .data	0000000000000000 $d.548
ffffffff00036edc l     F .text	0000000000000014 phys_mem_obj_check_flags
ffffffff00036ef0 l     F .text	0000000000000014 phys_mem_obj_get_page
ffffffff00036f10 l     F .text	0000000000000018 phys_mem_obj_destroy
ffffffff0015d530 l       .data	0000000000000000 $d.549
ffffffff00169c10 l       .bss	0000000000000000 $d.550
ffffffff0015d540 l       .data	0000000000000000 $d.551
ffffffff0015d578 l       .data	0000000000000000 $d.552
ffffffff00036ee0 l     F .text	0000000000000014 pmm_vmm_obj_check_flags
ffffffff00036ef4 l     F .text	0000000000000014 pmm_vmm_obj_get_page
ffffffff00036f14 l     F .text	0000000000000018 pmm_vmm_obj_destroy
ffffffff0015d590 l       .data	0000000000000000 $d.553
ffffffff0015d5c8 l       .data	0000000000000000 $d.554
ffffffff0003700c l     F .text	0000000000000094 vm_init_preheap
ffffffff00037010 l     F .text	0000000000000094 vm_init_postheap
ffffffff00169c18 l       .bss	0000000000000000 $d.555
ffffffff0015d668 l       .data	0000000000000000 $d.556
ffffffff0015d670 l       .data	0000000000000000 $d.557
ffffffff0015d680 l       .data	0000000000000000 $d.558
ffffffff0015d6b8 l       .data	0000000000000000 $d.559
ffffffff00036ef8 l     F .text	0000000000000014 vmm_res_obj_get_page
ffffffff00036f18 l     F .text	0000000000000018 vmm_res_obj_destroy
ffffffff0015d6d0 l       .data	0000000000000000 $d.560
ffffffff00169c20 l       .bss	0000000000000000 $d.561
ffffffff00169c28 l       .bss	0000000000000000 $d.562
ffffffff00169c90 l       .bss	0000000000000000 $d.563
ffffffff0015d6d8 l       .data	0000000000000000 $d.564
ffffffff00169c98 l       .bss	0000000000000000 $d.565
ffffffff0015d6e0 l       .data	0000000000000000 $d.566
ffffffff0015d6f0 l     O .data	0000000000000028 console_io_hooks
ffffffff0015d6f0 l       .data	0000000000000000 $d.567
ffffffff00036e2c l     F .text	0000000000000004 __debug_stdio_write
ffffffff00036e34 l     F .text	000000000000000c __debug_stdio_write_commit
ffffffff00036e30 l     F .text	0000000000000004 __debug_stdio_read
ffffffff00036e38 l     F .text	000000000000000c __debug_stdio_lock
ffffffff00036e3c l     F .text	000000000000000c __debug_stdio_unlock
ffffffff0015d718 l       .data	0000000000000000 $d.568
ffffffff0015d728 l       .data	0000000000000000 $d.569
ffffffff0015d730 l       .data	0000000000000000 $d.570
ffffffff0016ac98 l       .bss	0000000000000000 $d.571
ffffffff0015d738 l       .data	0000000000000000 $d.572
ffffffff0016ac9c l       .bss	0000000000000000 $d.573
ffffffff0016aca0 l       .bss	0000000000000000 $d.574
ffffffff00037014 l     F .text	0000000000000094 busy_test_init
ffffffff00037018 l     F .text	0000000000000094 busy_test_cpu_init
ffffffff0015d770 l       .data	0000000000000000 $d.575
ffffffff0016acb8 l       .bss	0000000000000000 $d.576
ffffffff0016acc8 l       .bss	0000000000000000 $d.577
ffffffff0015d7a0 l       .data	0000000000000000 $d.578
ffffffff0016acd0 l       .bss	0000000000000000 $d.579
ffffffff0016acd8 l       .bss	0000000000000000 $d.580
ffffffff0016acd9 l       .bss	0000000000000000 $d.581
ffffffff0016acdc l       .bss	0000000000000000 $d.582
ffffffff0003701c l     F .text	0000000000000094 arm_ffa_init
ffffffff0016ace0 l       .bss	0000000000000000 $d.583
ffffffff0016ace8 l       .bss	0000000000000000 $d.584
ffffffff00043448 l       .rodata	0000000000000000 $d.585
ffffffff00043468 l       .rodata	0000000000000000 $d.586
ffffffff00043480 l       .rodata	0000000000000000 $d.587
ffffffff0015d7d8 l       .data	0000000000000000 $d.588
ffffffff0015d8d8 l     O .data	0000000000000030 ktipctest_blockedsend_node
ffffffff0015d7e8 l     O .data	0000000000000030 ktipctest_connecterr_node
ffffffff0015d7e8 l       .data	0000000000000000 $d.589
ffffffff0015d818 l     O .data	0000000000000030 ktipctest_blockedport_node
ffffffff00036e50 l     F .text	000000000000001c ktipctest_connecterr
ffffffff0015d818 l       .data	0000000000000000 $d.590
ffffffff0015d848 l     O .data	0000000000000030 ktipctest_echo_node
ffffffff00036e54 l     F .text	000000000000001c ktipctest_blockedport
ffffffff0015d848 l       .data	0000000000000000 $d.591
ffffffff0015d878 l     O .data	0000000000000030 ktipctest_echo8_node
ffffffff00036e58 l     F .text	000000000000001c ktipctest_echo
ffffffff0015d878 l       .data	0000000000000000 $d.592
ffffffff0015d8a8 l     O .data	0000000000000030 ktipctest_close_node
ffffffff00036e5c l     F .text	000000000000001c ktipctest_echo8
ffffffff0015d8a8 l       .data	0000000000000000 $d.593
ffffffff00036e60 l     F .text	000000000000001c ktipctest_close
ffffffff0015d8d8 l       .data	0000000000000000 $d.594
ffffffff00036e64 l     F .text	000000000000001c ktipctest_blockedsend
ffffffff00037020 l     F .text	0000000000000094 ktipctest_init
ffffffff0016acf0 l       .bss	0000000000000000 $d.595
ffffffff0016acf8 l       .bss	0000000000000000 $d.596
ffffffff0016ad00 l       .bss	0000000000000000 $d.597
ffffffff0016ad08 l       .bss	0000000000000000 $d.598
ffffffff0016ad10 l       .bss	0000000000000000 $d.599
ffffffff0016ad18 l       .bss	0000000000000000 $d.600
ffffffff0016ad20 l       .bss	0000000000000000 $d.601
ffffffff0016ad28 l       .bss	0000000000000000 $d.602
ffffffff0016ad30 l       .bss	0000000000000000 $d.603
ffffffff0016ad38 l       .bss	0000000000000000 $d.604
ffffffff0015d908 l       .data	0000000000000000 $d.605
ffffffff0015d928 l       .data	0000000000000000 $d.606
ffffffff00036e48 l     F .text	0000000000000004 run_ktipctest
ffffffff0015d9a0 l       .data	0000000000000000 $d.607
ffffffff00037024 l     F .text	0000000000000094 ktipc_test_server_init
ffffffff0015d9b0 l       .data	0000000000000000 $d.608
ffffffff0015d9f8 l       .data	0000000000000000 $d.609
ffffffff0015dac0 l     O .data	0000000000000018 test_srv_port_acl
ffffffff0015da20 l       .data	0000000000000000 $d.610
ffffffff00037060 l     F .text	0000000000000018 test_handle_connect
ffffffff00037078 l     F .text	0000000000000020 test_handle_msg
ffffffff00037098 l     F .text	0000000000000018 test_handle_channel_cleanup
ffffffff0003707c l     F .text	0000000000000020 test_handle_send_unblocked
ffffffff0015da48 l       .data	0000000000000000 $d.611
ffffffff0015db60 l     O .data	0000000000000018 blocked_srv_port_acl
ffffffff0015da70 l       .data	0000000000000000 $d.612
ffffffff0015db80 l     O .data	0000000000000018 connecterr_srv_port_acl
ffffffff0015da98 l       .data	0000000000000000 $d.613
ffffffff0003705c l     F .text	0000000000000018 connecterr_handle_connect
ffffffff00037074 l     F .text	0000000000000020 nop_handle_msg
ffffffff00037094 l     F .text	0000000000000018 nop_handle_channel_cleanup
ffffffff0015dac0 l       .data	0000000000000000 $d.614
ffffffff0015dad8 l     O .data	0000000000000008 valid_uuids
ffffffff0015dad8 l       .data	0000000000000000 $d.615
ffffffff0016ad3c l       .bss	0000000000000000 $d.616
ffffffff0016ad7c l       .bss	0000000000000000 $d.617
ffffffff0015dae0 l       .data	0000000000000000 $d.618
ffffffff0015db60 l       .data	0000000000000000 $d.619
ffffffff0015db78 l     O .data	0000000000000008 bad_uuids
ffffffff0015db78 l       .data	0000000000000000 $d.620
ffffffff0015db80 l       .data	0000000000000000 $d.621
ffffffff00037028 l     F .text	0000000000000094 memlog_init
ffffffff0016ad80 l       .bss	0000000000000000 $d.622
ffffffff0015db98 l       .data	0000000000000000 $d.623
ffffffff0015dba8 l       .data	0000000000000000 $d.624
ffffffff0015dbe0 l       .data	0000000000000000 $d.625
ffffffff0015dc18 l       .data	0000000000000000 $d.626
ffffffff00036e7c l     F .text	000000000000000c hset_poll
ffffffff00036e88 l     F .text	0000000000000010 hset_destroy
ffffffff0015dc48 l       .data	0000000000000000 $d.627
ffffffff00036e80 l     F .text	000000000000000c chan_poll
ffffffff00036e8c l     F .text	0000000000000010 chan_handle_destroy
ffffffff0015dc78 l       .data	0000000000000000 $d.628
ffffffff00036e84 l     F .text	000000000000000c port_poll
ffffffff00036e90 l     F .text	0000000000000010 port_handle_destroy
ffffffff0015dca8 l       .data	0000000000000000 $d.629
ffffffff0015dce0 l       .data	0000000000000000 $d.630
ffffffff0015dcf0 l       .data	0000000000000000 $d.631
ffffffff0015dd00 l       .data	0000000000000000 $d.632
ffffffff00036e94 l     F .text	0000000000000010 memref_handle_destroy
ffffffff00036e98 l     F .text	0000000000000004 memref_mmap
ffffffff0015dd30 l       .data	0000000000000000 $d.633
ffffffff0015dd68 l       .data	0000000000000000 $d.634
ffffffff0015ddb8 l     O .data	0000000000000018 sys_std_fd_op
ffffffff0015ddb8 l       .data	0000000000000000 $d.635
ffffffff00036ea4 l     F .text	0000000000000018 sys_std_writev
ffffffff0003702c l     F .text	0000000000000094 uctx_init
ffffffff0016ad88 l       .bss	0000000000000000 $d.636
ffffffff0015ddd0 l       .data	0000000000000000 $d.637
ffffffff00036eb4 l     F .text	0000000000000018 uctx_handle_readv
ffffffff00036eb8 l     F .text	0000000000000018 uctx_handle_writev
ffffffff0015dde8 l       .data	0000000000000000 $d.638
ffffffff00036e9c l     F .text	0000000000000008 _uctx_startup
ffffffff00036ea0 l     F .text	0000000000000008 _uctx_shutdown
ffffffff0015de10 l       .data	0000000000000000 $d.639
ffffffff0015de48 l       .data	0000000000000000 $d.640
ffffffff0016ad8c l       .bss	0000000000000000 $d.641
ffffffff0015de58 l       .data	0000000000000000 $d.642
ffffffff0016ad90 l       .bss	0000000000000000 $d.643
ffffffff0016ad98 l       .bss	0000000000000000 $d.644
ffffffff0015de68 l       .data	0000000000000000 $d.645
ffffffff00037030 l     F .text	0000000000000094 start_apps
ffffffff00037034 l     F .text	0000000000000094 rctee_init
ffffffff0016ada0 l       .bss	0000000000000000 $d.646
ffffffff0015de78 l       .data	0000000000000000 $d.647
ffffffff00043498 l       .rodata	0000000000000000 $d.648
ffffffff00037038 l     F .text	0000000000000094 rctee_sm_init
ffffffff0015dea8 l       .data	0000000000000000 $d.649
ffffffff0015dee0 l       .data	0000000000000000 $d.650
ffffffff0015def0 l       .data	0000000000000000 $d.651
ffffffff0015df18 l       .data	0000000000000000 $d.652
ffffffff00036ec8 l     F .text	0000000000000004 rcipc_descr_size
ffffffff00036ecc l     F .text	0000000000000004 rcipc_get_vdev_descr
ffffffff00036ed0 l     F .text	0000000000000004 rcipc_vdev_probe
ffffffff00036ed4 l     F .text	0000000000000004 rcipc_vdev_reset
ffffffff00036ed8 l     F .text	0000000000000004 rcipc_vdev_kick_vq
ffffffff0015df40 l       .data	0000000000000000 $d.653
ffffffff00036eec l     F .text	0000000000000014 rcipc_ext_mem_check_flags
ffffffff00036f00 l     F .text	0000000000000014 rcipc_ext_mem_get_page
ffffffff00036f1c l     F .text	0000000000000018 rcipc_ext_mem_destroy
ffffffff0016ada8 l       .bss	0000000000000000 $d.654
ffffffff0016ae20 l       .bss	0000000000000000 $d.655
ffffffff0015df58 l       .data	0000000000000000 $d.656
ffffffff00036f04 l     F .text	0000000000000008 rcipc_tx_vq_notify_cb
ffffffff00036f08 l     F .text	0000000000000008 rcipc_rx_vq_notify_cb
ffffffff0016ae28 l       .bss	0000000000000000 $d.657
ffffffff0016ae30 l       .bss	0000000000000000 $d.658
ffffffff0015df68 l       .data	0000000000000000 $d.659
ffffffff000434b4 l       .rodata	0000000000000000 $d.660
ffffffff0003703c l     F .text	0000000000000094 register_rcipc_init
ffffffff0015df78 l       .data	0000000000000000 $d.661
ffffffff00036f0c l     F .text	0000000000000004 rcipc_init
ffffffff000434c4 l       .rodata	0000000000000000 $d.662
ffffffff0015df90 l       .data	0000000000000000 $d.663
ffffffff0016ae38 l       .bss	0000000000000000 $d.664
ffffffff0015e000 l       .data	0000000000000000 $d.665
ffffffff0016ae40 l       .bss	0000000000000000 $d.666
ffffffff0016ae44 l       .bss	0000000000000000 $d.667
ffffffff00037044 l     F .text	0000000000000094 sm_init
ffffffff0016ae48 l       .bss	0000000000000000 $d.668
ffffffff0016ae50 l       .bss	0000000000000000 $d.669
ffffffff0016af10 l       .bss	0000000000000000 $d.670
ffffffff0016af18 l       .bss	0000000000000000 $d.671
ffffffff0015e008 l       .data	0000000000000000 $d.672
ffffffff0016af38 l       .bss	0000000000000000 $d.673
ffffffff0016af40 l       .bss	0000000000000000 $d.674
ffffffff0016af48 l       .bss	0000000000000000 $d.675
ffffffff0016af50 l       .bss	0000000000000000 $d.676
ffffffff0016af58 l       .bss	0000000000000000 $d.677
ffffffff0016af60 l       .bss	0000000000000000 $d.678
ffffffff00037040 l     F .text	0000000000000094 sm_release_boot_args
ffffffff0016af68 l       .bss	0000000000000000 $d.679
ffffffff0016af88 l       .bss	0000000000000000 $d.680
ffffffff0015e040 l       .data	0000000000000000 $d.681
ffffffff00036f6c l     F .text	0000000000000060 smc_fastcall_secure_monitor
ffffffff0015e240 l       .data	0000000000000000 $d.682
ffffffff00036f68 l     F .text	0000000000000060 smc_nop_secure_monitor
ffffffff0015e440 l       .data	0000000000000000 $d.683
ffffffff00036f5c l     F .text	0000000000000060 smc_stdcall_secure_monitor
ffffffff0015e640 l       .data	0000000000000000 $d.684
ffffffff0015e678 l       .data	0000000000000000 $d.685
ffffffff00036f3c l     F .text	0000000000000060 smc_intc_get_next_irq
ffffffff00036f70 l     F .text	0000000000000060 smc_fiq_enter
ffffffff00036f74 l     F .text	0000000000000060 smc_cpu_suspend
ffffffff00036f78 l     F .text	0000000000000060 smc_cpu_resume
ffffffff00036f7c l     F .text	0000000000000060 smc_get_version_str
ffffffff00036f50 l     F .text	0000000000000060 smc_sm_api_version
ffffffff00036f54 l     F .text	0000000000000060 smc_get_smp_max_cpus
ffffffff0015e6e8 l       .data	0000000000000000 $d.686
ffffffff00036f60 l     F .text	0000000000000060 smc_restart_stdcall
ffffffff00036f64 l     F .text	0000000000000060 smc_nop_stdcall
ffffffff00036f80 l     F .text	0000000000000060 smc_trusty_sched_share_register
ffffffff00036f84 l     F .text	0000000000000060 smc_trusty_sched_share_unregister
ffffffff00037048 l     F .text	0000000000000094 shared_mem_init
ffffffff0015e718 l       .data	0000000000000000 $d.687
ffffffff00036ee8 l     F .text	0000000000000014 ext_mem_obj_check_flags
ffffffff00036efc l     F .text	0000000000000014 ext_mem_obj_get_page
ffffffff00036f24 l     F .text	0000000000000018 sm_mem_obj_destroy
ffffffff0015e730 l       .data	0000000000000000 $d.688
ffffffff00036f20 l     F .text	0000000000000018 sm_mem_obj_compat_destroy
ffffffff0016af90 l       .bss	0000000000000000 $d.689
ffffffff0016af98 l       .bss	0000000000000000 $d.690
ffffffff0016afa0 l       .bss	0000000000000000 $d.691
ffffffff0015e748 l       .data	0000000000000000 $d.692
ffffffff00036ea8 l     F .text	0000000000000018 sys_writev
ffffffff00036eac l     F .text	0000000000000018 sys_readv
ffffffff00036eb0 l     F .text	0000000000000018 sys_wait
ffffffff0015e958 l       .data	0000000000000000 $d.693
ffffffff0015e960 l       .data	0000000000000000 $d.694
ffffffff0016afc0 l       .bss	0000000000000000 $d.695
ffffffff0016afc8 l       .bss	0000000000000000 $d.696
ffffffff0016afd0 l       .bss	0000000000000000 $d.697
ffffffff0015e998 l       .data	0000000000000000 $d.698
ffffffff0003704c l     F .text	0000000000000094 apploader_service_init
ffffffff0015e9c8 l       .data	0000000000000000 $d.699
ffffffff0015ea10 l       .data	0000000000000000 $d.700
ffffffff0015ea60 l     O .data	0000000000000018 apploader_service_port_acl
ffffffff0015ea38 l       .data	0000000000000000 $d.701
ffffffff00037064 l     F .text	0000000000000018 apploader_service_handle_connect
ffffffff00037080 l     F .text	0000000000000020 apploader_service_handle_msg
ffffffff0003709c l     F .text	0000000000000018 apploader_service_handle_channel_cleanup
ffffffff0015ea60 l       .data	0000000000000000 $d.702
ffffffff0015ea78 l     O .data	0000000000000008 apploader_service_uuids
ffffffff0015ea78 l       .data	0000000000000000 $d.703
ffffffff00043530 l     O .rodata	0000000000000010 apploader_user_uuid
ffffffff00043530 l       .rodata	0000000000000000 $d.704
ffffffff0016afd8 l       .bss	0000000000000000 $d.705
ffffffff00037050 l     F .text	0000000000000094 generic_ta_service_init
ffffffff0015ea80 l       .data	0000000000000000 $d.706
ffffffff0015eac8 l       .data	0000000000000000 $d.707
ffffffff00043540 l     O .rodata	0000000000000018 generic_ta_service_port_acl
ffffffff0015eaf0 l       .data	0000000000000000 $d.708
ffffffff00037068 l     F .text	0000000000000018 generic_ta_service_handle_connect
ffffffff00037084 l     F .text	0000000000000020 generic_ta_service_handle_msg
ffffffff000370a0 l     F .text	0000000000000018 generic_ta_service_handle_channel_cleanup
ffffffff00043540 l       .rodata	0000000000000000 $d.709
ffffffff0016b070 l       .bss	0000000000000000 $d.710
ffffffff00037054 l     F .text	0000000000000094 hwrng_ktipc_server_init
ffffffff0015eb18 l       .data	0000000000000000 $d.711
ffffffff0015eb60 l       .data	0000000000000000 $d.712
ffffffff00043558 l     O .rodata	0000000000000018 hwrng_srv_port_acl
ffffffff0015eb88 l       .data	0000000000000000 $d.713
ffffffff0003706c l     F .text	0000000000000018 hwrng_handle_connect
ffffffff00037088 l     F .text	0000000000000020 hwrng_handle_msg
ffffffff000370a4 l     F .text	0000000000000018 hwrng_handle_channel_cleanup
ffffffff0003708c l     F .text	0000000000000020 hwrng_handle_send_unblocked
ffffffff00043558 l       .rodata	0000000000000000 $d.714
ffffffff0015ebb0 l       .data	0000000000000000 $d.715
ffffffff0016b080 l       .bss	0000000000000000 $d.716
ffffffff0016b088 l       .bss	0000000000000000 $d.717
ffffffff0016b108 l       .bss	0000000000000000 $d.718
ffffffff00037058 l     F .text	0000000000000094 smc_service_init
ffffffff0015ebc0 l       .data	0000000000000000 $d.719
ffffffff0015ec08 l       .data	0000000000000000 $d.720
ffffffff00043580 l     O .rodata	0000000000000018 smc_service_port_acl
ffffffff0015ec30 l       .data	0000000000000000 $d.721
ffffffff00037070 l     F .text	0000000000000018 smc_service_handle_connect
ffffffff00037090 l     F .text	0000000000000020 smc_service_handle_msg
ffffffff000370a8 l     F .text	0000000000000018 smc_service_handle_channel_cleanup
ffffffff00043570 l       .rodata	0000000000000000 $d.722
ffffffff00043580 l       .rodata	0000000000000000 $d.723
ffffffff00043598 l       .rodata	0000000000000000 $d.724
00000000000041aa l       .debug_loclists	0000000000000000 $d.725
0000000000004ac3 l       .debug_abbrev	0000000000000000 $d.726
0000000000008028 l       .debug_info	0000000000000000 $d.727
00000000000005bc l       .debug_rnglists	0000000000000000 $d.728
000000000004d323 l       .debug_macro	0000000000000000 $d.729
000000000004cd7c l       .debug_str_offsets	0000000000000000 $d.730
00000000000194b4 l       .debug_str	0000000000000000 $d.731
0000000000000ec8 l       .debug_addr	0000000000000000 $d.732
0000000000000000 l       *ABS*	0000000000000000 $d.733
0000000000000000 l       *ABS*	0000000000000000 $d.734
0000000000000000 l       *ABS*	0000000000000000 $d.735
0000000000000000 l       *ABS*	0000000000000000 $d.736
0000000000000000 l       *ABS*	0000000000000000 $d.737
0000000000000000 l       *ABS*	0000000000000000 $d.738
0000000000000000 l       *ABS*	0000000000000000 $d.739
0000000000000000 l       *ABS*	0000000000000000 $d.740
0000000000000000 l       *ABS*	0000000000000000 $d.741
0000000000000000 l       *ABS*	0000000000000000 $d.742
0000000000000000 l       *ABS*	0000000000000000 $d.743
0000000000000000 l       *ABS*	0000000000000000 $d.744
0000000000000000 l       *ABS*	0000000000000000 $d.745
0000000000000000 l       *ABS*	0000000000000000 $d.746
0000000000000000 l       *ABS*	0000000000000000 $d.747
0000000000000000 l       *ABS*	0000000000000000 $d.748
0000000000000000 l       *ABS*	0000000000000000 $d.749
0000000000000000 l       *ABS*	0000000000000000 $d.750
0000000000000000 l       *ABS*	0000000000000000 $d.751
0000000000000000 l       *ABS*	0000000000000000 $d.752
0000000000000000 l       *ABS*	0000000000000000 $d.753
0000000000000000 l       *ABS*	0000000000000000 $d.754
0000000000000000 l       *ABS*	0000000000000000 $d.755
0000000000000000 l       *ABS*	0000000000000000 $d.756
0000000000000000 l       *ABS*	0000000000000000 $d.757
0000000000000000 l       *ABS*	0000000000000000 $d.758
0000000000000000 l       *ABS*	0000000000000000 $d.759
0000000000000000 l       *ABS*	0000000000000000 $d.760
0000000000000000 l       *ABS*	0000000000000000 $d.761
0000000000000000 l       *ABS*	0000000000000000 $d.762
0000000000000000 l       *ABS*	0000000000000000 $d.763
0000000000000000 l       *ABS*	0000000000000000 $d.764
0000000000000000 l       *ABS*	0000000000000000 $d.765
0000000000000000 l       *ABS*	0000000000000000 $d.766
0000000000000000 l       *ABS*	0000000000000000 $d.767
0000000000000000 l       *ABS*	0000000000000000 $d.768
0000000000000000 l       *ABS*	0000000000000000 $d.769
0000000000000000 l       *ABS*	0000000000000000 $d.770
0000000000000000 l       *ABS*	0000000000000000 $d.771
0000000000000000 l       *ABS*	0000000000000000 $d.772
0000000000000000 l       *ABS*	0000000000000000 $d.773
0000000000000000 l       *ABS*	0000000000000000 $d.774
0000000000000000 l       *ABS*	0000000000000000 $d.775
0000000000000000 l       *ABS*	0000000000000000 $d.776
0000000000000000 l       *ABS*	0000000000000000 $d.777
0000000000000000 l       *ABS*	0000000000000000 $d.778
0000000000000000 l       *ABS*	0000000000000000 $d.779
0000000000000000 l       *ABS*	0000000000000000 $d.780
0000000000000000 l       *ABS*	0000000000000000 $d.781
0000000000000000 l       *ABS*	0000000000000000 $d.782
0000000000000000 l       *ABS*	0000000000000000 $d.783
0000000000000000 l       *ABS*	0000000000000000 $d.784
0000000000000000 l       *ABS*	0000000000000000 $d.785
0000000000000000 l       *ABS*	0000000000000000 $d.786
0000000000000000 l       *ABS*	0000000000000000 $d.787
0000000000000000 l       *ABS*	0000000000000000 $d.788
0000000000000000 l       *ABS*	0000000000000000 $d.789
0000000000000000 l       *ABS*	0000000000000000 $d.790
0000000000000000 l       *ABS*	0000000000000000 $d.791
0000000000000000 l       *ABS*	0000000000000000 $d.792
0000000000000000 l       *ABS*	0000000000000000 $d.793
0000000000000000 l       *ABS*	0000000000000000 $d.794
0000000000000000 l       *ABS*	0000000000000000 $d.795
0000000000000000 l       *ABS*	0000000000000000 $d.796
0000000000000000 l       *ABS*	0000000000000000 $d.797
0000000000000000 l       *ABS*	0000000000000000 $d.798
0000000000000000 l       *ABS*	0000000000000000 $d.799
0000000000000000 l       *ABS*	0000000000000000 $d.800
0000000000000000 l       *ABS*	0000000000000000 $d.801
0000000000000000 l       *ABS*	0000000000000000 $d.802
0000000000000000 l       *ABS*	0000000000000000 $d.803
0000000000000000 l       *ABS*	0000000000000000 $d.804
0000000000000000 l       *ABS*	0000000000000000 $d.805
0000000000000000 l       *ABS*	0000000000000000 $d.806
0000000000000000 l       *ABS*	0000000000000000 $d.807
0000000000000000 l       *ABS*	0000000000000000 $d.808
0000000000000000 l       *ABS*	0000000000000000 $d.809
0000000000000000 l       *ABS*	0000000000000000 $d.810
0000000000000000 l       *ABS*	0000000000000000 $d.811
0000000000000000 l       *ABS*	0000000000000000 $d.812
0000000000000000 l       *ABS*	0000000000000000 $d.813
0000000000000000 l       *ABS*	0000000000000000 $d.814
0000000000000000 l       *ABS*	0000000000000000 $d.815
0000000000000000 l       *ABS*	0000000000000000 $d.816
0000000000000000 l       *ABS*	0000000000000000 $d.817
0000000000000000 l       *ABS*	0000000000000000 $d.818
0000000000000000 l       *ABS*	0000000000000000 $d.819
0000000000000000 l       *ABS*	0000000000000000 $d.820
0000000000000000 l       *ABS*	0000000000000000 $d.821
0000000000000000 l       *ABS*	0000000000000000 $d.822
0000000000000000 l       *ABS*	0000000000000000 $d.823
0000000000000000 l       *ABS*	0000000000000000 $d.824
0000000000000000 l       *ABS*	0000000000000000 $d.825
0000000000000000 l       *ABS*	0000000000000000 $d.826
0000000000000000 l       *ABS*	0000000000000000 $d.827
0000000000000000 l       *ABS*	0000000000000000 $d.828
0000000000000000 l       *ABS*	0000000000000000 $d.829
0000000000000000 l       *ABS*	0000000000000000 $d.830
0000000000000000 l       *ABS*	0000000000000000 $d.831
0000000000000000 l       *ABS*	0000000000000000 $d.832
0000000000000000 l       *ABS*	0000000000000000 $d.833
0000000000000000 l       *ABS*	0000000000000000 $d.834
0000000000000000 l       *ABS*	0000000000000000 $d.835
0000000000000000 l       *ABS*	0000000000000000 $d.836
0000000000000000 l       *ABS*	0000000000000000 $d.837
0000000000000000 l       *ABS*	0000000000000000 $d.838
0000000000000000 l       *ABS*	0000000000000000 $d.839
0000000000000000 l       *ABS*	0000000000000000 $d.840
0000000000000000 l       *ABS*	0000000000000000 $d.841
0000000000000000 l       *ABS*	0000000000000000 $d.842
0000000000000000 l       *ABS*	0000000000000000 $d.843
0000000000000000 l       *ABS*	0000000000000000 $d.844
0000000000000000 l       *ABS*	0000000000000000 $d.845
0000000000000000 l       *ABS*	0000000000000000 $d.846
0000000000000000 l       *ABS*	0000000000000000 $d.847
0000000000000000 l       *ABS*	0000000000000000 $d.848
000000000000c8d3 l       .debug_line	0000000000000000 $d.849
00000000000009ae l       .debug_line_str	0000000000000000 $d.850
ffffffff000176c0 l     F .text	0000000000000240 .hidden _panic
ffffffff0001795c l     F .text	0000000000000004 .hidden free
ffffffff00017900 l     F .text	000000000000000c .hidden malloc
ffffffff00006d80 l     F .text	0000000000000538 .hidden arm64_secondary_entry
ffffffff000072b8 l     F .text	0000000000000568 .hidden arm64_sync_exception
ffffffff000079c0 l     F .text	0000000000000044 .hidden arm64_invalid_exception
ffffffff00168158 l     O .bss	0000000000000001 .hidden arm64_mte_enabled
ffffffff000080c4 l     F .text	0000000000000460 .hidden arm64_early_mmu_init
ffffffff00168160 l     O .bss	0000000000000020 .hidden arm64_kernel_translation_table
ffffffff0000a1b4 l     F .text	0000000000000160 .hidden platform_irq
ffffffff0000ff10 l     F .text	0000000000000008 .hidden thread_preempt
ffffffff00018708 l     F .text	0000000000000a98 .hidden lk_main
ffffffff00017910 l     F .text	000000000000004c .hidden calloc
ffffffff0015d718 l     O .data	0000000000000010 .hidden console_io
ffffffff000184ec l     F .text	0000000000000084 .hidden io_write
ffffffff00018570 l     F .text	0000000000000088 .hidden io_write_commit
ffffffff000185f8 l     F .text	0000000000000088 .hidden io_lock
ffffffff00018680 l     F .text	0000000000000088 .hidden io_unlock
ffffffff00034890 l     F .text	0000000000000048 .hidden sys_undefined
ffffffff0015e748 l     O .data	0000000000000210 .hidden syscall_table
ffffffff0015e958 l     O .data	0000000000000008 .hidden nr_syscalls
ffffffff00036e04 l     F .text	0000000000000001 .hidden __typeid__ZTSFijE_global_addr
ffffffff00036e08 l     F .text	0000000000000001 .hidden __typeid__ZTSFijjmE_global_addr
ffffffff00036e10 l     F .text	0000000000000001 .hidden __typeid__ZTSF14handler_returnPvE_global_addr
ffffffff00036e1c l     F .text	0000000000000001 .hidden __typeid__ZTSF14handler_returnP5timeryPvE_global_addr
ffffffff00036e28 l     F .text	0000000000000001 .hidden __typeid__ZTSF14handler_returnPvyE_global_addr
ffffffff00036e2c l     F .text	0000000000000001 .hidden __typeid__ZTSFlP9io_handlePKcmE_global_addr
ffffffff00036e30 l     F .text	0000000000000001 .hidden __typeid__ZTSFlP9io_handlePcmE_global_addr
ffffffff00036e34 l     F .text	0000000000000001 .hidden __typeid__ZTSFvP9io_handleE_global_addr
ffffffff00036e40 l     F .text	0000000000000001 .hidden __typeid__ZTSFvP12ktipc_serverP18ksrv_event_handlerjE_global_addr
ffffffff00036e48 l     F .text	0000000000000001 .hidden __typeid__ZTSFbP8unittestE_global_addr
ffffffff00036e4c l     F .text	0000000000000001 .hidden __typeid__ZTSFvvE_global_addr
ffffffff00036e68 l     F .text	0000000000000001 .hidden __typeid__ZTSFvP16__print_callbackPKcmE_global_addr
ffffffff00036e6c l     F .text	0000000000000001 .hidden __typeid__ZTSFvP16__print_callbackE_global_addr
ffffffff00036e70 l     F .text	0000000000000001 .hidden __typeid__ZTSFvP13handle_waiterE_global_addr
ffffffff00036e7c l     F .text	0000000000000001 .hidden __typeid__ZTSFjP6handlejbE_global_addr
ffffffff00036e88 l     F .text	0000000000000001 .hidden __typeid__ZTSFvP6handleE_global_addr
ffffffff00036e98 l     F .text	0000000000000001 .hidden __typeid__ZTSFiP6handlemmjPmE_global_addr
ffffffff00036e9c l     F .text	0000000000000001 .hidden __typeid__ZTSFiP9rctee_appE_global_addr
ffffffff00036ea4 l     F .text	0000000000000001 .hidden __typeid__ZTSFljmjE_global_addr
ffffffff00036ebc l     F .text	0000000000000001 .hidden __typeid__ZTSFvP12phys_mem_objE_global_addr
ffffffff00036ec0 l     F .text	0000000000000001 .hidden __typeid__ZTSFiPhmPvE_global_addr
ffffffff00036ec8 l     F .text	0000000000000001 .hidden __typeid__ZTSFmP4vdevE_global_addr
ffffffff00036ecc l     F .text	0000000000000001 .hidden __typeid__ZTSFlP4vdevPvE_global_addr
ffffffff00036ed0 l     F .text	0000000000000001 .hidden __typeid__ZTSFiP4vdevPvE_global_addr
ffffffff00036ed4 l     F .text	0000000000000001 .hidden __typeid__ZTSFiP4vdevE_global_addr
ffffffff00036ed8 l     F .text	0000000000000001 .hidden __typeid__ZTSFiP4vdevjE_global_addr
ffffffff00036edc l     F .text	0000000000000001 .hidden __typeid__ZTSFiP7vmm_objPjE_global_addr
ffffffff00036ef0 l     F .text	0000000000000001 .hidden __typeid__ZTSFiP7vmm_objmPmS1_E_global_addr
ffffffff00036f04 l     F .text	0000000000000001 .hidden __typeid__ZTSFiP6vqueuePvE_global_addr
ffffffff00036f0c l     F .text	0000000000000001 .hidden __typeid__ZTSFiP16rctee_virtio_busE_global_addr
ffffffff00036f10 l     F .text	0000000000000001 .hidden __typeid__ZTSFvP7vmm_objE_global_addr
ffffffff00036f28 l     F .text	0000000000000001 .hidden __typeid__ZTSFlP10smc32_argsE_global_addr
ffffffff00036f88 l     F .text	0000000000000001 .hidden __typeid__ZTSFiPvE_global_addr
ffffffff00036fc8 l     F .text	0000000000000001 .hidden __typeid__ZTSFvjE_global_addr
ffffffff0003705c l     F .text	0000000000000001 .hidden __typeid__ZTSFiPK10ktipc_portP6handlePK4uuidPPvE_global_addr
ffffffff00037074 l     F .text	0000000000000001 .hidden __typeid__ZTSFiPK10ktipc_portP6handlePvE_global_addr
ffffffff00037094 l     F .text	0000000000000001 .hidden __typeid__ZTSFvPvE_global_addr
ffffffff0015c4b8 l       .dynamic	0000000000000000 .hidden _DYNAMIC
ffffffff0015c0a0 g     O .lk_init	0000000000000018 _init_struct_uart_console
ffffffff0015c0b8 g     O .lk_init	0000000000000018 _init_struct_platform_after_vm
ffffffff0015c0d0 g     O .lk_init	0000000000000018 _init_struct_allowed_app_ranges
ffffffff0015c0e8 g     O .lk_init	0000000000000018 _init_struct_imx_caam
ffffffff0015c100 g     O .lk_init	0000000000000018 _init_struct_caam_dev_init
ffffffff00001a94 g     F .text	0000000000000000 arch_clean_cache_range
ffffffff00001ab4 g     F .text	0000000000000000 arch_clean_invalidate_cache_range
ffffffff00001b50 g     F .text	0000000000000000 arch_copy_from_user
ffffffff00001b30 g     F .text	0000000000000000 arch_copy_to_user
ffffffff0015c118 g     O .lk_init	0000000000000018 _init_struct_csu_dev_init
ffffffff0015c130 g     O .lk_init	0000000000000018 _init_struct_imx_linux_driver
ffffffff0015c148 g     O .lk_init	0000000000000018 _init_struct_snvs_driver
ffffffff0015c160 g     O .lk_init	0000000000000018 _init_struct_vpu_driver
ffffffff0015c178 g     O .lk_init	0000000000000018 _init_struct_vpu_enc_driver
ffffffff0015c0a0 g       .apps	0000000000000000 __apps_start
ffffffff0015c0a0 g       .apps	0000000000000000 __apps_end
ffffffff000018a8 g     F .text	0000000000000000 arm64_enter_uspace
ffffffff00001994 g     F .text	0000000000000000 arm64_el3_to_el1
ffffffff00001a8c g     F .text	0000000000000000 arch_spin_unlock
ffffffff000002e8  w    F .text	0000000000000000 arm64_curr_cpu_num
ffffffff00001a6c g     F .text	0000000000000000 arch_spin_lock
ffffffff00001000 g     F .text	0000000000000000 arm64_exception_base
ffffffff00038000 g       .rodata	0000000000000000 __fault_handler_table_start
ffffffff00038060 g       .rodata	0000000000000000 __fault_handler_table_end
ffffffff00001934 g     F .text	0000000000000000 arm64_context_switch
ffffffff00163000 g     O .bss	0000000000000000 __stack_end
ffffffff00163000 g     O .bss	0000000000000000 __shadow_stack
ffffffff0015c190 g     O .lk_init	0000000000000018 _init_struct_arm64_pan_init
ffffffff000019d8 g     F .text	0000000000000000 arm64_elX_to_el1
ffffffff0000321c g     F .text	0000000000000000 platform_early_halt
ffffffff00001a54 g     F .text	0000000000000000 arch_spin_trylock
ffffffff00000000 g       .text	0000000000000000 arm_reset
ffffffff00000000 g     O .text	0000000000000000 _start
ffffffff00167800 g     O .bss	0000000000000000 tt_trampoline
ffffffff0015d008 g     O .data	0000000000000000 tt_trampoline_not_ready
ffffffff00168000 g       .bss	0000000000000000 __post_prebss_bss_start
ffffffff0016b110 g       .bss	0000000000000000 __bss_end
ffffffff0015c418 g       .relr.dyn	0000000000000000 __relr_start
ffffffff0015c4b8 g       .relr.dyn	0000000000000000 __relr_end
ffffffff0015d00c g     O .data	0000000000000000 page_tables_not_ready
ffffffff00167000 g     O .bss	0000000000000000 sp_el1_bufs
ffffffff0015f000 g     O .bss	0000000000000000 __stack
ffffffff00001ad4 g     F .text	0000000000000000 arch_invalidate_cache_range
ffffffff00001af4 g     F .text	0000000000000000 arch_sync_cache_range
ffffffff00001b70 g     F .text	0000000000000000 arch_strlcpy_from_user
ffffffff00001bb4 g     F .text	0000000000000000 copy_from_anywhere
ffffffff00001bf0 g     F .text	0000000000000000 tag_for_addr_
ffffffff0015ec58 g       .devices	0000000000000000 __devices
ffffffff0015ec58 g       .devices	0000000000000000 __devices_end
ffffffff0015c1a8 g     O .lk_init	0000000000000018 _init_struct_arm_gic_init_percpu
ffffffff0015c1c0 g     O .lk_init	0000000000000018 _init_struct_arm_gic_suspend_cpu
ffffffff0015c1d8 g     O .lk_init	0000000000000018 _init_struct_arm_gic_resume_cpu
ffffffff0015c1f0 g     O .lk_init	0000000000000018 _init_struct_arm_generic_timer_init_secondary_cpu
ffffffff0015c208 g     O .lk_init	0000000000000018 _init_struct_arm_generic_timer_suspend_cpu
ffffffff0015c220 g     O .lk_init	0000000000000018 _init_struct_arm_generic_timer_resume_cpu
ffffffff0016c000 g       .bss	0000000000000000 _end
ffffffff0015c238 g     O .lk_init	0000000000000018 _init_struct_vm_preheap
ffffffff0015c250 g     O .lk_init	0000000000000018 _init_struct_vm
ffffffff00000000 g       .text	0000000000000000 __code_start
ffffffff00038000 g       .rodata	0000000000000000 __rodata_start
ffffffff0015d000 g       .data	0000000000000000 __data_start
ffffffff0015c0a0 g       .lk_init	0000000000000000 __lk_init
ffffffff0015c418 g       .lk_init	0000000000000000 __lk_init_end
ffffffff0015c4b8 g       .ctors	0000000000000000 __ctor_list
ffffffff0015c4b8 g       .ctors	0000000000000000 __ctor_end
ffffffff0015c268 g     O .lk_init	0000000000000018 _init_struct_busy_test_init
ffffffff0015c280 g     O .lk_init	0000000000000018 _init_struct_busy_test_cpu_init
ffffffff0015c298 g     O .lk_init	0000000000000018 _init_struct_arm_ffa_init
ffffffff00003240 g     F .text	0000000000000000 smc8
ffffffff0015c2b0 g     O .lk_init	0000000000000018 _init_struct_ktipctest
ffffffff0015c2c8 g     O .lk_init	0000000000000018 _init_struct_ktipc_test_server_init
ffffffff0015c2e0 g     O .lk_init	0000000000000018 _init_struct_memlog
ffffffff0015c2f8 g     O .lk_init	0000000000000018 _init_struct_uctx
ffffffff0015c310 g     O .lk_init	0000000000000018 _init_struct_librctee_apps
ffffffff0015c328 g     O .lk_init	0000000000000018 _init_struct_librctee
ffffffff0015c000 g       .__rctee_app_list	0000000000000000 __rctee_app_list_start
ffffffff0015c0a0 g       .__rctee_app_list	0000000000000000 __rctee_app_list_end
ffffffff0015c340 g     O .lk_init	0000000000000018 _init_struct_rctee_smcall
ffffffff0015c358 g     O .lk_init	0000000000000018 _init_struct_register_rcipc_init
ffffffff0015c370 g     O .lk_init	0000000000000018 _init_struct_libsm
ffffffff0015c388 g     O .lk_init	0000000000000018 _init_struct_libsm_bootargs
ffffffff0015c3a0 g     O .lk_init	0000000000000018 _init_struct_shared_mem
ffffffff000031f0 g     F .text	0000000000000000 sm_sched_nonsecure
ffffffff0015c3b8 g     O .lk_init	0000000000000018 _init_struct_apploader
ffffffff0015c3d0 g     O .lk_init	0000000000000018 _init_struct_generic_ta
ffffffff0015c3e8 g     O .lk_init	0000000000000018 _init_struct_hwrng_ktipc_server_init
ffffffff0015c400 g     O .lk_init	0000000000000018 _init_struct_smc
0000000000000000         *UND*	0000000000000000 __system_property_get
0000000000000000         *UND*	0000000000000000 getauxval
ffffffff0003715c g       .gnu.hash	0000000000000000 __exidx_start
ffffffff0003715c g       .gnu.hash	0000000000000000 __exidx_end
ffffffff0003715c g       .fake_post_text	0000000000000000 __code_end
ffffffff0015c4b8 g       .dtors	0000000000000000 __dtor_list
ffffffff0015c4b8 g       .dtors	0000000000000000 __dtor_end
ffffffff0015c600 g       .fake_post_rodata	0000000000000000 __rodata_end
ffffffff0015d000 g       .data	0000000000000000 __data_start_rom
ffffffff0015ec58 g       .fake_post_data	0000000000000000 __data_end
ffffffff0015f000 g       .bss	0000000000000000 __bss_start
ffffffff02000000 g       .bss	0000000000000000 _end_of_ram
ffffffff0015c0a0 g       .drivers	0000000000000000 __drivers
ffffffff0015c0a0 g       .drivers	0000000000000000 __drivers_end
