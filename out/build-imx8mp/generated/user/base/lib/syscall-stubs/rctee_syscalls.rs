/*
 * Copyright (c) LK RCTEE Authors. All Rights Reserved.
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files
 * (the "Software"), to deal in the Software without restriction,
 * including without limitation the rights to use, copy, modify, merge,
 * publish, distribute, sublicense, and/or sell copies of the Software,
 * and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
 * IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
 * CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
 * TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
 * SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

/* This file is auto-generated. !!! DO NOT EDIT !!! */

extern "C" {
    pub fn _rctee_writev(fd: uint32_t, iov: *const iovec, iovcnt: uint32_t) -> long;
    pub fn _rctee_brk(brk: *mut void) -> *mut void;
    pub fn _rctee_exit_etc(status: int32_t, flags: uint32_t) -> long;
    pub fn _rctee_readv(fd: uint32_t, iov: *const iovec, iovcnt: uint32_t) -> long;
    pub fn _rctee_ioctl(fd: uint32_t, req: uint32_t, buf: *mut void) -> long;
    pub fn _rctee_nanosleep(clock_id: uint32_t, flags: uint32_t, sleep_time: uint64_t) -> long;
    pub fn _rctee_gettime(clock_id: uint32_t, flags: uint32_t, time: *mut int64_t) -> long;
    pub fn _rctee_mmap(uaddr: *mut void, size: uint32_t, flags: uint32_t, handle: int32_t) -> long;
    pub fn _rctee_munmap(uaddr: *mut void, size: uint32_t) -> long;
    pub fn _rctee_prepare_dma(uaddr: *mut void, size: uint32_t, flags: uint32_t, pmem: *mut dma_pmem) -> long;
    pub fn _rctee_finish_dma(uaddr: *mut void, size: uint32_t, flags: uint32_t) -> long;
    pub fn _rctee_set_user_tls(uaddr: *mut void) -> long;
    pub fn _rctee_dup(handle: int32_t) -> long;
    pub fn _rctee_port_create(path: *const char, num_recv_bufs: uint32_t, recv_buf_size: uint32_t, flags: uint32_t) -> long;
    pub fn _rctee_connect(path: *const char, flags: uint32_t) -> long;
    pub fn _rctee_accept(handle: int32_t, peer_uuid: *mut uuid) -> long;
    pub fn _rctee_close(handle: int32_t) -> long;
    pub fn _rctee_set_cookie(handle: int32_t, cookie: *mut void) -> long;
    pub fn _rctee_handle_set_create() -> long;
    pub fn _rctee_handle_set_ctrl(handle: int32_t, cmd: uint32_t, evt: *mut uevent) -> long;
    pub fn _rctee_wait(handle: int32_t, event: *mut uevent, timeout_msecs: uint32_t) -> long;
    pub fn _rctee_wait_any(event: *mut uevent, timeout_msecs: uint32_t) -> long;
    pub fn _rctee_get_msg(handle: int32_t, msg_info: *mut ipc_msg_info) -> long;
    pub fn _rctee_read_msg(handle: int32_t, msg_id: uint32_t, offset: uint32_t, msg: *mut ipc_msg) -> long;
    pub fn _rctee_put_msg(handle: int32_t, msg_id: uint32_t) -> long;
    pub fn _rctee_send_msg(handle: int32_t, msg: *mut ipc_msg) -> long;
    pub fn _rctee_memref_create(uaddr: *mut void, size: uint32_t, mmap_prot: uint32_t) -> long;
    pub fn _rctee_dump_memory_info(dump_type: int32_t) -> long;
}
