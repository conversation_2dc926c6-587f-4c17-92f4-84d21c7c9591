/*
 * Copyright (c) LK RCTEE Authors. All Rights Reserved.
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files
 * (the "Software"), to deal in the Software without restriction,
 * including without limitation the rights to use, copy, modify, merge,
 * publish, distribute, sublicense, and/or sell copies of the Software,
 * and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
 * IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
 * CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
 * TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
 * SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

/* This file is auto-generated. !!! DO NOT EDIT !!! */

#include <lk/asm.h>
#include <rctee_syscalls.h>

.section .text._rctee_writev
.balign 4
FUNCTION(_rctee_writev)
    mov     x12, #__NR_writev
    svc     #0
    ret
.size _rctee_writev,.-_rctee_writev

.section .text._rctee_brk
.balign 4
FUNCTION(_rctee_brk)
    mov     x12, #__NR_brk
    svc     #0
    ret
.size _rctee_brk,.-_rctee_brk

.section .text._rctee_exit_etc
.balign 4
FUNCTION(_rctee_exit_etc)
    mov     x12, #__NR_exit_etc
    svc     #0
    ret
.size _rctee_exit_etc,.-_rctee_exit_etc

.section .text._rctee_readv
.balign 4
FUNCTION(_rctee_readv)
    mov     x12, #__NR_readv
    svc     #0
    ret
.size _rctee_readv,.-_rctee_readv

.section .text._rctee_ioctl
.balign 4
FUNCTION(_rctee_ioctl)
    mov     x12, #__NR_ioctl
    svc     #0
    ret
.size _rctee_ioctl,.-_rctee_ioctl

.section .text._rctee_nanosleep
.balign 4
FUNCTION(_rctee_nanosleep)
    mov     x12, #__NR_nanosleep
    svc     #0
    ret
.size _rctee_nanosleep,.-_rctee_nanosleep

.section .text._rctee_gettime
.balign 4
FUNCTION(_rctee_gettime)
    mov     x12, #__NR_gettime
    svc     #0
    ret
.size _rctee_gettime,.-_rctee_gettime

.section .text._rctee_mmap
.balign 4
FUNCTION(_rctee_mmap)
    mov     x12, #__NR_mmap
    svc     #0
    ret
.size _rctee_mmap,.-_rctee_mmap

.section .text._rctee_munmap
.balign 4
FUNCTION(_rctee_munmap)
    mov     x12, #__NR_munmap
    svc     #0
    ret
.size _rctee_munmap,.-_rctee_munmap

.section .text._rctee_prepare_dma
.balign 4
FUNCTION(_rctee_prepare_dma)
    mov     x12, #__NR_prepare_dma
    svc     #0
    ret
.size _rctee_prepare_dma,.-_rctee_prepare_dma

.section .text._rctee_finish_dma
.balign 4
FUNCTION(_rctee_finish_dma)
    mov     x12, #__NR_finish_dma
    svc     #0
    ret
.size _rctee_finish_dma,.-_rctee_finish_dma

.section .text._rctee_set_user_tls
.balign 4
FUNCTION(_rctee_set_user_tls)
    mov     x12, #__NR_set_user_tls
    svc     #0
    ret
.size _rctee_set_user_tls,.-_rctee_set_user_tls

.section .text._rctee_dup
.balign 4
FUNCTION(_rctee_dup)
    mov     x12, #__NR_dup
    svc     #0
    ret
.size _rctee_dup,.-_rctee_dup

.section .text._rctee_port_create
.balign 4
FUNCTION(_rctee_port_create)
    mov     x12, #__NR_port_create
    svc     #0
    ret
.size _rctee_port_create,.-_rctee_port_create

.section .text._rctee_connect
.balign 4
FUNCTION(_rctee_connect)
    mov     x12, #__NR_connect
    svc     #0
    ret
.size _rctee_connect,.-_rctee_connect

.section .text._rctee_accept
.balign 4
FUNCTION(_rctee_accept)
    mov     x12, #__NR_accept
    svc     #0
    ret
.size _rctee_accept,.-_rctee_accept

.section .text._rctee_close
.balign 4
FUNCTION(_rctee_close)
    mov     x12, #__NR_close
    svc     #0
    ret
.size _rctee_close,.-_rctee_close

.section .text._rctee_set_cookie
.balign 4
FUNCTION(_rctee_set_cookie)
    mov     x12, #__NR_set_cookie
    svc     #0
    ret
.size _rctee_set_cookie,.-_rctee_set_cookie

.section .text._rctee_handle_set_create
.balign 4
FUNCTION(_rctee_handle_set_create)
    mov     x12, #__NR_handle_set_create
    svc     #0
    ret
.size _rctee_handle_set_create,.-_rctee_handle_set_create

.section .text._rctee_handle_set_ctrl
.balign 4
FUNCTION(_rctee_handle_set_ctrl)
    mov     x12, #__NR_handle_set_ctrl
    svc     #0
    ret
.size _rctee_handle_set_ctrl,.-_rctee_handle_set_ctrl

.section .text._rctee_wait
.balign 4
FUNCTION(_rctee_wait)
    mov     x12, #__NR_wait
    svc     #0
    ret
.size _rctee_wait,.-_rctee_wait

.section .text._rctee_wait_any
.balign 4
FUNCTION(_rctee_wait_any)
    mov     x12, #__NR_wait_any
    svc     #0
    ret
.size _rctee_wait_any,.-_rctee_wait_any

.section .text._rctee_get_msg
.balign 4
FUNCTION(_rctee_get_msg)
    mov     x12, #__NR_get_msg
    svc     #0
    ret
.size _rctee_get_msg,.-_rctee_get_msg

.section .text._rctee_read_msg
.balign 4
FUNCTION(_rctee_read_msg)
    mov     x12, #__NR_read_msg
    svc     #0
    ret
.size _rctee_read_msg,.-_rctee_read_msg

.section .text._rctee_put_msg
.balign 4
FUNCTION(_rctee_put_msg)
    mov     x12, #__NR_put_msg
    svc     #0
    ret
.size _rctee_put_msg,.-_rctee_put_msg

.section .text._rctee_send_msg
.balign 4
FUNCTION(_rctee_send_msg)
    mov     x12, #__NR_send_msg
    svc     #0
    ret
.size _rctee_send_msg,.-_rctee_send_msg

.section .text._rctee_memref_create
.balign 4
FUNCTION(_rctee_memref_create)
    mov     x12, #__NR_memref_create
    svc     #0
    ret
.size _rctee_memref_create,.-_rctee_memref_create

.section .text._rctee_dump_memory_info
.balign 4
FUNCTION(_rctee_dump_memory_info)
    mov     x12, #__NR_dump_memory_info
    svc     #0
    ret
.size _rctee_dump_memory_info,.-_rctee_dump_memory_info

SECTION_GNU_NOTE_PROPERTY_AARCH64_FEATURES(GNU_NOTE_FEATURE_AARCH64_BTI)
