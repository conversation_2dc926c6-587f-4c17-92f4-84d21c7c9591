/*
 * Copyright (C) 2024 The Tongsuo Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <stddef.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <rctee_user_ipc.h>
#include <uapi/err.h>
#include <libutee.h>
#include <tee_api_types.h>
#include <tee_api_defines.h>
#include <tee_ta_api.h>

#define TLOG_LVL TLOG_LVL_INFO
#include <trusty_log.h>

#define TLOG_TAG "tongsuo_crypto_test"

// Command IDs
#define CMD_TEST_BASIC_CRYPTO       1

static int test_basic_crypto(void)
{
    TLOGI("Testing basic crypto functionality with Tongsuo...");

    // Simple test - just verify we can link with Tongsuo
    TLOGI("Tongsuo crypto library linked successfully!");

    return TEE_SUCCESS;
}

/* RCTEE回调函数实现 */
int RCTEE_OnCall(uint32_t cmd,
                 uint8_t* in_buf,
                 size_t in_buf_size,
                 uint8_t** out_buf,
                 size_t* out_buf_size) {
    (void)in_buf;
    (void)in_buf_size;
    (void)out_buf;
    (void)out_buf_size;

    TLOGI("Tongsuo Crypto Test TA - Command ID: %u", cmd);

    switch (cmd) {
    case CMD_TEST_BASIC_CRYPTO:
        return test_basic_crypto();

    default:
        TLOGE("Unknown command ID: %u", cmd);
        return ERROR_INVALID;
    }
}

int RCTEE_OnConnect(void) {
    TLOGI("Tongsuo Crypto Test TA - Client connected");
    return TEE_SUCCESS;
}

void RCTEE_OnDisConnect(void* cookie) {
    (void)cookie;
    TLOGI("Tongsuo Crypto Test TA - Client disconnected");
}

int RCTEE_OnInit(void) {
    TLOGI("Tongsuo Crypto Test TA - Initialized");
    return TEE_SUCCESS;
}
