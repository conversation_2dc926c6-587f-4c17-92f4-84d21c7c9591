/*
 * Copyright (C) 2024 The Tongsuo Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <stddef.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <tee_api_types.h>
#include <tee_api_defines.h>
#include <tee_ta_api.h>
#include <libutee.h>

#define TLOG_LVL TLOG_LVL_INFO
#include <trusty_log.h>

#define TLOG_TAG "tongsuo_crypto_test"

// Command IDs
#define CMD_TEST_BASIC_CRYPTO       1

static TEE_Result test_basic_crypto(void)
{
    TLOGI("Testing basic crypto functionality with Tongsuo...");

    // Simple test - just verify we can link with Tongsuo
    TLOGI("Tongsuo crypto library linked successfully!");

    return TEE_SUCCESS;
}

TEE_Result TA_InvokeCommandEntryPoint(void __unused *sess_ctx,
                                      uint32_t cmd_id,
                                      uint32_t param_types,
                                      TEE_Param params[4])
{
    (void)&sess_ctx;
    (void)&param_types;
    (void)&params;
    
    TLOGI("Tongsuo Crypto Test TA - Command ID: %u", cmd_id);
    
    switch (cmd_id) {
    case CMD_TEST_BASIC_CRYPTO:
        return test_basic_crypto();

    default:
        TLOGE("Unknown command ID: %u", cmd_id);
        return TEE_ERROR_NOT_SUPPORTED;
    }
}
