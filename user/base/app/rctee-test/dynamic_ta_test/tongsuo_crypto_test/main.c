/*
 * Copyright (C) 2024 The Tongsuo Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <tee_api_types.h>
#include <tee_api_defines.h>
#include <tee_ta_api.h>
#include <libutee.h>

#define TLOG_LVL TLOG_LVL_INFO
#include <trusty_log.h>

#define TLOG_TAG "tongsuo_crypto_test"

// Include Tongsuo headers
#include <openssl/evp.h>
#include <openssl/aes.h>
#include <openssl/rand.h>
#include <openssl/sm3.h>
#include <openssl/sm4.h>
#include <openssl/bn.h>

// Command IDs
#define CMD_TEST_AES_ENCRYPT        1
#define CMD_TEST_SM3_HASH          2
#define CMD_TEST_SM4_ENCRYPT       3
#define CMD_TEST_RANDOM_GENERATE   4
#define CMD_TEST_BIGNUM_OPS        5

static TEE_Result test_aes_encrypt(void)
{
    TLOGI("Testing AES encryption with Tongsuo...");
    
    unsigned char key[16] = {0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
                             0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f};
    unsigned char plaintext[16] = "Hello Tongsuo!!!";
    unsigned char ciphertext[16];
    unsigned char decrypted[16];
    
    AES_KEY aes_key;
    
    // Set encryption key
    if (AES_set_encrypt_key(key, 128, &aes_key) != 0) {
        TLOGE("AES_set_encrypt_key failed");
        return TEE_ERROR_GENERIC;
    }
    
    // Encrypt
    AES_encrypt(plaintext, ciphertext, &aes_key);
    
    // Set decryption key
    if (AES_set_decrypt_key(key, 128, &aes_key) != 0) {
        TLOGE("AES_set_decrypt_key failed");
        return TEE_ERROR_GENERIC;
    }
    
    // Decrypt
    AES_decrypt(ciphertext, decrypted, &aes_key);
    
    // Verify
    if (memcmp(plaintext, decrypted, 16) == 0) {
        TLOGI("AES test PASSED");
        return TEE_SUCCESS;
    } else {
        TLOGE("AES test FAILED");
        return TEE_ERROR_GENERIC;
    }
}

static TEE_Result test_sm3_hash(void)
{
    TLOGI("Testing SM3 hash with Tongsuo...");
    
    const char *message = "Hello SM3 from Tongsuo!";
    unsigned char hash[SM3_DIGEST_LENGTH];
    
    SM3_CTX ctx;
    
    if (!SM3_Init(&ctx)) {
        TLOGE("SM3_Init failed");
        return TEE_ERROR_GENERIC;
    }
    
    if (!SM3_Update(&ctx, message, strlen(message))) {
        TLOGE("SM3_Update failed");
        return TEE_ERROR_GENERIC;
    }
    
    if (!SM3_Final(hash, &ctx)) {
        TLOGE("SM3_Final failed");
        return TEE_ERROR_GENERIC;
    }
    
    TLOGI("SM3 hash computed successfully");
    TLOGI("Hash: ");
    for (int i = 0; i < SM3_DIGEST_LENGTH; i++) {
        printf("%02x", hash[i]);
    }
    printf("\n");
    
    return TEE_SUCCESS;
}

static TEE_Result test_sm4_encrypt(void)
{
    TLOGI("Testing SM4 encryption with Tongsuo...");
    
    unsigned char key[16] = {0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef,
                             0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10};
    unsigned char plaintext[16] = {0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef,
                                   0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10};
    unsigned char ciphertext[16];
    unsigned char decrypted[16];
    
    SM4_KEY sm4_key;
    
    // Set encryption key
    if (SM4_set_key(key, &sm4_key) != 0) {
        TLOGE("SM4_set_key failed");
        return TEE_ERROR_GENERIC;
    }
    
    // Encrypt
    SM4_encrypt(plaintext, ciphertext, &sm4_key);
    
    // Decrypt
    SM4_decrypt(ciphertext, decrypted, &sm4_key);
    
    // Verify
    if (memcmp(plaintext, decrypted, 16) == 0) {
        TLOGI("SM4 test PASSED");
        return TEE_SUCCESS;
    } else {
        TLOGE("SM4 test FAILED");
        return TEE_ERROR_GENERIC;
    }
}

static TEE_Result test_random_generate(void)
{
    TLOGI("Testing random number generation with Tongsuo...");
    
    unsigned char random_bytes[32];
    
    if (RAND_bytes(random_bytes, sizeof(random_bytes)) != 1) {
        TLOGE("RAND_bytes failed");
        return TEE_ERROR_GENERIC;
    }
    
    TLOGI("Random bytes generated successfully:");
    for (int i = 0; i < 32; i++) {
        printf("%02x", random_bytes[i]);
    }
    printf("\n");
    
    return TEE_SUCCESS;
}

static TEE_Result test_bignum_ops(void)
{
    TLOGI("Testing big number operations with Tongsuo...");
    
    BIGNUM *a = BN_new();
    BIGNUM *b = BN_new();
    BIGNUM *result = BN_new();
    BN_CTX *ctx = BN_CTX_new();
    
    if (!a || !b || !result || !ctx) {
        TLOGE("Failed to allocate BIGNUM structures");
        goto cleanup;
    }
    
    // Set a = 123456789
    if (!BN_set_word(a, 123456789)) {
        TLOGE("BN_set_word failed for a");
        goto cleanup;
    }
    
    // Set b = 987654321
    if (!BN_set_word(b, 987654321)) {
        TLOGE("BN_set_word failed for b");
        goto cleanup;
    }
    
    // Calculate result = a + b
    if (!BN_add(result, a, b)) {
        TLOGE("BN_add failed");
        goto cleanup;
    }
    
    char *result_str = BN_bn2dec(result);
    if (result_str) {
        TLOGI("BigNum addition result: %s", result_str);
        OPENSSL_free(result_str);
    }
    
    TLOGI("BigNum test PASSED");
    
cleanup:
    if (a) BN_free(a);
    if (b) BN_free(b);
    if (result) BN_free(result);
    if (ctx) BN_CTX_free(ctx);
    
    return TEE_SUCCESS;
}

TEE_Result TA_InvokeCommandEntryPoint(void __unused *sess_ctx,
                                      uint32_t cmd_id,
                                      uint32_t param_types,
                                      TEE_Param params[4])
{
    (void)&sess_ctx;
    (void)&param_types;
    (void)&params;
    
    TLOGI("Tongsuo Crypto Test TA - Command ID: %u", cmd_id);
    
    switch (cmd_id) {
    case CMD_TEST_AES_ENCRYPT:
        return test_aes_encrypt();
        
    case CMD_TEST_SM3_HASH:
        return test_sm3_hash();
        
    case CMD_TEST_SM4_ENCRYPT:
        return test_sm4_encrypt();
        
    case CMD_TEST_RANDOM_GENERATE:
        return test_random_generate();
        
    case CMD_TEST_BIGNUM_OPS:
        return test_bignum_ops();
        
    default:
        TLOGE("Unknown command ID: %u", cmd_id);
        return TEE_ERROR_NOT_SUPPORTED;
    }
}
