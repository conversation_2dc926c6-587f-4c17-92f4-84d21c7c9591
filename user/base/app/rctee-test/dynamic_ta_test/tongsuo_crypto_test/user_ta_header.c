/*
 * Copyright (C) 2024 The Tongsuo Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <stdint.h>
#include <tee_ta_api.h>
#include "user_ta_header_defines.h"

TEE_Result TA_CreateEntryPoint(void)
{
    return TEE_SUCCESS;
}

void TA_DestroyEntryPoint(void)
{
}

TEE_Result TA_OpenSessionEntryPoint(uint32_t param_types,
                                    TEE_Param __unused params[4],
                                    void __unused **sess_ctx)
{
    (void)&param_types;
    (void)&params;
    (void)&sess_ctx;
    return TEE_SUCCESS;
}

void TA_CloseSessionEntryPoint(void __unused *sess_ctx)
{
    (void)&sess_ctx;
}

TEE_Result TA_InvokeCommandEntryPoint(void __unused *sess_ctx,
                                      uint32_t cmd_id,
                                      uint32_t param_types,
                                      TEE_Param params[4]);

const ta_head_t ta_head = {
    .uuid = TA_UUID,
    .name = "tongsuo_crypto_test",
    .flags = TA_FLAGS,
    .stack_size = TA_STACK_SIZE,
    .heap_size = TA_DATA_SIZE,
    .prop = (user_ta_property_t[]){
        TA_CURRENT_TA_EXT_PROPERTIES
    },
    .prop_count = sizeof((user_ta_property_t[]){
        TA_CURRENT_TA_EXT_PROPERTIES
    }) / sizeof(user_ta_property_t),
    .create = TA_CreateEntryPoint,
    .destroy = TA_DestroyEntryPoint,
    .open_session = TA_OpenSessionEntryPoint,
    .close_session = TA_CloseSessionEntryPoint,
    .invoke_command = TA_InvokeCommandEntryPoint,
};
